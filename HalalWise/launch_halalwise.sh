#!/bin/bash

# HalalWise Complete Launch Script
echo "🕌 HalalWise - Halal Food Verification App"
echo "=========================================="
echo ""

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check prerequisites
echo "🔍 Checking prerequisites..."

# Check Flutter
if ! command -v flutter &> /dev/null; then
    print_error "Flutter is not installed"
    echo "   Please install Flutter: https://docs.flutter.dev/get-started/install"
    exit 1
fi
print_status "Flutter $(flutter --version | head -n1 | cut -d' ' -f2) detected"

# Check Node.js
if ! command -v node &> /dev/null; then
    print_warning "Node.js not found - backend features will be disabled"
    BACKEND_AVAILABLE=false
else
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_warning "Node.js version 18+ recommended. Current: $(node -v)"
    else
        print_status "Node.js $(node -v) detected"
    fi
    BACKEND_AVAILABLE=true
fi

echo ""

# Ask user for launch preference
echo "🚀 Choose launch option:"
echo "1. Flutter App Only (Quick Preview)"
echo "2. Full Stack (App + Backend Server)"
echo "3. Backend Server Only"
echo ""
read -p "Enter your choice (1-3): " choice

case $choice in
    1)
        echo ""
        print_info "Launching Flutter app only..."
        
        # Install Flutter dependencies
        echo "📦 Installing Flutter dependencies..."
        flutter pub get
        
        if [ $? -ne 0 ]; then
            print_error "Failed to install Flutter dependencies"
            exit 1
        fi
        print_status "Dependencies installed"
        
        # Check for devices
        echo ""
        echo "📱 Available devices:"
        flutter devices
        
        echo ""
        print_info "Starting HalalWise app..."
        print_info "🌐 Launching on Chrome for best preview experience"
        print_info "📱 Use 'flutter run' to see device options"
        
        # Launch on Chrome
        flutter run -d chrome
        ;;
        
    2)
        echo ""
        print_info "Launching full stack (App + Backend)..."
        
        if [ "$BACKEND_AVAILABLE" = false ]; then
            print_error "Node.js not available - cannot start backend"
            exit 1
        fi
        
        # Start backend in background
        echo "🖥️  Starting backend server..."
        cd backend
        
        if [ ! -d "node_modules" ]; then
            echo "📦 Installing backend dependencies..."
            npm install
            if [ $? -ne 0 ]; then
                print_error "Failed to install backend dependencies"
                exit 1
            fi
        fi
        
        # Start backend server in background
        npm start &
        BACKEND_PID=$!
        cd ..
        
        # Wait for backend to start
        echo "⏳ Waiting for backend to start..."
        sleep 3
        
        # Check if backend is running
        if curl -s http://localhost:3000/api/health > /dev/null; then
            print_status "Backend server running on http://localhost:3000"
        else
            print_warning "Backend may not be fully ready yet"
        fi
        
        # Install Flutter dependencies
        echo "📦 Installing Flutter dependencies..."
        flutter pub get
        
        if [ $? -ne 0 ]; then
            print_error "Failed to install Flutter dependencies"
            # Kill backend process
            kill $BACKEND_PID 2>/dev/null
            exit 1
        fi
        
        print_status "All dependencies installed"
        
        echo ""
        print_info "🌐 Backend API: http://localhost:3000"
        print_info "📱 Flutter App: Starting now..."
        print_info "🛑 Press Ctrl+C to stop both services"
        
        # Function to cleanup on exit
        cleanup() {
            echo ""
            print_info "Stopping services..."
            kill $BACKEND_PID 2>/dev/null
            print_status "Services stopped"
            exit 0
        }
        
        # Set trap to cleanup on script exit
        trap cleanup SIGINT SIGTERM
        
        # Start Flutter app
        flutter run -d chrome
        
        # Cleanup when Flutter app exits
        cleanup
        ;;
        
    3)
        echo ""
        print_info "Starting backend server only..."
        
        if [ "$BACKEND_AVAILABLE" = false ]; then
            print_error "Node.js not available"
            exit 1
        fi
        
        cd backend
        
        if [ ! -d "node_modules" ]; then
            echo "📦 Installing dependencies..."
            npm install
            if [ $? -ne 0 ]; then
                print_error "Failed to install dependencies"
                exit 1
            fi
        fi
        
        print_status "Starting backend server..."
        print_info "🌐 API will be available at: http://localhost:3000"
        print_info "📊 Health check: http://localhost:3000/api/health"
        print_info "🛑 Press Ctrl+C to stop"
        
        npm start
        ;;
        
    *)
        print_error "Invalid choice. Please run the script again."
        exit 1
        ;;
esac
