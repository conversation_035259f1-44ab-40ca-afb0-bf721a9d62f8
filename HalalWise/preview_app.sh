#!/bin/bash

# HalalWise App Preview Script
echo "📱 HalalWise App Preview Setup"
echo "================================"

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter is not installed."
    echo "   Please install Flutter first: https://docs.flutter.dev/get-started/install"
    exit 1
fi

echo "✅ Flutter $(flutter --version | head -n1 | cut -d' ' -f2) detected"

# Run Flutter doctor
echo "🔍 Checking Flutter setup..."
flutter doctor

# Check if there are any connected devices
echo ""
echo "📱 Checking for connected devices..."
flutter devices

# Install dependencies
echo ""
echo "📦 Installing Flutter dependencies..."
flutter pub get

if [ $? -ne 0 ]; then
    echo "❌ Failed to install Flutter dependencies"
    exit 1
fi

echo "✅ Dependencies installed successfully"

# Check for available devices
DEVICES=$(flutter devices --machine | jq -r '.[].id' 2>/dev/null)

if [ -z "$DEVICES" ]; then
    echo ""
    echo "⚠️  No devices detected. Please:"
    echo "   1. Connect an Android device via USB (enable USB debugging)"
    echo "   2. Start an Android emulator"
    echo "   3. Start iOS simulator (macOS only)"
    echo "   4. Or run on Chrome: flutter run -d chrome"
    echo ""
    echo "🌐 Starting on Chrome web browser..."
    flutter run -d chrome
else
    echo ""
    echo "🚀 Starting HalalWise app..."
    echo "   Available devices:"
    flutter devices
    echo ""
    echo "   Starting on default device..."
    flutter run
fi
