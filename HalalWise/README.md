# HalalWise

An elegant, modern, and user-friendly iOS and Android application designed to verify the halal and health status of food items using AI.

## Features

### 🔍 Input Methods
- **Barcode Scanning**: Scan product barcodes using device camera
- **Label Scanning**: Upload or scan food packaging images with OCR
- **Manual Input**: Type food names or ingredient lists
- **Voice Input**: Voice-based input for accessibility

### ✅ Halal Status Verification
- Comprehensive database lookup
- AI analysis for unknown items
- Certification display with explanations
- Intelligent caching to minimize AI usage

### 🏥 Health Status Analysis
- Nutritional analysis and health scores
- Allergen alerts and warnings
- Customizable health preferences

### 🎨 UI/UX Features
- Elegant and modern design
- Cultural sensitivity with Arabic typography
- Right-to-left (RTL) support
- Full accessibility compliance
- Offline support with caching

## Technical Stack

- **Framework**: Flutter 3.10+
- **State Management**: Provider
- **Database**: SQLite (local) + Firebase Firestore (cloud)
- **AI Integration**: Grok API for text analysis
- **OCR**: Google ML Kit Text Recognition
- **Barcode Scanning**: Mobile Scanner
- **Architecture**: Clean Architecture with Repository Pattern

## Getting Started

### Prerequisites

1. **Flutter SDK**: Install Flutter 3.10 or later
   ```bash
   flutter --version
   ```

2. **Development Environment**:
   - Android Studio or VS Code
   - Xcode (for iOS development)
   - Android SDK and iOS SDK

### Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd HalalWise
   ```

2. **Install dependencies**:
   ```bash
   flutter pub get
   ```

3. **Configure Firebase**:
   - Create a new Firebase project
   - Add Android and iOS apps to your Firebase project
   - Download `google-services.json` (Android) and `GoogleService-Info.plist` (iOS)
   - Place them in the appropriate directories

4. **Configure API Keys**:
   - Get Grok API key from xAI
   - Update `lib/core/network/ai_service.dart` with your API key
   - Configure Google Vision API if using cloud OCR

5. **Run the app**:
   ```bash
   # For Android
   flutter run

   # For iOS
   flutter run -d ios
   ```

## Project Structure

```
lib/
├── core/                     # Core functionality
│   ├── app_config.dart      # App configuration
│   ├── database/            # Database helpers
│   ├── network/             # API services
│   ├── services/            # Service locator
│   └── theme/               # App theming
├── features/                # Feature modules
│   ├── auth/               # Authentication
│   ├── food_analysis/      # Food analysis logic
│   ├── history/            # Scan history
│   ├── scanner/            # Barcode/label scanning
│   └── settings/           # App settings
└── main.dart               # App entry point
```

## Configuration

### Database Setup
The app uses SQLite for local storage and Firebase Firestore for cloud sync. The database is automatically initialized on first run with:
- Halal certification bodies
- Common non-halal ingredients
- Questionable ingredients requiring verification

### AI Configuration
Update the following in `lib/core/app_config.dart`:
- `maxTokensPerRequest`: Maximum tokens per AI request
- `confidenceThreshold`: Minimum confidence for AI results

### Localization
The app supports multiple languages:
- English (en)
- Arabic (ar)
- Malay (ms)
- Urdu (ur)
- Turkish (tr)
- Indonesian (id)

## API Integration

### Open Food Facts
The app integrates with Open Food Facts API for product information:
- No API key required
- Automatic fallback for unknown products

### Grok AI API
For AI-powered analysis:
1. Sign up for xAI Grok API
2. Get your API key
3. Update `lib/core/network/ai_service.dart`

### Google Vision API (Optional)
For enhanced OCR capabilities:
1. Enable Google Vision API in Google Cloud Console
2. Configure authentication
3. Update OCR implementation

## Building for Production

### Android
```bash
flutter build apk --release
# or for app bundle
flutter build appbundle --release
```

### iOS
```bash
flutter build ios --release
```

## Testing

Run tests with:
```bash
flutter test
```

## Performance Optimization

The app is optimized for:
- **Minimal AI Usage**: Database-first approach with intelligent caching
- **Fast Scanning**: Optimized camera and OCR processing
- **Offline Support**: Local database with sync capabilities
- **Memory Efficiency**: Proper image handling and caching

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Contact the development team

## Roadmap

- [ ] Enhanced AI models for better accuracy
- [ ] Community reporting features
- [ ] Restaurant menu scanning
- [ ] Nutritionist recommendations
- [ ] Social sharing features
- [ ] Offline AI models

---

**Note**: This app is designed to assist in halal verification but should not be the sole source for religious dietary decisions. Always consult with qualified religious authorities when in doubt.
