<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/c.h</key>
		<data>
		dHX1OPayTiQI9GAKLdxmXlJAIhE=
		</data>
		<key>Headers/cache.h</key>
		<data>
		BjUSKnePi2fnzkBFOQGvcwoF+8o=
		</data>
		<key>Headers/comparator.h</key>
		<data>
		l8I3Jv0Dg8InVuoD3KaFG6EU+ho=
		</data>
		<key>Headers/db.h</key>
		<data>
		hxgteUlEAxpXUhVXS63hEbX7c64=
		</data>
		<key>Headers/dumpfile.h</key>
		<data>
		Gag1I6uZqv9sE5TnPTZBbdC9HGk=
		</data>
		<key>Headers/env.h</key>
		<data>
		e+0gt/+kJP1AYbh8KceJG7XPp/A=
		</data>
		<key>Headers/export.h</key>
		<data>
		wwQLm0Ra6BsSjeE72QUmkBDGwXQ=
		</data>
		<key>Headers/filter_policy.h</key>
		<data>
		wdRGEa2Acn9kGRklg6hmawVfOyQ=
		</data>
		<key>Headers/iterator.h</key>
		<data>
		K6uT2VNgNBzca967KvHOh5A/Wi4=
		</data>
		<key>Headers/leveldb-library-umbrella.h</key>
		<data>
		V7Nfypo9KLkG3n3ipLO6O4H44Rs=
		</data>
		<key>Headers/options.h</key>
		<data>
		MXKm+dLnQOmKhipOOdxp8fr15ys=
		</data>
		<key>Headers/slice.h</key>
		<data>
		E2EuU1f5LCNCTvw/r6Ktf8mh71o=
		</data>
		<key>Headers/status.h</key>
		<data>
		uUnGrKyrIkl6V40EzRInR7WJgzY=
		</data>
		<key>Headers/table.h</key>
		<data>
		OPqQ977VANoSJ4KROLCNCj3WB1w=
		</data>
		<key>Headers/table_builder.h</key>
		<data>
		oDgJKRRzu2HSjjZP2eHlzt0FYHQ=
		</data>
		<key>Headers/write_batch.h</key>
		<data>
		URr3FlmtuqQZzYV2mYEQXT6EgH8=
		</data>
		<key>Info.plist</key>
		<data>
		a9kMK9OTbP52bEqWLA1PuR/SrUU=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		XiHCOtoll/P1GzSxgvvpGO9wfas=
		</data>
		<key>leveldb_Privacy.bundle/Info.plist</key>
		<data>
		Gb30e3tLNx73oQsYx+Vc4mcGYKI=
		</data>
		<key>leveldb_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		JTGYQTSkuJ7uGfD1SfjQTq3ngyk=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/c.h</key>
		<dict>
			<key>hash2</key>
			<data>
			WzoNQxkPrkCs/iTJhhlJwkw3kVdrBzWdx266iWehizU=
			</data>
		</dict>
		<key>Headers/cache.h</key>
		<dict>
			<key>hash2</key>
			<data>
			oBtG11nJvbcfJv1w7XJe1ifwDKrHayKgksMydEXcfTI=
			</data>
		</dict>
		<key>Headers/comparator.h</key>
		<dict>
			<key>hash2</key>
			<data>
			xo2njPPuQ8p5eDiyyj4b8e5t2j1XLb+uTkjYR0hrrhY=
			</data>
		</dict>
		<key>Headers/db.h</key>
		<dict>
			<key>hash2</key>
			<data>
			r/tfOONcN6Jh8jX4jjseCfAuRIlPytXypnqzZgw/stw=
			</data>
		</dict>
		<key>Headers/dumpfile.h</key>
		<dict>
			<key>hash2</key>
			<data>
			d9QrAvheAfMr0hmsViCEyloTuNHEvcI7/FP7GXhjkQ0=
			</data>
		</dict>
		<key>Headers/env.h</key>
		<dict>
			<key>hash2</key>
			<data>
			dnlSO4SoMBDhgQzSGFvwg6Qzr8+HUnTWelqDKJYpb48=
			</data>
		</dict>
		<key>Headers/export.h</key>
		<dict>
			<key>hash2</key>
			<data>
			FtEHJacSFWiRPmuZTFjGoSJ/RZcTBlqGQMTY8TDLWvc=
			</data>
		</dict>
		<key>Headers/filter_policy.h</key>
		<dict>
			<key>hash2</key>
			<data>
			apBtWX4vqzRXDUsaIUUjo1QiAkMAVZB56+awqxD+W8s=
			</data>
		</dict>
		<key>Headers/iterator.h</key>
		<dict>
			<key>hash2</key>
			<data>
			5tU9PS2uXLi/2FnABEylJ+pwuj5dlf1i+y8oJkT+m5s=
			</data>
		</dict>
		<key>Headers/leveldb-library-umbrella.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ADJx+iWg0amEoMNEjBIeg+4KC+0jIUuoGHcKP0koQto=
			</data>
		</dict>
		<key>Headers/options.h</key>
		<dict>
			<key>hash2</key>
			<data>
			4hVzvRpjnVuZFtqUEZXrScjXhbDEtfo3JcVzsUUO4mE=
			</data>
		</dict>
		<key>Headers/slice.h</key>
		<dict>
			<key>hash2</key>
			<data>
			lB4FSHXSXsABrYRC8B5GsINd0W3+vklc6H974ngmv4Q=
			</data>
		</dict>
		<key>Headers/status.h</key>
		<dict>
			<key>hash2</key>
			<data>
			liDe189T5c1JEcicABLnEk7m3nibOFjgKxE82u54cRQ=
			</data>
		</dict>
		<key>Headers/table.h</key>
		<dict>
			<key>hash2</key>
			<data>
			q/wM2xwwomjqcTnOZVRJYROcjJ2sSc8wnF+d9NRymfI=
			</data>
		</dict>
		<key>Headers/table_builder.h</key>
		<dict>
			<key>hash2</key>
			<data>
			XJ86/zSDT8KGp5hP8LT+UpLhnGoxxQgyue8lIRaPvN0=
			</data>
		</dict>
		<key>Headers/write_batch.h</key>
		<dict>
			<key>hash2</key>
			<data>
			CuHR+yYgo/+3aqm0MwvUooP5yS6CmvrNUuploRzD2K0=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			UhsTreyByDiflo1WFAwMQxhr4c0Lj0qYWd7aWmklhUc=
			</data>
		</dict>
		<key>leveldb_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			xv3kLgt49swmIPDU9oQ768QxmOwAgEeFe/ske8EcxEo=
			</data>
		</dict>
		<key>leveldb_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			CsAKC37/AHnc4lLimrQdcc9YxslP5OROoRDO9cYpcOE=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
