/*
 * Copyright 2017 Google
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#import "FIRAggregateField.h"
#import "FIRAggregateQuery.h"
#import "FIRAggregateQuerySnapshot.h"
#import "FIRAggregateSource.h"
#import "FIRCollectionReference.h"
#import "FIRDocumentChange.h"
#import "FIRDocumentReference.h"
#import "FIRDocumentSnapshot.h"
#import "FIRFieldPath.h"
#import "FIRFieldValue.h"
#import "FIRFilter.h"
#import "FIRFirestore.h"
#import "FIRFirestoreErrors.h"
#import "FIRFirestoreSettings.h"
#import "FIRGeoPoint.h"
#import "FIRListenerRegistration.h"
#import "FIRLoadBundleTask.h"
#import "FIRLocalCacheSettings.h"
#import "FIRQuery.h"
#import "FIRQuerySnapshot.h"
#import "FIRSnapshotListenOptions.h"
#import "FIRSnapshotMetadata.h"
#import "FIRTimestamp.h"
#import "FIRTransaction.h"
#import "FIRTransactionOptions.h"
#import "FIRWriteBatch.h"
