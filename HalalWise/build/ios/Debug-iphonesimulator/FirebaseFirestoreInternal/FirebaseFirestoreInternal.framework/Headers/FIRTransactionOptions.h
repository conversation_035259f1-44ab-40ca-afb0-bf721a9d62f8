/*
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * Options to customize the behavior of `Firestore.runTransactionWithOptions()`.
 */
NS_SWIFT_NAME(TransactionOptions)
@interface FIRTransactionOptions : NSObject <NSCopying>

/**
 * Creates and returns a new `TransactionOptions` object with all properties initialized to their
 * default values.
 *
 * @return The created `TransactionOptions` object.
 */
- (instancetype)init NS_DESIGNATED_INITIALIZER;

/** The maximum number of attempts to commit, after which transaction fails. Default is 5. */
@property(nonatomic, assign) NSInteger maxAttempts;

@end

NS_ASSUME_NONNULL_END
