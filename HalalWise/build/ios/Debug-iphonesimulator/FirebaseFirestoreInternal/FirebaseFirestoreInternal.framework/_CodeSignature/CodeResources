<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>FirebaseFirestoreInternal_Privacy.bundle/Info.plist</key>
		<data>
		CZJrX7mFbmH3uNTNW10N6Ej7PNY=
		</data>
		<key>FirebaseFirestoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		WXQUJr75eMRgiVnLGyf8Gr3uLUU=
		</data>
		<key>Headers/FIRAggregateField.h</key>
		<data>
		0LW8pFKyi4kQ1EpYtZXW3GjTjJ0=
		</data>
		<key>Headers/FIRAggregateQuery.h</key>
		<data>
		nRZWBYQbOggM+r0gwEt7eck7J3c=
		</data>
		<key>Headers/FIRAggregateQuerySnapshot.h</key>
		<data>
		4AtLCoLLuaYqkgR5/G+eZg8jBn4=
		</data>
		<key>Headers/FIRAggregateSource.h</key>
		<data>
		LlVa11Kh64mApDnBwYVx54SZtUQ=
		</data>
		<key>Headers/FIRCollectionReference.h</key>
		<data>
		nvwBqdiTr+okLQWX/2edbm25sIQ=
		</data>
		<key>Headers/FIRDocumentChange.h</key>
		<data>
		Z5wH+9kygCY1dWYb42uOSVk7fKI=
		</data>
		<key>Headers/FIRDocumentReference.h</key>
		<data>
		jYd3QnQL96HA9D+2df1bc2ZpG4s=
		</data>
		<key>Headers/FIRDocumentSnapshot.h</key>
		<data>
		MrAukDM+XZ9JcScTIn+cHjXJZW0=
		</data>
		<key>Headers/FIRFieldPath.h</key>
		<data>
		FXD8/va2xyZeiPgoWtehvqd+ZnQ=
		</data>
		<key>Headers/FIRFieldValue.h</key>
		<data>
		7zOFqr23MaB5tPPIyFwIVudJQmI=
		</data>
		<key>Headers/FIRFilter.h</key>
		<data>
		pkHEoEB25YATrjRrfMbeiLeNg0o=
		</data>
		<key>Headers/FIRFirestore.h</key>
		<data>
		183zDFxmLGjOAD+I+XiZztd8YjA=
		</data>
		<key>Headers/FIRFirestoreErrors.h</key>
		<data>
		uRgq9hPsE1/igfTuu+n+FveY2TE=
		</data>
		<key>Headers/FIRFirestoreSettings.h</key>
		<data>
		ctuSelq+agJfQoJxrNpx4OOieoQ=
		</data>
		<key>Headers/FIRFirestoreSource.h</key>
		<data>
		3SfVCoT0HOl7HeQwL/UezU3sBCg=
		</data>
		<key>Headers/FIRGeoPoint.h</key>
		<data>
		DQqhFBv4usgvZadrTcX4jiOZlC4=
		</data>
		<key>Headers/FIRListenerRegistration.h</key>
		<data>
		xFzRtaBqhh/R0lWcBHUua7Da0MQ=
		</data>
		<key>Headers/FIRLoadBundleTask.h</key>
		<data>
		MRnjVBMz2ZcUjZ+0bxa7TjQsocQ=
		</data>
		<key>Headers/FIRLocalCacheSettings.h</key>
		<data>
		fTXx+5mRdkeXw/bssBNSdm6xVgE=
		</data>
		<key>Headers/FIRPersistentCacheIndexManager.h</key>
		<data>
		fgjr34HxLdz5LL/9vNhs77JNHuM=
		</data>
		<key>Headers/FIRQuery.h</key>
		<data>
		amVw+vDnJ8XAT/hswrNO9LvEa5M=
		</data>
		<key>Headers/FIRQuerySnapshot.h</key>
		<data>
		pmzhI26u0KiWXw21qLglCQR7Tpc=
		</data>
		<key>Headers/FIRSnapshotListenOptions.h</key>
		<data>
		DC1PZzG+w7fSIyaxbe7N3LPRA0c=
		</data>
		<key>Headers/FIRSnapshotMetadata.h</key>
		<data>
		Loqqck/ipQwVXghqexawj1xqu90=
		</data>
		<key>Headers/FIRTransaction.h</key>
		<data>
		v7bkkKw+MOuK93iT2KjxWu4vCTQ=
		</data>
		<key>Headers/FIRTransactionOptions.h</key>
		<data>
		ddC+0yQSPrlRh0ZA5leQwHvLqok=
		</data>
		<key>Headers/FIRVectorValue.h</key>
		<data>
		TGPEYlvAIRUVvVwniboPgyh5d2g=
		</data>
		<key>Headers/FIRWriteBatch.h</key>
		<data>
		sR91hk5jQZAYdZNc1inldcm4q40=
		</data>
		<key>Headers/FirebaseFirestore.h</key>
		<data>
		D8RDHiJUEeEamWNFVtIGAqlS/z4=
		</data>
		<key>Headers/FirebaseFirestoreInternal-umbrella.h</key>
		<data>
		HPnhBFrD5q1SLMqG5DHrlaqU3kk=
		</data>
		<key>Info.plist</key>
		<data>
		pIPA03SoOQKhNleU12X+EuHdTIg=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		/485ywOdZLX9pIZ22zI7S7u2ojk=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>FirebaseFirestoreInternal_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			T3ferXuCL5MENrQnB4lvOxEmkoHeWFk+01sYWfPDNdQ=
			</data>
		</dict>
		<key>FirebaseFirestoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			z7s8T3ambVNpi66R9xEMAPIUjm5vE619MlkpCbwBDlE=
			</data>
		</dict>
		<key>Headers/FIRAggregateField.h</key>
		<dict>
			<key>hash2</key>
			<data>
			cIj0WXLtHCXE6oW9HcM1SUspXpv8z/42QC6pzVmatV0=
			</data>
		</dict>
		<key>Headers/FIRAggregateQuery.h</key>
		<dict>
			<key>hash2</key>
			<data>
			/kW0olnbuYvibRn8cz5G9j1j9HHqQx3WkK2H4JMvt8g=
			</data>
		</dict>
		<key>Headers/FIRAggregateQuerySnapshot.h</key>
		<dict>
			<key>hash2</key>
			<data>
			I6hVb3KBdBkJFkQ/PkxGN3j+f8TwSHivFOSVWcC0sPE=
			</data>
		</dict>
		<key>Headers/FIRAggregateSource.h</key>
		<dict>
			<key>hash2</key>
			<data>
			AdxxnkkVlnIBHxhP/IvCSvWXm+cJeKsPuRp3aIVoSro=
			</data>
		</dict>
		<key>Headers/FIRCollectionReference.h</key>
		<dict>
			<key>hash2</key>
			<data>
			lj5E74kQZOPJk4w4NIpei+e6q/p8QfTkxYqYJk4bJc4=
			</data>
		</dict>
		<key>Headers/FIRDocumentChange.h</key>
		<dict>
			<key>hash2</key>
			<data>
			V+KurXrFgExtvZEC9flqqBLwx5OItNTeGHqRBwVXyoE=
			</data>
		</dict>
		<key>Headers/FIRDocumentReference.h</key>
		<dict>
			<key>hash2</key>
			<data>
			cPw0PIIyln99F8KyIUnaD0oPZUG0killU+58g9J77V0=
			</data>
		</dict>
		<key>Headers/FIRDocumentSnapshot.h</key>
		<dict>
			<key>hash2</key>
			<data>
			yilm5bCjcA8PqwgZJghIgJx/Pw0j6qsA8upSGztFJUw=
			</data>
		</dict>
		<key>Headers/FIRFieldPath.h</key>
		<dict>
			<key>hash2</key>
			<data>
			YfCZNY3mIJHdhpXcPWG1x8vjKtoudwocdecRe8S6PaY=
			</data>
		</dict>
		<key>Headers/FIRFieldValue.h</key>
		<dict>
			<key>hash2</key>
			<data>
			iIq25h1WAHfWQaWLASTzKSNQcHQ657xA3/Omrnt2vVw=
			</data>
		</dict>
		<key>Headers/FIRFilter.h</key>
		<dict>
			<key>hash2</key>
			<data>
			tGhAMyFNThPACvD0Zk0+CC6p64osPk0AECZixqKZRbc=
			</data>
		</dict>
		<key>Headers/FIRFirestore.h</key>
		<dict>
			<key>hash2</key>
			<data>
			YxAov+J839moriawzZteqjuwfA1TEWQl/aG6QJkbq8c=
			</data>
		</dict>
		<key>Headers/FIRFirestoreErrors.h</key>
		<dict>
			<key>hash2</key>
			<data>
			eQWFa0N48Y5L6Jc04YPqSVRQUIZxbGm1HFYnR1+tpVc=
			</data>
		</dict>
		<key>Headers/FIRFirestoreSettings.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Tyx6rgL9sDidkUzXyj/onu4CjG5Dx7sEWycyw9DthK8=
			</data>
		</dict>
		<key>Headers/FIRFirestoreSource.h</key>
		<dict>
			<key>hash2</key>
			<data>
			KwoNLisywRepcESJGK6Gf86w3RJ1cAA3OP1WBxqin/c=
			</data>
		</dict>
		<key>Headers/FIRGeoPoint.h</key>
		<dict>
			<key>hash2</key>
			<data>
			523AT43k9D7Vom4A7wDoxr2tp/tLltHPsadupRfbiVY=
			</data>
		</dict>
		<key>Headers/FIRListenerRegistration.h</key>
		<dict>
			<key>hash2</key>
			<data>
			uZ2bb0ma+vPyRtrsIqGq5irNxFRrxQCV1iPHgek+b6c=
			</data>
		</dict>
		<key>Headers/FIRLoadBundleTask.h</key>
		<dict>
			<key>hash2</key>
			<data>
			oC3AOQqDNUn2RLylcIM/1FuCBmcdS6ITI8eG49seA6A=
			</data>
		</dict>
		<key>Headers/FIRLocalCacheSettings.h</key>
		<dict>
			<key>hash2</key>
			<data>
			iTSDUERvrRPoQh4+s6cNnNkiZ7/ISrOcYTMhIEHpZBk=
			</data>
		</dict>
		<key>Headers/FIRPersistentCacheIndexManager.h</key>
		<dict>
			<key>hash2</key>
			<data>
			prHQUsIoOctP1THgrSR37a1zhBvYftVksLAiG5gqLcE=
			</data>
		</dict>
		<key>Headers/FIRQuery.h</key>
		<dict>
			<key>hash2</key>
			<data>
			5LLRhBdFm+CPA7dx6pZpFvJPinVNjz4bBw5gPLWfPFg=
			</data>
		</dict>
		<key>Headers/FIRQuerySnapshot.h</key>
		<dict>
			<key>hash2</key>
			<data>
			sRBLXkQDiY6aZk94zvLm1XSfFkUxS7OOYMgz5Vfro3c=
			</data>
		</dict>
		<key>Headers/FIRSnapshotListenOptions.h</key>
		<dict>
			<key>hash2</key>
			<data>
			dzdP1AgarPbsVlQJTg6SVK/klN63cPWGSbhgkKDvw8w=
			</data>
		</dict>
		<key>Headers/FIRSnapshotMetadata.h</key>
		<dict>
			<key>hash2</key>
			<data>
			YoA1waQ/wO/8nMGnl65FPJcCiEqnpCregbgCn2xL+HY=
			</data>
		</dict>
		<key>Headers/FIRTransaction.h</key>
		<dict>
			<key>hash2</key>
			<data>
			e16PQDMxtslwgkbuRtYSALlJ/tKVjyI1nKsAJ5vtNFQ=
			</data>
		</dict>
		<key>Headers/FIRTransactionOptions.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ms6FqxXaJFP8X/G2ars5BJf+B/h31G0dYLSEeEKBb3A=
			</data>
		</dict>
		<key>Headers/FIRVectorValue.h</key>
		<dict>
			<key>hash2</key>
			<data>
			0fFEa2F648R9japPkGHZ/7nN7x4YRZLWFSj5wURhVzE=
			</data>
		</dict>
		<key>Headers/FIRWriteBatch.h</key>
		<dict>
			<key>hash2</key>
			<data>
			SItHcoMMHpX+1hEcfloE0l0eMGwDDB/kEMQIt2c3A44=
			</data>
		</dict>
		<key>Headers/FirebaseFirestore.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Hr6FPX835fhkrOD35N2Sau8AkawIvWhdtU9HqNFhBd0=
			</data>
		</dict>
		<key>Headers/FirebaseFirestoreInternal-umbrella.h</key>
		<dict>
			<key>hash2</key>
			<data>
			JDenVbUkw/RoUuano2WzwkwHV1KxnH+LwQyXtpCtpQg=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			+XpmRdcLuVoi+77DIwhoSUXwmmtCTq45ZtFVIM7mzu4=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
