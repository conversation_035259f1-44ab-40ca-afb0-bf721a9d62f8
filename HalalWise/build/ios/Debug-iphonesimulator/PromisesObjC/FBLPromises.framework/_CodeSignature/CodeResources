<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>FBLPromises_Privacy.bundle/Info.plist</key>
		<data>
		2SJBPs18s3FNJdSkWFi6sH419Fo=
		</data>
		<key>FBLPromises_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		ZajnvEs/MYRS3X4TPLAhBWi8mc4=
		</data>
		<key>Headers/FBLPromise+All.h</key>
		<data>
		1/HWSa9B9CfE+6oTjy3jrp1Dbng=
		</data>
		<key>Headers/FBLPromise+Always.h</key>
		<data>
		sVZckdpNp0YMZno6+IaJ16Zal00=
		</data>
		<key>Headers/FBLPromise+Any.h</key>
		<data>
		jYWCdqjloLKNtDe+sIQ4PyQsf0I=
		</data>
		<key>Headers/FBLPromise+Async.h</key>
		<data>
		gIl6GZ+2tBZw7sNlHmw6tQggHn4=
		</data>
		<key>Headers/FBLPromise+Await.h</key>
		<data>
		WKlBKfPKetJ8or+tZtW13HAGcaM=
		</data>
		<key>Headers/FBLPromise+Catch.h</key>
		<data>
		WaTiqwvv8w13vIKlm2K4DAgZulo=
		</data>
		<key>Headers/FBLPromise+Delay.h</key>
		<data>
		qoQSMY2FcD2SV3kKq34NITtbdmk=
		</data>
		<key>Headers/FBLPromise+Do.h</key>
		<data>
		ggkiDySeq8jvunvZvPkZ/Kkn8xU=
		</data>
		<key>Headers/FBLPromise+Race.h</key>
		<data>
		KhU7Y/hu/++OhV8HrTR0blCqsa8=
		</data>
		<key>Headers/FBLPromise+Recover.h</key>
		<data>
		bQIMfszj33smHtitYuadQj5unrM=
		</data>
		<key>Headers/FBLPromise+Reduce.h</key>
		<data>
		fHvVgIVNfukW8aWkuQb9Upl1Hv8=
		</data>
		<key>Headers/FBLPromise+Retry.h</key>
		<data>
		EFBZkIULq7mo/1LX3HSsRl51Fhw=
		</data>
		<key>Headers/FBLPromise+Testing.h</key>
		<data>
		Ks+FXD691hFVSvDw/g/0x4ic7+0=
		</data>
		<key>Headers/FBLPromise+Then.h</key>
		<data>
		xXwhWUcFcLdpUBrK28dob5PeMRI=
		</data>
		<key>Headers/FBLPromise+Timeout.h</key>
		<data>
		yngMore1DA762uLfNvM0zRhL/u0=
		</data>
		<key>Headers/FBLPromise+Validate.h</key>
		<data>
		QXCa+SPD+GyNt+fKriUFP4JLYi0=
		</data>
		<key>Headers/FBLPromise+Wrap.h</key>
		<data>
		pnbKEVv3KohF9zBVmwpY92K1/H8=
		</data>
		<key>Headers/FBLPromise.h</key>
		<data>
		7OfQzgwmGOgsO/CHeNSkTs6YP2g=
		</data>
		<key>Headers/FBLPromiseError.h</key>
		<data>
		NiD1rwNDO7Tz69bYyOj9qmlCGRQ=
		</data>
		<key>Headers/FBLPromises.h</key>
		<data>
		xmHnPqDlpJpUbAqgOJw5Da3418U=
		</data>
		<key>Headers/PromisesObjC-umbrella.h</key>
		<data>
		LRjj72lPCA3L9iDRZehhvoVhUGU=
		</data>
		<key>Info.plist</key>
		<data>
		5uZ7gOgcUmg2oFCkU/iOhepTUyY=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		LZnGdhdf01gvhIz70qtzgzIYPX4=
		</data>
		<key>PrivateHeaders/FBLPromisePrivate.h</key>
		<data>
		J7AOJnwCKzdcaI5lsMAhXoByoT0=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>FBLPromises_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			2SJBPs18s3FNJdSkWFi6sH419Fo=
			</data>
			<key>hash2</key>
			<data>
			VzBcQ4Zp7I6w7yWnRu9jAEFjT4LYTGCL+Mc32hd+uoc=
			</data>
		</dict>
		<key>FBLPromises_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			ZajnvEs/MYRS3X4TPLAhBWi8mc4=
			</data>
			<key>hash2</key>
			<data>
			dLDNcvwjwe8wLyLuJ1P2GBfNxa8P96fy0GMrUk+4rOo=
			</data>
		</dict>
		<key>Headers/FBLPromise+All.h</key>
		<dict>
			<key>hash</key>
			<data>
			1/HWSa9B9CfE+6oTjy3jrp1Dbng=
			</data>
			<key>hash2</key>
			<data>
			g6BUKLMTJAGRI/4XjGU/dZaG7rA27W16VZK8WvM1eYs=
			</data>
		</dict>
		<key>Headers/FBLPromise+Always.h</key>
		<dict>
			<key>hash</key>
			<data>
			sVZckdpNp0YMZno6+IaJ16Zal00=
			</data>
			<key>hash2</key>
			<data>
			mSLLqLIfLaAdISUzmCnHE5dI5q5afctdua9s4Ddwrw4=
			</data>
		</dict>
		<key>Headers/FBLPromise+Any.h</key>
		<dict>
			<key>hash</key>
			<data>
			jYWCdqjloLKNtDe+sIQ4PyQsf0I=
			</data>
			<key>hash2</key>
			<data>
			ETMK2COV+ajlfA4XxDUUJzV3p8LUaA+GN9sRLwNE0Ck=
			</data>
		</dict>
		<key>Headers/FBLPromise+Async.h</key>
		<dict>
			<key>hash</key>
			<data>
			gIl6GZ+2tBZw7sNlHmw6tQggHn4=
			</data>
			<key>hash2</key>
			<data>
			dHc8ASbXmQxrkpQCBEbAZL4HTgO6J7gNiv9erzu92QU=
			</data>
		</dict>
		<key>Headers/FBLPromise+Await.h</key>
		<dict>
			<key>hash</key>
			<data>
			WKlBKfPKetJ8or+tZtW13HAGcaM=
			</data>
			<key>hash2</key>
			<data>
			W7kqZbC71TD1Xv9wdtMIjoDbYSZ5ktUSkok5tPZS5A8=
			</data>
		</dict>
		<key>Headers/FBLPromise+Catch.h</key>
		<dict>
			<key>hash</key>
			<data>
			WaTiqwvv8w13vIKlm2K4DAgZulo=
			</data>
			<key>hash2</key>
			<data>
			kwyC7vU/6AZticiALUlN0j/nrmzG7QFExjjBV25pQTY=
			</data>
		</dict>
		<key>Headers/FBLPromise+Delay.h</key>
		<dict>
			<key>hash</key>
			<data>
			qoQSMY2FcD2SV3kKq34NITtbdmk=
			</data>
			<key>hash2</key>
			<data>
			nSl7wEJQ+tww5d/BWlnoA4Mv9wde0pDI2SJaPISo3vI=
			</data>
		</dict>
		<key>Headers/FBLPromise+Do.h</key>
		<dict>
			<key>hash</key>
			<data>
			ggkiDySeq8jvunvZvPkZ/Kkn8xU=
			</data>
			<key>hash2</key>
			<data>
			AbrMui/y3jBRVXloC2ek4ZcwpBQPlqu29/aPRTvOE68=
			</data>
		</dict>
		<key>Headers/FBLPromise+Race.h</key>
		<dict>
			<key>hash</key>
			<data>
			KhU7Y/hu/++OhV8HrTR0blCqsa8=
			</data>
			<key>hash2</key>
			<data>
			CNuTRimQ0rZFNUumwP/hYOqi204+JbffrEkONBFuIVg=
			</data>
		</dict>
		<key>Headers/FBLPromise+Recover.h</key>
		<dict>
			<key>hash</key>
			<data>
			bQIMfszj33smHtitYuadQj5unrM=
			</data>
			<key>hash2</key>
			<data>
			WP2VmkMLEJh9pP+sW2pW5945/IEIEhib/7rGfCaIWQQ=
			</data>
		</dict>
		<key>Headers/FBLPromise+Reduce.h</key>
		<dict>
			<key>hash</key>
			<data>
			fHvVgIVNfukW8aWkuQb9Upl1Hv8=
			</data>
			<key>hash2</key>
			<data>
			OVoK729ybsf8EX8DRwCRRidRbTPXhRiHZjV4Kn40EtE=
			</data>
		</dict>
		<key>Headers/FBLPromise+Retry.h</key>
		<dict>
			<key>hash</key>
			<data>
			EFBZkIULq7mo/1LX3HSsRl51Fhw=
			</data>
			<key>hash2</key>
			<data>
			onMjxab1k7BI/BmEl5YQXwuaYLZwgEh/ERMPIZ5utlY=
			</data>
		</dict>
		<key>Headers/FBLPromise+Testing.h</key>
		<dict>
			<key>hash</key>
			<data>
			Ks+FXD691hFVSvDw/g/0x4ic7+0=
			</data>
			<key>hash2</key>
			<data>
			fEVZv1dfYDKHOKyiZqgdyhYdURZ0tKWsHGVPIQL70f0=
			</data>
		</dict>
		<key>Headers/FBLPromise+Then.h</key>
		<dict>
			<key>hash</key>
			<data>
			xXwhWUcFcLdpUBrK28dob5PeMRI=
			</data>
			<key>hash2</key>
			<data>
			QMH7kzRIHfHY/An28iUioHxCvP1dvpEJ4Giffps6eBI=
			</data>
		</dict>
		<key>Headers/FBLPromise+Timeout.h</key>
		<dict>
			<key>hash</key>
			<data>
			yngMore1DA762uLfNvM0zRhL/u0=
			</data>
			<key>hash2</key>
			<data>
			NoaGHny9bLEcgwjjW5UQxZBayRPcQORpW7WJdxa8iDg=
			</data>
		</dict>
		<key>Headers/FBLPromise+Validate.h</key>
		<dict>
			<key>hash</key>
			<data>
			QXCa+SPD+GyNt+fKriUFP4JLYi0=
			</data>
			<key>hash2</key>
			<data>
			c+8J+TGkNw0JZn+It/LBQP3DACatwAluOhLLNDPozMw=
			</data>
		</dict>
		<key>Headers/FBLPromise+Wrap.h</key>
		<dict>
			<key>hash</key>
			<data>
			pnbKEVv3KohF9zBVmwpY92K1/H8=
			</data>
			<key>hash2</key>
			<data>
			m576DCvDiDcYotjxumxrhOVOJ8XyUnJcIM7qlVvq2nQ=
			</data>
		</dict>
		<key>Headers/FBLPromise.h</key>
		<dict>
			<key>hash</key>
			<data>
			7OfQzgwmGOgsO/CHeNSkTs6YP2g=
			</data>
			<key>hash2</key>
			<data>
			8zo1VQB3l2nvOKVHu1ra+QRd1asb8+/pFf9rbETehMk=
			</data>
		</dict>
		<key>Headers/FBLPromiseError.h</key>
		<dict>
			<key>hash</key>
			<data>
			NiD1rwNDO7Tz69bYyOj9qmlCGRQ=
			</data>
			<key>hash2</key>
			<data>
			3LBDNhkSuyDE1LE8Kk9c19ytLxAw5ABQnJu7J4/1yk8=
			</data>
		</dict>
		<key>Headers/FBLPromises.h</key>
		<dict>
			<key>hash</key>
			<data>
			xmHnPqDlpJpUbAqgOJw5Da3418U=
			</data>
			<key>hash2</key>
			<data>
			wH9eWTsWL9EixsUdC7G1gbD8vY97El45adWrrpwvqM0=
			</data>
		</dict>
		<key>Headers/PromisesObjC-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			LRjj72lPCA3L9iDRZehhvoVhUGU=
			</data>
			<key>hash2</key>
			<data>
			AToK0tCJy6hieeo6JMbMhLunYgbQLarPqQyHJ6dfRuA=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			LZnGdhdf01gvhIz70qtzgzIYPX4=
			</data>
			<key>hash2</key>
			<data>
			AMm7+HhwNWfKD7lB61N5cRjihG7RKsrGL/xaEOYGS/Y=
			</data>
		</dict>
		<key>PrivateHeaders/FBLPromisePrivate.h</key>
		<dict>
			<key>hash</key>
			<data>
			J7AOJnwCKzdcaI5lsMAhXoByoT0=
			</data>
			<key>hash2</key>
			<data>
			yTbxGjb54FkX0p6ayty7U27WUyoFPXE9uJnivUTlPv4=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
