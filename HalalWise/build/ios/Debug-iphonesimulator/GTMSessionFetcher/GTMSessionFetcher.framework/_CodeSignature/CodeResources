<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/GTMSessionFetcher-umbrella.h</key>
		<data>
		uk5tfn4+qFjgRMutCcPDErlSaiE=
		</data>
		<key>Headers/GTMSessionFetcher.h</key>
		<data>
		BSHJFm140IUrEy+WQroOAPbbfwc=
		</data>
		<key>Headers/GTMSessionFetcherLogging.h</key>
		<data>
		vFtOipBuEf02JCLgs39Y4wvulB8=
		</data>
		<key>Headers/GTMSessionFetcherService.h</key>
		<data>
		l1NvgQnmqWY7nHU+am2ODmU/udU=
		</data>
		<key>Headers/GTMSessionUploadFetcher.h</key>
		<data>
		IG6SPUop/AlBDPaLGhMt0LxihCw=
		</data>
		<key>Info.plist</key>
		<data>
		T62Aa5RAfndhkIYDr1kM7+oEIbc=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		Zd+B+N1kD6ufOJwjEQ1h4yl/05s=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/GTMSessionFetcher-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			uk5tfn4+qFjgRMutCcPDErlSaiE=
			</data>
			<key>hash2</key>
			<data>
			3rne+xIEztt63voNklSVEM3UXM+Jkgc6ENNW5IKGMh8=
			</data>
		</dict>
		<key>Headers/GTMSessionFetcher.h</key>
		<dict>
			<key>hash</key>
			<data>
			BSHJFm140IUrEy+WQroOAPbbfwc=
			</data>
			<key>hash2</key>
			<data>
			AikS6joms+aKocfXKHq7stAZqLfsOoxdWTHRkLVidh4=
			</data>
		</dict>
		<key>Headers/GTMSessionFetcherLogging.h</key>
		<dict>
			<key>hash</key>
			<data>
			vFtOipBuEf02JCLgs39Y4wvulB8=
			</data>
			<key>hash2</key>
			<data>
			F7xvUlqnZ4ZstcfQaleLxrVyaal/wPqL9msBVjsAXxo=
			</data>
		</dict>
		<key>Headers/GTMSessionFetcherService.h</key>
		<dict>
			<key>hash</key>
			<data>
			l1NvgQnmqWY7nHU+am2ODmU/udU=
			</data>
			<key>hash2</key>
			<data>
			xC/vXxkrltm+k86mtEKbsQEOj+FO0uzlKRIuSyWa0fU=
			</data>
		</dict>
		<key>Headers/GTMSessionUploadFetcher.h</key>
		<dict>
			<key>hash</key>
			<data>
			IG6SPUop/AlBDPaLGhMt0LxihCw=
			</data>
			<key>hash2</key>
			<data>
			Gr1/RHX4ExVPnbpwzuXDoEw4+Qq1g64k6/C7jKeWzcQ=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			Zd+B+N1kD6ufOJwjEQ1h4yl/05s=
			</data>
			<key>hash2</key>
			<data>
			s/7Ay5SAF/AZJnsEfDo0km7Zw2aVRXsBLuyUjeZo7/Y=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
