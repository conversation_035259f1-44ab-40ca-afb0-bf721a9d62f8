<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/FLTImagePickerImageUtil.h</key>
		<data>
		SMlyFZj2SvJBjywLwVFhKKpLQ78=
		</data>
		<key>Headers/FLTImagePickerMetaDataUtil.h</key>
		<data>
		zSOS6f5yY1LLDVrnjr2TmDCspYU=
		</data>
		<key>Headers/FLTImagePickerPhotoAssetUtil.h</key>
		<data>
		xVS+/tZ8Q8rmGORRmOc3Sp8fikQ=
		</data>
		<key>Headers/FLTImagePickerPlugin.h</key>
		<data>
		ozsn/Ur7a327x/3nSDP7hvGTsZM=
		</data>
		<key>Headers/FLTImagePickerPlugin_Test.h</key>
		<data>
		klK8quYH8qoZE9pCoPnkY4y3lnM=
		</data>
		<key>Headers/FLTPHPickerSaveImageToPathOperation.h</key>
		<data>
		jlXowiM1ZSEn4nT2E7cVOVcVnNE=
		</data>
		<key>Headers/image_picker_ios-umbrella.h</key>
		<data>
		OFQwp7YGozQ7u1wPdwSWhKjXAsM=
		</data>
		<key>Headers/messages.g.h</key>
		<data>
		cooWgcfGyhGQ45GmX2DdvcGp/s0=
		</data>
		<key>Info.plist</key>
		<data>
		DwEHhqrdZbbO7Hx+0Oxe/yIVidc=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		utEeFhanjdWjDLCc4TFqNbjeY6g=
		</data>
		<key>image_picker_ios_privacy.bundle/Info.plist</key>
		<data>
		xoXjDBLDbasshS+nYhF3dV3f41k=
		</data>
		<key>image_picker_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/FLTImagePickerImageUtil.h</key>
		<dict>
			<key>hash2</key>
			<data>
			yykcI3M6t8zMn7Ass3OakoUbw6aHcboaEAOWLO+EZbM=
			</data>
		</dict>
		<key>Headers/FLTImagePickerMetaDataUtil.h</key>
		<dict>
			<key>hash2</key>
			<data>
			cO9lumGQK7vjRX2TSDwnmMWJZZINVs2zFdvpul+QC2M=
			</data>
		</dict>
		<key>Headers/FLTImagePickerPhotoAssetUtil.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Rxd98uONWhc9uJNYXy/R8Iolzr1f9cNIk+sZ6KCBXzU=
			</data>
		</dict>
		<key>Headers/FLTImagePickerPlugin.h</key>
		<dict>
			<key>hash2</key>
			<data>
			XjmVowotP2lIovgsGekMSjcTgxg8jq8Ll1BqxMkPvWM=
			</data>
		</dict>
		<key>Headers/FLTImagePickerPlugin_Test.h</key>
		<dict>
			<key>hash2</key>
			<data>
			w8GSSQnl89cE6ibIovG1Xc3+wWDKAn0VW7VJ6gs0rew=
			</data>
		</dict>
		<key>Headers/FLTPHPickerSaveImageToPathOperation.h</key>
		<dict>
			<key>hash2</key>
			<data>
			CdeBQbpUFC180NchzgvZhlkRnk4Y5Ht5CtF7LbxcyT8=
			</data>
		</dict>
		<key>Headers/image_picker_ios-umbrella.h</key>
		<dict>
			<key>hash2</key>
			<data>
			5jWAnSSqwjVc4s95RG4YGKdGYJ330Z37y8fZM2ly8IQ=
			</data>
		</dict>
		<key>Headers/messages.g.h</key>
		<dict>
			<key>hash2</key>
			<data>
			oRAatXwe9rRan/dn6rfOBRhhtgBdNBK61HDxulYibBA=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			jZbtVbbZP4wOZUE32eqGhwZOTha7/2e0oBoYLDnekhc=
			</data>
		</dict>
		<key>image_picker_ios_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			O+ea+292iBhDMloJf/pspBqezWCLaXewLSWHU1d3TYo=
			</data>
		</dict>
		<key>image_picker_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
