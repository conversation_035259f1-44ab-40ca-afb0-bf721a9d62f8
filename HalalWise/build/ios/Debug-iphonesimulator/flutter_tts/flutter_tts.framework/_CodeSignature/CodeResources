<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/FlutterTtsPlugin.h</key>
		<data>
		ZxayPmdBb0+nKLzjSm2ExbsWZ6c=
		</data>
		<key>Headers/flutter_tts-Swift.h</key>
		<data>
		7KzkWgghXrHZQO6V+Dk2s9b8T5Y=
		</data>
		<key>Headers/flutter_tts-umbrella.h</key>
		<data>
		aoPbVhKTsfEg07dPSyEGWANk6VE=
		</data>
		<key>Info.plist</key>
		<data>
		zKHhxM/8sRymua23Zy3t16TouIU=
		</data>
		<key>Modules/flutter_tts.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		R000gdw1Axihmr3l0BzvhHQTZjM=
		</data>
		<key>Modules/flutter_tts.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		Nh+kHQQscgRhI/EHzU0qwGucKpY=
		</data>
		<key>Modules/flutter_tts.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/flutter_tts.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		IAY/kSrkhZJlFZDp4ym+En1mq8k=
		</data>
		<key>Modules/flutter_tts.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<data>
		7GuSWb5bKyyXFb5ityc1r9bCu8E=
		</data>
		<key>Modules/flutter_tts.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/flutter_tts.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		O0dLSNqCzqtWhJC0QiRHIyuIV24=
		</data>
		<key>Modules/flutter_tts.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		c4WZRtV69s15fpQ8e/Uad7FMQDQ=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		CzPH6+WNdqIrVTKtoLf20Rcd118=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/FlutterTtsPlugin.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZxayPmdBb0+nKLzjSm2ExbsWZ6c=
			</data>
			<key>hash2</key>
			<data>
			TBMsBpB+hoT6q1ez8cLSIjfHVyQSub2ATYmY0bqxpXk=
			</data>
		</dict>
		<key>Headers/flutter_tts-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			7KzkWgghXrHZQO6V+Dk2s9b8T5Y=
			</data>
			<key>hash2</key>
			<data>
			4eEMoscV0uKKfqTKV3Aq/sNEE85hc+w1qPtVCUznrYM=
			</data>
		</dict>
		<key>Headers/flutter_tts-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			aoPbVhKTsfEg07dPSyEGWANk6VE=
			</data>
			<key>hash2</key>
			<data>
			GdDRvTeZgc8ZQRLCMB1ruwTm2wM4HtwuSLlt7qU+Msg=
			</data>
		</dict>
		<key>Modules/flutter_tts.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			R000gdw1Axihmr3l0BzvhHQTZjM=
			</data>
			<key>hash2</key>
			<data>
			dsa/+dE/l2fImUexgZTZ5zY3fMPhEo+BLIlWvhYN5bQ=
			</data>
		</dict>
		<key>Modules/flutter_tts.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			Nh+kHQQscgRhI/EHzU0qwGucKpY=
			</data>
			<key>hash2</key>
			<data>
			Xgh+72X+TdfjA8FuAEktKWTZejUIw9NvHQhfhyKZAjI=
			</data>
		</dict>
		<key>Modules/flutter_tts.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			gcwBsH4BgyFY4sVtNt+/xOKS3vY=
			</data>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/flutter_tts.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			IAY/kSrkhZJlFZDp4ym+En1mq8k=
			</data>
			<key>hash2</key>
			<data>
			bcbRKweJwmuxIJPCJe9P4PuIdt2gv7Uof0xu/EKrYlI=
			</data>
		</dict>
		<key>Modules/flutter_tts.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			7GuSWb5bKyyXFb5ityc1r9bCu8E=
			</data>
			<key>hash2</key>
			<data>
			WUvbBSPHbj0zhNe34QJQPG0r2KEf5frg5WabewSc3kY=
			</data>
		</dict>
		<key>Modules/flutter_tts.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			gcwBsH4BgyFY4sVtNt+/xOKS3vY=
			</data>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/flutter_tts.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			O0dLSNqCzqtWhJC0QiRHIyuIV24=
			</data>
			<key>hash2</key>
			<data>
			PdfyjQ/7pTmr2Mxs58pRvmN8H8AccLv7YH2bysahi0A=
			</data>
		</dict>
		<key>Modules/flutter_tts.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			c4WZRtV69s15fpQ8e/Uad7FMQDQ=
			</data>
			<key>hash2</key>
			<data>
			z4K9DBnl68di2FyKn6NBwj4UaCo6k82oNXut4WI5Xus=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			CzPH6+WNdqIrVTKtoLf20Rcd118=
			</data>
			<key>hash2</key>
			<data>
			sFHES9ufLMtahWzebct/MIHKJHLA7566W/8aubN6uz4=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
