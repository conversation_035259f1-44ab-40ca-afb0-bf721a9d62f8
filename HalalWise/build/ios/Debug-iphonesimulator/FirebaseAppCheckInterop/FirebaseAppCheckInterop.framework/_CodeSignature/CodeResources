<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/FIRAppCheckInterop.h</key>
		<data>
		i4P3VfiVwHf/7YyJH4jhtJYXxW0=
		</data>
		<key>Headers/FIRAppCheckProtocol.h</key>
		<data>
		dOd90U4QM7XfWxIo40/g0hrtWIk=
		</data>
		<key>Headers/FIRAppCheckTokenProtocol.h</key>
		<data>
		bgQ53ecPMP4YpaopvMN4dpI2oco=
		</data>
		<key>Headers/FIRAppCheckTokenResultInterop.h</key>
		<data>
		xVWF6LnYC4mr+6+X7odpOGjF7w4=
		</data>
		<key>Headers/FirebaseAppCheckInterop-umbrella.h</key>
		<data>
		pxt0i9AGnD/8DpzCcrRgRKEm8Jg=
		</data>
		<key>Headers/FirebaseAppCheckInterop.h</key>
		<data>
		vpWE0td3RjSs6BE3DePjPpu29vE=
		</data>
		<key>Info.plist</key>
		<data>
		ux1UcZDRA1/btpVApXfUhtV6A0A=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		w9L0BFybTP1Xhk/EOcOicZUdfkY=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/FIRAppCheckInterop.h</key>
		<dict>
			<key>hash</key>
			<data>
			i4P3VfiVwHf/7YyJH4jhtJYXxW0=
			</data>
			<key>hash2</key>
			<data>
			9H3bC5Z5adEjOtzsWPfO71dJraOxxHku8QGGWFYzbB0=
			</data>
		</dict>
		<key>Headers/FIRAppCheckProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			dOd90U4QM7XfWxIo40/g0hrtWIk=
			</data>
			<key>hash2</key>
			<data>
			H7RWionJTPNrFL6gD+Kg1u3/f/kLDY/b5GKKya3jaMo=
			</data>
		</dict>
		<key>Headers/FIRAppCheckTokenProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			bgQ53ecPMP4YpaopvMN4dpI2oco=
			</data>
			<key>hash2</key>
			<data>
			ZNmBtlVFrF6x7kyjbcKyIgw0IQGmoBU0OivXwqTUebw=
			</data>
		</dict>
		<key>Headers/FIRAppCheckTokenResultInterop.h</key>
		<dict>
			<key>hash</key>
			<data>
			xVWF6LnYC4mr+6+X7odpOGjF7w4=
			</data>
			<key>hash2</key>
			<data>
			VeCsPwt5xPDpzdYUz3Fltl3iaUZ+Dh4wJwW7xsbbx3A=
			</data>
		</dict>
		<key>Headers/FirebaseAppCheckInterop-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			pxt0i9AGnD/8DpzCcrRgRKEm8Jg=
			</data>
			<key>hash2</key>
			<data>
			9KwL6dnQ+jxuPbPq90beyRdexZva37lityZR3J2jT8o=
			</data>
		</dict>
		<key>Headers/FirebaseAppCheckInterop.h</key>
		<dict>
			<key>hash</key>
			<data>
			vpWE0td3RjSs6BE3DePjPpu29vE=
			</data>
			<key>hash2</key>
			<data>
			LzLhkmVBtmgeG5INC+k/6T00EDETBusy2LgRuo+Yi0g=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			w9L0BFybTP1Xhk/EOcOicZUdfkY=
			</data>
			<key>hash2</key>
			<data>
			/Zv+D2gmO7hwGLJmy0xozuyd0uR3cgegoBkDGsGrMww=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
