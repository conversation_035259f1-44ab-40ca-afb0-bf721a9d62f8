<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>GoogleUtilities_Privacy.bundle/Info.plist</key>
		<data>
		ZbVImixPkaGn7m4yLuEM2DRNPPk=
		</data>
		<key>GoogleUtilities_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		TrRzpkxcmDwPf9xXhrQKnuRceyI=
		</data>
		<key>Headers/GULAppEnvironmentUtil.h</key>
		<data>
		NGo9Y7oLjVb4AS8n5vgMLDZ0uqM=
		</data>
		<key>Headers/GULHeartbeatDateStorable.h</key>
		<data>
		4OtriHMHMXlkFjw9E9asMucvYBk=
		</data>
		<key>Headers/GULHeartbeatDateStorage.h</key>
		<data>
		P/qw3G71G5PCb2AWoGkwoMPbwkg=
		</data>
		<key>Headers/GULHeartbeatDateStorageUserDefaults.h</key>
		<data>
		yIxvciZTWSq2QppEk0lfLQw8Fno=
		</data>
		<key>Headers/GULKeychainStorage.h</key>
		<data>
		e286JCIHL6bzzSpelRVlIgbNei8=
		</data>
		<key>Headers/GULKeychainUtils.h</key>
		<data>
		zbMNKiNQyW5isBVmHY6uTrv0cRk=
		</data>
		<key>Headers/GULLogger.h</key>
		<data>
		HTjFj96M5thN8ETKvg0g/RjaeVc=
		</data>
		<key>Headers/GULLoggerLevel.h</key>
		<data>
		kI/Q5XKyjxY19Tph/lUMaOC09ZE=
		</data>
		<key>Headers/GULNSData+zlib.h</key>
		<data>
		kFWywRhiZKZSTElPxadV/AaoVqA=
		</data>
		<key>Headers/GULNetworkInfo.h</key>
		<data>
		WsAAfsEiwFkuHGHVl5YZZPx/y7g=
		</data>
		<key>Headers/GULSecureCoding.h</key>
		<data>
		NqI3HnVooz9+xSkTFaaO0Qu9wPE=
		</data>
		<key>Headers/GULURLSessionDataResponse.h</key>
		<data>
		C7k2DFJRl4sOYxgFqiOpnGh1Xho=
		</data>
		<key>Headers/GULUserDefaults.h</key>
		<data>
		EBGg0ZapNUzyIzb6nCliBREczUA=
		</data>
		<key>Headers/GoogleUtilities-umbrella.h</key>
		<data>
		mboIYYo2v6Dv/J8bhecVbZcL6BI=
		</data>
		<key>Headers/NSURLSession+GULPromises.h</key>
		<data>
		2BgzYjJd/EL8eHaeK39hhonnQHk=
		</data>
		<key>Info.plist</key>
		<data>
		wpTu0C+Zly33U96bANuicLyyYpk=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		jNNM5/vRCkspln7DuMCSYf0cbB8=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>GoogleUtilities_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			ZbVImixPkaGn7m4yLuEM2DRNPPk=
			</data>
			<key>hash2</key>
			<data>
			5N2o18BYDZGGiQeKPZi1H3qLnj3f8PZW5gG2E2ijWJM=
			</data>
		</dict>
		<key>GoogleUtilities_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			TrRzpkxcmDwPf9xXhrQKnuRceyI=
			</data>
			<key>hash2</key>
			<data>
			jxZCM8XSFfsGzrh4STQCHd3X05qJ15Xla6XIMqsPs/c=
			</data>
		</dict>
		<key>Headers/GULAppEnvironmentUtil.h</key>
		<dict>
			<key>hash</key>
			<data>
			NGo9Y7oLjVb4AS8n5vgMLDZ0uqM=
			</data>
			<key>hash2</key>
			<data>
			IB43SiNSY97Pj+GaCp1vc//zy7TML0KMz0/nrb5NquI=
			</data>
		</dict>
		<key>Headers/GULHeartbeatDateStorable.h</key>
		<dict>
			<key>hash</key>
			<data>
			4OtriHMHMXlkFjw9E9asMucvYBk=
			</data>
			<key>hash2</key>
			<data>
			ZL4+u/A2OdvbAUp+BaAuMCVi16wtPtXr+2KLKvE4JWg=
			</data>
		</dict>
		<key>Headers/GULHeartbeatDateStorage.h</key>
		<dict>
			<key>hash</key>
			<data>
			P/qw3G71G5PCb2AWoGkwoMPbwkg=
			</data>
			<key>hash2</key>
			<data>
			8K7xliqi/Ra8UCIsxcyw25YHd85HTBjTMXO/Lj4+Ruc=
			</data>
		</dict>
		<key>Headers/GULHeartbeatDateStorageUserDefaults.h</key>
		<dict>
			<key>hash</key>
			<data>
			yIxvciZTWSq2QppEk0lfLQw8Fno=
			</data>
			<key>hash2</key>
			<data>
			8WE865QHH0tAjEOd/jVjNJ/AF3AbLWDNrWwDchiYNTY=
			</data>
		</dict>
		<key>Headers/GULKeychainStorage.h</key>
		<dict>
			<key>hash</key>
			<data>
			e286JCIHL6bzzSpelRVlIgbNei8=
			</data>
			<key>hash2</key>
			<data>
			ejcnjNtMQyuFrJx4UBtRMRYpiCQF8Fg6ODBR7UU0Nl4=
			</data>
		</dict>
		<key>Headers/GULKeychainUtils.h</key>
		<dict>
			<key>hash</key>
			<data>
			zbMNKiNQyW5isBVmHY6uTrv0cRk=
			</data>
			<key>hash2</key>
			<data>
			49V5lvDz2dabLTrS9hetsm9Kj4NFbSXarb36YIAHGJg=
			</data>
		</dict>
		<key>Headers/GULLogger.h</key>
		<dict>
			<key>hash</key>
			<data>
			HTjFj96M5thN8ETKvg0g/RjaeVc=
			</data>
			<key>hash2</key>
			<data>
			ct/taMwKmvF8YGpyC+wIiZyK94VTIcHXgiQpEP780Y4=
			</data>
		</dict>
		<key>Headers/GULLoggerLevel.h</key>
		<dict>
			<key>hash</key>
			<data>
			kI/Q5XKyjxY19Tph/lUMaOC09ZE=
			</data>
			<key>hash2</key>
			<data>
			LiP2jwqqzSGdPL/LiTSObUXRHghVvIJYdV2SXCW8M+k=
			</data>
		</dict>
		<key>Headers/GULNSData+zlib.h</key>
		<dict>
			<key>hash</key>
			<data>
			kFWywRhiZKZSTElPxadV/AaoVqA=
			</data>
			<key>hash2</key>
			<data>
			QD8Y96XnfDAahzjrGk4My5T2g8CL4crH1xVR4Hu0PRU=
			</data>
		</dict>
		<key>Headers/GULNetworkInfo.h</key>
		<dict>
			<key>hash</key>
			<data>
			WsAAfsEiwFkuHGHVl5YZZPx/y7g=
			</data>
			<key>hash2</key>
			<data>
			Y+INcPftnbj6InNVUO0nGGarNDqPaAK141x6EAzF3T8=
			</data>
		</dict>
		<key>Headers/GULSecureCoding.h</key>
		<dict>
			<key>hash</key>
			<data>
			NqI3HnVooz9+xSkTFaaO0Qu9wPE=
			</data>
			<key>hash2</key>
			<data>
			3L9JDOv4n3TFP3Gm7AeKtTU8ZIbY0RCDqXIDiY1SBtM=
			</data>
		</dict>
		<key>Headers/GULURLSessionDataResponse.h</key>
		<dict>
			<key>hash</key>
			<data>
			C7k2DFJRl4sOYxgFqiOpnGh1Xho=
			</data>
			<key>hash2</key>
			<data>
			n5k4qaKh2bYI2mwBIAa845pVXUSRxdfJfPyeZ/AIEyA=
			</data>
		</dict>
		<key>Headers/GULUserDefaults.h</key>
		<dict>
			<key>hash</key>
			<data>
			EBGg0ZapNUzyIzb6nCliBREczUA=
			</data>
			<key>hash2</key>
			<data>
			ys09g5WqzZ5D/IaWg2v2AyTadtZDy7y+bS/G95iNTnE=
			</data>
		</dict>
		<key>Headers/GoogleUtilities-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			mboIYYo2v6Dv/J8bhecVbZcL6BI=
			</data>
			<key>hash2</key>
			<data>
			MZe3AbI8yiDfOTAdgHxxKc6F6x2kuwLSvI1ssbPhUeU=
			</data>
		</dict>
		<key>Headers/NSURLSession+GULPromises.h</key>
		<dict>
			<key>hash</key>
			<data>
			2BgzYjJd/EL8eHaeK39hhonnQHk=
			</data>
			<key>hash2</key>
			<data>
			biELhEMquN4g3AwZ15AIB3vcGg5AdJI//dTOZc4+/s8=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			jNNM5/vRCkspln7DuMCSYf0cbB8=
			</data>
			<key>hash2</key>
			<data>
			AWXmNXv9PVCSYOCWhqKqLRqCRav50cANCfQhM5gsw5M=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
