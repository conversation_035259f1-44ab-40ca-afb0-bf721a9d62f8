<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/nanopb-umbrella.h</key>
		<data>
		3AHKVvUiwH5st6WulWFEieJUQMI=
		</data>
		<key>Headers/pb.h</key>
		<data>
		UqjkVSHa0JaGAG6o5iLn2VHyRz8=
		</data>
		<key>Headers/pb_common.h</key>
		<data>
		AdSOtv4HeHxYwkFyKB6MbxGs1Ns=
		</data>
		<key>Headers/pb_decode.h</key>
		<data>
		71k8w9HgFHTP3y98V/i9ExE9UYU=
		</data>
		<key>Headers/pb_encode.h</key>
		<data>
		10Gl/I0zZ+VjzMR3fBWtHOWdRWI=
		</data>
		<key>Info.plist</key>
		<data>
		Tha1gxoU01BPJnZBB8BYsPgA3Ak=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		VE0n+HvVm7YDki/SnnYSYzJdaOE=
		</data>
		<key>nanopb_Privacy.bundle/Info.plist</key>
		<data>
		OA3sJFwib9Bi3HcuOpjI6I3kKHc=
		</data>
		<key>nanopb_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		KY5lfwC2TvsgFj4wt7hkMmainbs=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/nanopb-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			3AHKVvUiwH5st6WulWFEieJUQMI=
			</data>
			<key>hash2</key>
			<data>
			ua0swQdCw9NqdpzW1vnyfp6Sep7qcaFrByRxT8BolY0=
			</data>
		</dict>
		<key>Headers/pb.h</key>
		<dict>
			<key>hash</key>
			<data>
			UqjkVSHa0JaGAG6o5iLn2VHyRz8=
			</data>
			<key>hash2</key>
			<data>
			3LSuMkz44oXe0P+KGiCFLz8t/dsF7FSOw/TEQQ83iTs=
			</data>
		</dict>
		<key>Headers/pb_common.h</key>
		<dict>
			<key>hash</key>
			<data>
			AdSOtv4HeHxYwkFyKB6MbxGs1Ns=
			</data>
			<key>hash2</key>
			<data>
			PtDnUYzSxgTyYx84uo0xGdqu6zxbOcttuWjKVAMtdUE=
			</data>
		</dict>
		<key>Headers/pb_decode.h</key>
		<dict>
			<key>hash</key>
			<data>
			71k8w9HgFHTP3y98V/i9ExE9UYU=
			</data>
			<key>hash2</key>
			<data>
			rf5T82EuZcRWFPEpIm6LAN3nzI8c9+0y61h2WYpbKJQ=
			</data>
		</dict>
		<key>Headers/pb_encode.h</key>
		<dict>
			<key>hash</key>
			<data>
			10Gl/I0zZ+VjzMR3fBWtHOWdRWI=
			</data>
			<key>hash2</key>
			<data>
			dkrujnmALgdV5dKQgyNrFEoK8J2t4B7vx4f6a6p0q4Q=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			VE0n+HvVm7YDki/SnnYSYzJdaOE=
			</data>
			<key>hash2</key>
			<data>
			964ewTQFnBAQHHPXRiTBEL9kMV+4JU4sMGWXVlK03/s=
			</data>
		</dict>
		<key>nanopb_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			OA3sJFwib9Bi3HcuOpjI6I3kKHc=
			</data>
			<key>hash2</key>
			<data>
			1SsIRzgDZV5T8ijxUvNj1SPV8r0f075SlKIv0tFyris=
			</data>
		</dict>
		<key>nanopb_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			KY5lfwC2TvsgFj4wt7hkMmainbs=
			</data>
			<key>hash2</key>
			<data>
			cpujy9D0WMeM1h7fFzUO2v4ONMqG4xTsZMjLIszSG1Q=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
