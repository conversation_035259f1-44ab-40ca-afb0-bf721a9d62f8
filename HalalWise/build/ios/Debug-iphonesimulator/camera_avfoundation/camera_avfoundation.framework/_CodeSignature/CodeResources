<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/CameraProperties.h</key>
		<data>
		kc4LZE3Ysx1FpGXVLokcjLeHU+s=
		</data>
		<key>Headers/FLTAssetWriter.h</key>
		<data>
		jRgn4QO0oXxQzAYPJcdZP3KK238=
		</data>
		<key>Headers/FLTCam.h</key>
		<data>
		wLyU/69JbmKH8XqPtc7X/lZiMgc=
		</data>
		<key>Headers/FLTCamConfiguration.h</key>
		<data>
		6sSQGsE5O46/k1coWbBlLXwCVek=
		</data>
		<key>Headers/FLTCamMediaSettingsAVWrapper.h</key>
		<data>
		MxpEExhzAzTu6nK+NXAj+eRJRtc=
		</data>
		<key>Headers/FLTCam_Test.h</key>
		<data>
		EHwPmiBUIrQre6jOBIcFqFTH/GM=
		</data>
		<key>Headers/FLTCameraDeviceDiscovering.h</key>
		<data>
		X0y7ik7WwszF6fEsQtxWloR5+hM=
		</data>
		<key>Headers/FLTCameraPermissionManager.h</key>
		<data>
		olnPb1iPX2bHnglW/pOwR18HEig=
		</data>
		<key>Headers/FLTCaptureConnection.h</key>
		<data>
		9t6XaTnZeMsP9AUEpvkiDXsl5nI=
		</data>
		<key>Headers/FLTCaptureDevice.h</key>
		<data>
		yjZGt4ExqYX3MR8tbqVjD0bIkBg=
		</data>
		<key>Headers/FLTCaptureDeviceFormat.h</key>
		<data>
		rem3QASr7yiatYSQB+eYmGW+FeI=
		</data>
		<key>Headers/FLTCaptureOutput.h</key>
		<data>
		QDEOJoGoPGjd1k2d2GvUZ91orvg=
		</data>
		<key>Headers/FLTCapturePhotoOutput.h</key>
		<data>
		S0KfolNqYq+hUL/claAiLDk9Nh0=
		</data>
		<key>Headers/FLTCaptureSession.h</key>
		<data>
		p0VoBzlFY20Q9uqav08k0ToI+1Y=
		</data>
		<key>Headers/FLTCaptureVideoDataOutput.h</key>
		<data>
		YcLjm5Y+P8QmBXGcr48/8K7Qmdk=
		</data>
		<key>Headers/FLTDeviceOrientationProviding.h</key>
		<data>
		jqdVivzR8NvCdptQnRMf+j+jPrw=
		</data>
		<key>Headers/FLTEventChannel.h</key>
		<data>
		h/Khx2k5XErcm8X0SW+7hMx9HoA=
		</data>
		<key>Headers/FLTPermissionServicing.h</key>
		<data>
		ksLsuvPrIAQ+V84Vkg7qlasjFgU=
		</data>
		<key>Headers/FLTSavePhotoDelegate.h</key>
		<data>
		4K02d/P8OgweYt88zJ7wZc5RHOA=
		</data>
		<key>Headers/FLTSavePhotoDelegate_Test.h</key>
		<data>
		cpoHtHsCjMMBeWZTd2N7gMsESzI=
		</data>
		<key>Headers/FLTThreadSafeEventChannel.h</key>
		<data>
		K6Jx443DhZDrxBgLUXU/8BPrdp8=
		</data>
		<key>Headers/FLTWritableData.h</key>
		<data>
		gDSn25sdQtimclKGgemf/K4BkPo=
		</data>
		<key>Headers/QueueUtils.h</key>
		<data>
		KOGU20AER+FhexVzx/PxN19bjxc=
		</data>
		<key>Headers/camera_avfoundation-Swift.h</key>
		<data>
		wA/I1m6w8r+P7FXGD2BOBa2UBO0=
		</data>
		<key>Headers/camera_avfoundation-umbrella.h</key>
		<data>
		tzy/kc27kcge0BgwB8hUhTo6hN0=
		</data>
		<key>Headers/camera_avfoundation.h</key>
		<data>
		VD3j7XaZ8omLgPohjQdrO3AvWv8=
		</data>
		<key>Headers/messages.g.h</key>
		<data>
		wFPXE78kejOy1heN9ZEKyBKM9wk=
		</data>
		<key>Info.plist</key>
		<data>
		LFu4VaaP00c0PA8Y2WB0YjMWbmk=
		</data>
		<key>Modules/camera_avfoundation.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		tEUWjI2T9mGqY4jTvEji5gbYIEo=
		</data>
		<key>Modules/camera_avfoundation.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		IKlFS0YV07+3ZPLyHZTL8BeNlnA=
		</data>
		<key>Modules/camera_avfoundation.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/camera_avfoundation.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		xkR24lQzyiLxayJoJ04DB7AC8CU=
		</data>
		<key>Modules/camera_avfoundation.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<data>
		NSmy/wsOFW9tu9cESXYgs0VDuqc=
		</data>
		<key>Modules/camera_avfoundation.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/camera_avfoundation.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		9n4ndxOSfaKzyL/fWx6GSxdpBUE=
		</data>
		<key>Modules/camera_avfoundation.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		J3uDKZJqqQ98ppU1NhMesPLY4iM=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		f/BCznk60PcIkJAOwjVyY+TPuJw=
		</data>
		<key>camera_avfoundation_privacy.bundle/Info.plist</key>
		<data>
		vbEumCCJ2FkqPygHfxkTZvRU+tA=
		</data>
		<key>camera_avfoundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/CameraProperties.h</key>
		<dict>
			<key>hash2</key>
			<data>
			2thGS/DnpvNNfVdAuIDm/wJxe8fFuezwBqzDVBf5eQY=
			</data>
		</dict>
		<key>Headers/FLTAssetWriter.h</key>
		<dict>
			<key>hash2</key>
			<data>
			huPeCGDgSZ26LkeShn7Xoxf1eE5KBX4/PdUUxsM2Ctk=
			</data>
		</dict>
		<key>Headers/FLTCam.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ZyjMY4XbKS1CF/Ax9q0tUol6Qaz+3IIawlXfKvqzpHA=
			</data>
		</dict>
		<key>Headers/FLTCamConfiguration.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ffKZQ43HnJDyqCGh3gzIPinz/jep3t8aZl3U5FiBGnE=
			</data>
		</dict>
		<key>Headers/FLTCamMediaSettingsAVWrapper.h</key>
		<dict>
			<key>hash2</key>
			<data>
			GYBUIxFyx6Wm5Lp0WtBAi5nDYjspLAaPl/wQWyLDX/8=
			</data>
		</dict>
		<key>Headers/FLTCam_Test.h</key>
		<dict>
			<key>hash2</key>
			<data>
			qrV+DSCLYGRKXH8mTePdq5Zr+dMmR6Qes5iADoMCx9I=
			</data>
		</dict>
		<key>Headers/FLTCameraDeviceDiscovering.h</key>
		<dict>
			<key>hash2</key>
			<data>
			0iPxlESQeTzMeNdhoiwtKeGZv4uk4ZQPdUHOh1fUwjQ=
			</data>
		</dict>
		<key>Headers/FLTCameraPermissionManager.h</key>
		<dict>
			<key>hash2</key>
			<data>
			a6dp+5Zm/PDEsFij8FEDgPccY/BZ+Mqp69aXFskH0i4=
			</data>
		</dict>
		<key>Headers/FLTCaptureConnection.h</key>
		<dict>
			<key>hash2</key>
			<data>
			seley78zZBmKJzODWLuNkGjlVyyo+fzPaIVoPc4pPc4=
			</data>
		</dict>
		<key>Headers/FLTCaptureDevice.h</key>
		<dict>
			<key>hash2</key>
			<data>
			uPOalCm0roCau/9N6IwJZqZ2sXJYMqEgKY+ebst2atY=
			</data>
		</dict>
		<key>Headers/FLTCaptureDeviceFormat.h</key>
		<dict>
			<key>hash2</key>
			<data>
			DHEs76Q3JG2scadZCo5HOaJtv848/Nn4kOFDabnOlmo=
			</data>
		</dict>
		<key>Headers/FLTCaptureOutput.h</key>
		<dict>
			<key>hash2</key>
			<data>
			rXh0YQW6mnprDb04LJ9JGbP9JVwiQDHByHd5h1RFiC4=
			</data>
		</dict>
		<key>Headers/FLTCapturePhotoOutput.h</key>
		<dict>
			<key>hash2</key>
			<data>
			uH11HmHr8kGiyoo8uGN+vjwV+GwS9mrNmTGDmLhaOJ4=
			</data>
		</dict>
		<key>Headers/FLTCaptureSession.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ZZSxEBzgQV8TFVabk6HbvfS6pLXnVt4vx4qToVxBgvk=
			</data>
		</dict>
		<key>Headers/FLTCaptureVideoDataOutput.h</key>
		<dict>
			<key>hash2</key>
			<data>
			N9zGp3FlDL7hzjYv+7ozQPoxXG/7+XQPJ/lGLuAWoMM=
			</data>
		</dict>
		<key>Headers/FLTDeviceOrientationProviding.h</key>
		<dict>
			<key>hash2</key>
			<data>
			iq1J8/Ffi7j7xwE0ymG1NmgjIDzpYvf6CxTjNcfWvv4=
			</data>
		</dict>
		<key>Headers/FLTEventChannel.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Wu5Gtgc35Def6RMWI7mdinDclodmBOpEUPhBAeN/wAU=
			</data>
		</dict>
		<key>Headers/FLTPermissionServicing.h</key>
		<dict>
			<key>hash2</key>
			<data>
			JLmZyzVa7zrbzvuS3irUi3ZLYkUHAxi93svm2eQgQkg=
			</data>
		</dict>
		<key>Headers/FLTSavePhotoDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			23ZBSIif8zJWnyYHf7ttYIlU1ehN/Px1RRL0iDOvCK4=
			</data>
		</dict>
		<key>Headers/FLTSavePhotoDelegate_Test.h</key>
		<dict>
			<key>hash2</key>
			<data>
			4Jug5EKMFzLkQAc93Re9vnJwdRwbt7k4ysfsQDIbVlQ=
			</data>
		</dict>
		<key>Headers/FLTThreadSafeEventChannel.h</key>
		<dict>
			<key>hash2</key>
			<data>
			z84LCYmqGkFlL/qtCdYeUSqQgUk1yQWirK5t2azfsek=
			</data>
		</dict>
		<key>Headers/FLTWritableData.h</key>
		<dict>
			<key>hash2</key>
			<data>
			+JrFLv35k534shmfdmH/c3o4Sokf7/LzVFLfXovazoU=
			</data>
		</dict>
		<key>Headers/QueueUtils.h</key>
		<dict>
			<key>hash2</key>
			<data>
			XRZoQvpiNpS7x0DnvleHO9vD1m7GCu8Ow41SAxitGr4=
			</data>
		</dict>
		<key>Headers/camera_avfoundation-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Wla2U8FFK8kg7vKGveoJGV6VJtAzrGV48a8jSghyFsE=
			</data>
		</dict>
		<key>Headers/camera_avfoundation-umbrella.h</key>
		<dict>
			<key>hash2</key>
			<data>
			BYYC3oCXPbkXDHObuCuQBcJTNjQVtl1ZtWBE8Hlg7is=
			</data>
		</dict>
		<key>Headers/camera_avfoundation.h</key>
		<dict>
			<key>hash2</key>
			<data>
			qjbkLo9lOxSJdhH6RJTYGJpLqjakGx4rEPm4VJ9mw74=
			</data>
		</dict>
		<key>Headers/messages.g.h</key>
		<dict>
			<key>hash2</key>
			<data>
			L8ePhlllsBNmXs59IOQ5L/Y1UGr0GkEu8rRKe3rmj4U=
			</data>
		</dict>
		<key>Modules/camera_avfoundation.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			UHhq3D1gcQHRg/6qCTexX6jiiWYb1v4NYqsr9Mc2UF4=
			</data>
		</dict>
		<key>Modules/camera_avfoundation.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			W+gA3HU2o8pCR9c/4KOKuQvwH3qK3o8bJ4OwTwRONNQ=
			</data>
		</dict>
		<key>Modules/camera_avfoundation.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/camera_avfoundation.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			eaTEJJHQz0CDoimv4WM6fyAl5yYO4+sIRzdnb8JXSHA=
			</data>
		</dict>
		<key>Modules/camera_avfoundation.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			7HRqyd+3bO1OeL+IJ8nBpTg7SLeBMKYGslYQCnCrG+Q=
			</data>
		</dict>
		<key>Modules/camera_avfoundation.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/camera_avfoundation.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			rjywL3jqfJ7zSZ56lplKfDk5GCnCG4MagOmCu676tIs=
			</data>
		</dict>
		<key>Modules/camera_avfoundation.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			965ygCmsbN3lPs34xuyp9u4mkyLJoP3yeL6l/paTAos=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			ir9GRZ1AgIucoltUYSA5O/j/RCDOXaswlrq1+GhW968=
			</data>
		</dict>
		<key>camera_avfoundation_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			yu/xGshu3limjtu4Hp4Xzxq+NO6jasHZnU9moSGwFdo=
			</data>
		</dict>
		<key>camera_avfoundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
