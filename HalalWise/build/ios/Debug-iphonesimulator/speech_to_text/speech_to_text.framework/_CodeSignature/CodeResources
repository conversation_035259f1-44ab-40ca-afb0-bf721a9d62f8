<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/SpeechToTextPlugin.h</key>
		<data>
		zBbLuZAgov/9+ht69U0Qx4gsT9g=
		</data>
		<key>Headers/speech_to_text-Swift.h</key>
		<data>
		PB6qRoCciObEn8fJd9yksSKTOKY=
		</data>
		<key>Headers/speech_to_text-umbrella.h</key>
		<data>
		iPfYcX80VptAqmLiY1bNOBaLivk=
		</data>
		<key>Info.plist</key>
		<data>
		CyPPz5SGoUTsR030rLN4ncZtfX0=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		8RWCe7HcEvFd8N5t0iFxnZEDpfc=
		</data>
		<key>Modules/speech_to_text.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		UOjoLQfq6Hojv4JZVQowSwkd6+E=
		</data>
		<key>Modules/speech_to_text.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		4e//SSDfHTxBsZg3EpfCtT/St80=
		</data>
		<key>Modules/speech_to_text.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/speech_to_text.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		fBr4CwTsGF1hguiCLVShYviRNPc=
		</data>
		<key>Modules/speech_to_text.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<data>
		lYHmSks6+JrzdpXfjo0rlSFzr1U=
		</data>
		<key>Modules/speech_to_text.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/speech_to_text.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		5H/lCp2x7QrForxBLdsQ2rLd1ik=
		</data>
		<key>Modules/speech_to_text.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		pO1DNIGoVma/XDim9y5idKy4V4M=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/SpeechToTextPlugin.h</key>
		<dict>
			<key>hash2</key>
			<data>
			jX9mtDMl6OAgTQ9piim6IhdspR/xxVWjKoLVkF57T9g=
			</data>
		</dict>
		<key>Headers/speech_to_text-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			yUWWhYCCqbAP1p3ZupjXfeGItclV4eLlDesPd07QVNg=
			</data>
		</dict>
		<key>Headers/speech_to_text-umbrella.h</key>
		<dict>
			<key>hash2</key>
			<data>
			91Gr05A9Jpiihap6tSMX9jlOeUpyGMbrVFmhODJc6gI=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			aInzDjrJ/ApaV2I2XoRi+Lo4802BL2JRSyYI3fHJrYQ=
			</data>
		</dict>
		<key>Modules/speech_to_text.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			q4P3MaPYr0+ERC05x6HtS1toO6QU9D4Cg/1d1SGbwew=
			</data>
		</dict>
		<key>Modules/speech_to_text.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			FYtovgEizurxnYQeVeVbKaeSZqfGp7KF3oK58X9OTIY=
			</data>
		</dict>
		<key>Modules/speech_to_text.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/speech_to_text.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			/WRC0ozPZS8ekjMoc6oKosdlJzPkkAyIfnuPqn/oqqw=
			</data>
		</dict>
		<key>Modules/speech_to_text.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			0NJqBCSQBywEsC+5Ssm4dvHm9M6DexPs0KAgi/DgumY=
			</data>
		</dict>
		<key>Modules/speech_to_text.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/speech_to_text.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			/1+mkJrRf7Ps/33eUcCNq8wdwyauwgjXkJg5x3FOsqU=
			</data>
		</dict>
		<key>Modules/speech_to_text.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			ipSRpZYN2denikVpNYZLBHA00BMRVrn9SdYNlZcgNCo=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
