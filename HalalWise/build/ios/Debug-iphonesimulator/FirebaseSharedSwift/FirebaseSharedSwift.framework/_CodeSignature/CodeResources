<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/FirebaseSharedSwift-Swift.h</key>
		<data>
		f0Ju1Wh0H7hZyKA5q+3j2itIqsc=
		</data>
		<key>Headers/FirebaseSharedSwift-umbrella.h</key>
		<data>
		9qxJhorb4veUGKIINskewUk+oM8=
		</data>
		<key>Info.plist</key>
		<data>
		xjHaH5XR4hS9TZr+QRt+B7TJg9w=
		</data>
		<key>Modules/FirebaseSharedSwift.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		uvPOJtGRlEjPwG4KS5oHDJnz9wc=
		</data>
		<key>Modules/FirebaseSharedSwift.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		rBMyGtJMOmXtGDZzyc2cWcFZpd0=
		</data>
		<key>Modules/FirebaseSharedSwift.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/FirebaseSharedSwift.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		8O/kEovpnCJTadkQvwH1bwyxmlU=
		</data>
		<key>Modules/FirebaseSharedSwift.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<data>
		Q7806QCvoI1nqudVKPmsYpm2Uxs=
		</data>
		<key>Modules/FirebaseSharedSwift.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/FirebaseSharedSwift.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		fyCARna/oGgOmK9vP77sqkY4QOQ=
		</data>
		<key>Modules/FirebaseSharedSwift.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		LW0qFjCuEwT0HZOxKmEABqItnzQ=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		aT4kXIhj5PrKN2CajzKUbFrZ1nY=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/FirebaseSharedSwift-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			gbp3427H6BXeFXBLmkhgOY1x/ASSrAHyxLBHNvPBdMk=
			</data>
		</dict>
		<key>Headers/FirebaseSharedSwift-umbrella.h</key>
		<dict>
			<key>hash2</key>
			<data>
			wWsq6y/tVwbb21bkRbZp1AqcxpEQui2S4eQR4ZWfnVA=
			</data>
		</dict>
		<key>Modules/FirebaseSharedSwift.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			c+YAFyJXZOvpbuj1PJ9Am9CxaUS/PbXKuLpWvDdcLJM=
			</data>
		</dict>
		<key>Modules/FirebaseSharedSwift.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			ZD5ktmYlVONkBuUxxcWaHNKy35wPrS8LiwhEzkrFMy8=
			</data>
		</dict>
		<key>Modules/FirebaseSharedSwift.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/FirebaseSharedSwift.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			+VeJ+5vxBKKX6VQt+aulz/KuCgwghtyvzxGOvZgcS4Q=
			</data>
		</dict>
		<key>Modules/FirebaseSharedSwift.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			56TUUGLNR1fm0+wROjPrTcu1uHn9uxlZRUiA1Xd26zA=
			</data>
		</dict>
		<key>Modules/FirebaseSharedSwift.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/FirebaseSharedSwift.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			84XZMnwZGSAl6pqTkIit3Gft2VnMNHU1RMtrw7i5dyI=
			</data>
		</dict>
		<key>Modules/FirebaseSharedSwift.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			wT7hZPvHLO7FA/EBNlucv7KSBnfS7sbSEaz7awZRkrw=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			FBOYiSNaYJpXLbdr8XQv1IUCe/HxQ0YzFG/HQF01BYc=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
