<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/Try-Swift.h</key>
		<data>
		cQQiKLDTqVQJ5m9lJPbqc8PshfI=
		</data>
		<key>Headers/Try-umbrella.h</key>
		<data>
		I4qxpriUUszT9loriCtd2TdpbBs=
		</data>
		<key>Headers/WBTry.h</key>
		<data>
		JsrUbTblYVDwe++4sCsOkiHGKGw=
		</data>
		<key>Info.plist</key>
		<data>
		eF+2HtLuTfwcEgMlvWB8dA+CH7I=
		</data>
		<key>Modules/Try.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		82JnKsZCMeWAJDK8ABOAACyRN90=
		</data>
		<key>Modules/Try.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		oUj3A7IpUxOqsjlL6Sx3ZKSzXNY=
		</data>
		<key>Modules/Try.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/Try.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		of0JHX/e7KUN+nA5+/toN0NCemg=
		</data>
		<key>Modules/Try.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<data>
		DPJr/O+xzYVDLx+PUXNtAXDGrPY=
		</data>
		<key>Modules/Try.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/Try.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		d70okUQJAaquRG1YZ0PSbKgEKNw=
		</data>
		<key>Modules/Try.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		ug06VhgdXWfJkM86w0alb9297cY=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		wVEAQyZ/vHGIgQap/bmnBQ9MVHc=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/Try-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			cQQiKLDTqVQJ5m9lJPbqc8PshfI=
			</data>
			<key>hash2</key>
			<data>
			lpGsmtmSJ4mJPFSzRfvbaO2YfW1DgT52Cs2UKa5SAcc=
			</data>
		</dict>
		<key>Headers/Try-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			I4qxpriUUszT9loriCtd2TdpbBs=
			</data>
			<key>hash2</key>
			<data>
			+aQQaQaFTTVG/ZxM5yxbnGEYWTpWfsWnBMGlq/j+KJg=
			</data>
		</dict>
		<key>Headers/WBTry.h</key>
		<dict>
			<key>hash</key>
			<data>
			JsrUbTblYVDwe++4sCsOkiHGKGw=
			</data>
			<key>hash2</key>
			<data>
			4Psb1tbUQ+WV4jr5ZiG4Bv/HfvC5pVrJsKf7/+qE7jc=
			</data>
		</dict>
		<key>Modules/Try.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			82JnKsZCMeWAJDK8ABOAACyRN90=
			</data>
			<key>hash2</key>
			<data>
			BaPyPANxs4nIW+aRPTy6/sjIsePWyKCqk8oHOaFI1f0=
			</data>
		</dict>
		<key>Modules/Try.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			oUj3A7IpUxOqsjlL6Sx3ZKSzXNY=
			</data>
			<key>hash2</key>
			<data>
			qTB1F6nXft7GSvs193H8gFmei+Upfxq9qZ7UFP+E5Rk=
			</data>
		</dict>
		<key>Modules/Try.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			gcwBsH4BgyFY4sVtNt+/xOKS3vY=
			</data>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/Try.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			of0JHX/e7KUN+nA5+/toN0NCemg=
			</data>
			<key>hash2</key>
			<data>
			4zStj44gERcP16xjw5qlYw2hUjBp4JQUCcSb2MYfKUA=
			</data>
		</dict>
		<key>Modules/Try.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			DPJr/O+xzYVDLx+PUXNtAXDGrPY=
			</data>
			<key>hash2</key>
			<data>
			OvoIMytFbP6gVOoRtyGQ9BvDgPtKyHV38+fmv9IQ4Z8=
			</data>
		</dict>
		<key>Modules/Try.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			gcwBsH4BgyFY4sVtNt+/xOKS3vY=
			</data>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/Try.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			d70okUQJAaquRG1YZ0PSbKgEKNw=
			</data>
			<key>hash2</key>
			<data>
			M9MG426mWX4cMnfFlPk2y6LbfLxZrPME7hrm2q6tKw0=
			</data>
		</dict>
		<key>Modules/Try.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			ug06VhgdXWfJkM86w0alb9297cY=
			</data>
			<key>hash2</key>
			<data>
			etpoQEReiC+N4qm38WexjM8Ohi69a0DoGrnKa48MQFs=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			wVEAQyZ/vHGIgQap/bmnBQ9MVHc=
			</data>
			<key>hash2</key>
			<data>
			ecwFF93KxYpg8d4/tTsdPXDim0NL5001sq4h8ZmKXhM=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
