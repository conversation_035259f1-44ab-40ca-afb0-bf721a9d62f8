// Protocol Buffers - Google's data interchange format
// Copyright 2023 Google LLC.  All rights reserved.
//
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file or at
// https://developers.google.com/open-source/licenses/bsd

#ifndef UPB_REFLECTION_DEF_H_
#define UPB_REFLECTION_DEF_H_

// IWYU pragma: begin_exports
#include "upb/reflection/def_pool.h"
#include "upb/reflection/enum_def.h"
#include "upb/reflection/enum_value_def.h"
#include "upb/reflection/extension_range.h"
#include "upb/reflection/field_def.h"
#include "upb/reflection/file_def.h"
#include "upb/reflection/message_def.h"
#include "upb/reflection/method_def.h"
#include "upb/reflection/oneof_def.h"
#include "upb/reflection/service_def.h"
// IWYU pragma: end_exports

#endif /* UPB_REFLECTION_DEF_H_ */
