// Protocol Buffers - Google's data interchange format
// Copyright 2023 Google LLC.  All rights reserved.
//
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file or at
// https://developers.google.com/open-source/licenses/bsd

#ifndef UPB_REFLECTION_MESSAGE_HPP_
#define UPB_REFLECTION_MESSAGE_HPP_

#include "upb/reflection/message.h"

namespace upb {

typedef upb_MessageValue MessageValue;

}  // namespace upb

#endif  // UPB_REFLECTION_MESSAGE_HPP_
