// Protocol Buffers - Google's data interchange format
// Copyright 2023 Google LLC.  All rights reserved.
//
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file or at
// https://developers.google.com/open-source/licenses/bsd

#ifndef UPB_REFLECTION_UPB_EDITION_DEFAULTS_H_
#define UPB_REFLECTION_UPB_EDITION_DEFAULTS_H_

// This file contains the serialized FeatureSetDefaults object for
// language-independent features and (possibly at some point) for upb-specific
// features. This is used for feature resolution under Editions.
// NOLINTBEGIN
// clang-format off
#define UPB_INTERNAL_UPB_EDITION_DEFAULTS "\n\023\030\204\007\"\000*\014\010\001\020\002\030\002 \003(\0010\002\n\023\030\347\007\"\000*\014\010\002\020\001\030\001 \002(\0010\001\n\023\030\350\007\"\014\010\001\020\001\030\001 \002(\0010\001*\000 \346\007(\350\007"
// clang-format on
// NOLINTEND

#endif  // UPB_REFLECTION_UPB_EDITION_DEFAULTS_H_
