/* This file was generated by upb_generator from the input file:
 *
 *     envoy/config/route/v3/route_components.proto
 *
 * Do not edit -- your changes will be discarded when the file is
 * regenerated.
 * NO CHECKED-IN PROTOBUF GENCODE */

#ifndef ENVOY_CONFIG_ROUTE_V3_ROUTE_COMPONENTS_PROTO_UPB_H__UPBDEFS_H_
#define ENVOY_CONFIG_ROUTE_V3_ROUTE_COMPONENTS_PROTO_UPB_H__UPBDEFS_H_

#include "upb/reflection/def.h"
#include "upb/reflection/internal/def_pool.h"

#include "upb/port/def.inc" // Must be last.
#ifdef __cplusplus
extern "C" {
#endif

extern _upb_DefPool_Init envoy_config_route_v3_route_components_proto_upbdefinit;

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_VirtualHost_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.VirtualHost");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_VirtualHost_TypedPerFilterConfigEntry_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.VirtualHost.TypedPerFilterConfigEntry");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_FilterAction_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.FilterAction");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RouteList_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RouteList");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_Route_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.Route");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_Route_TypedPerFilterConfigEntry_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.Route.TypedPerFilterConfigEntry");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_WeightedCluster_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.WeightedCluster");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_WeightedCluster_ClusterWeight_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.WeightedCluster.ClusterWeight");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_WeightedCluster_ClusterWeight_TypedPerFilterConfigEntry_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.WeightedCluster.ClusterWeight.TypedPerFilterConfigEntry");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_ClusterSpecifierPlugin_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.ClusterSpecifierPlugin");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RouteMatch_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RouteMatch");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RouteMatch_GrpcRouteMatchOptions_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RouteMatch.GrpcRouteMatchOptions");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RouteMatch_TlsContextMatchOptions_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RouteMatch.TlsContextMatchOptions");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RouteMatch_ConnectMatcher_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RouteMatch.ConnectMatcher");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_CorsPolicy_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.CorsPolicy");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RouteAction_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RouteAction");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RouteAction_RequestMirrorPolicy_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RouteAction.RequestMirrorPolicy");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RouteAction_HashPolicy_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RouteAction.HashPolicy");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RouteAction_HashPolicy_Header_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RouteAction.HashPolicy.Header");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RouteAction_HashPolicy_CookieAttribute_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RouteAction.HashPolicy.CookieAttribute");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RouteAction_HashPolicy_Cookie_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RouteAction.HashPolicy.Cookie");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RouteAction_HashPolicy_ConnectionProperties_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RouteAction.HashPolicy.ConnectionProperties");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RouteAction_HashPolicy_QueryParameter_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RouteAction.HashPolicy.QueryParameter");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RouteAction_HashPolicy_FilterState_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RouteAction.HashPolicy.FilterState");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RouteAction_UpgradeConfig_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RouteAction.UpgradeConfig");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RouteAction_UpgradeConfig_ConnectConfig_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RouteAction.UpgradeConfig.ConnectConfig");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RouteAction_MaxStreamDuration_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RouteAction.MaxStreamDuration");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RetryPolicy_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RetryPolicy");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RetryPolicy_RetryPriority_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RetryPolicy.RetryPriority");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RetryPolicy_RetryHostPredicate_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RetryPolicy.RetryHostPredicate");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RetryPolicy_RetryBackOff_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RetryPolicy.RetryBackOff");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RetryPolicy_ResetHeader_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RetryPolicy.ResetHeader");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RetryPolicy_RateLimitedRetryBackOff_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RetryPolicy.RateLimitedRetryBackOff");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_HedgePolicy_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.HedgePolicy");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RedirectAction_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RedirectAction");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_DirectResponseAction_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.DirectResponseAction");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_NonForwardingAction_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.NonForwardingAction");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_Decorator_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.Decorator");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_Tracing_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.Tracing");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_VirtualCluster_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.VirtualCluster");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RateLimit_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RateLimit");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RateLimit_Action_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RateLimit.Action");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RateLimit_Action_SourceCluster_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RateLimit.Action.SourceCluster");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RateLimit_Action_DestinationCluster_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RateLimit.Action.DestinationCluster");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RateLimit_Action_RequestHeaders_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RateLimit.Action.RequestHeaders");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RateLimit_Action_RemoteAddress_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RateLimit.Action.RemoteAddress");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RateLimit_Action_MaskedRemoteAddress_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RateLimit.Action.MaskedRemoteAddress");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RateLimit_Action_GenericKey_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RateLimit.Action.GenericKey");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RateLimit_Action_HeaderValueMatch_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RateLimit.Action.HeaderValueMatch");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RateLimit_Action_DynamicMetaData_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RateLimit.Action.DynamicMetaData");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RateLimit_Action_MetaData_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RateLimit.Action.MetaData");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RateLimit_Action_QueryParameterValueMatch_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RateLimit.Action.QueryParameterValueMatch");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RateLimit_Override_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RateLimit.Override");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_RateLimit_Override_DynamicMetadata_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.RateLimit.Override.DynamicMetadata");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_HeaderMatcher_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.HeaderMatcher");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_QueryParameterMatcher_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.QueryParameterMatcher");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_InternalRedirectPolicy_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.InternalRedirectPolicy");
}

UPB_INLINE const upb_MessageDef *envoy_config_route_v3_FilterConfig_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_route_v3_route_components_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.route.v3.FilterConfig");
}

#ifdef __cplusplus
}  /* extern "C" */
#endif

#include "upb/port/undef.inc"

#endif  /* ENVOY_CONFIG_ROUTE_V3_ROUTE_COMPONENTS_PROTO_UPB_H__UPBDEFS_H_ */
