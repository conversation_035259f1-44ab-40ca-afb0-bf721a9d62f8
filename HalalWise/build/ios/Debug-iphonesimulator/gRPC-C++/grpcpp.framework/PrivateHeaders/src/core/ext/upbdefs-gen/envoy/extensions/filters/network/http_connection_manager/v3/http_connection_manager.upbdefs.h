/* This file was generated by upb_generator from the input file:
 *
 *     envoy/extensions/filters/network/http_connection_manager/v3/http_connection_manager.proto
 *
 * Do not edit -- your changes will be discarded when the file is
 * regenerated.
 * NO CHECKED-IN PROTOBUF GENCODE */

#ifndef ENVOY_EXTENSIONS_FILTERS_NETWORK_HTTP_CONNECTION_MANAGER_V3_HTTP_CONNECTION_MANAGER_PROTO_UPB_H__UPBDEFS_H_
#define ENVOY_EXTENSIONS_FILTERS_NETWORK_HTTP_CONNECTION_MANAGER_V3_HTTP_CONNECTION_MANAGER_PROTO_UPB_H__UPBDEFS_H_

#include "upb/reflection/def.h"
#include "upb/reflection/internal/def_pool.h"

#include "upb/port/def.inc" // Must be last.
#ifdef __cplusplus
extern "C" {
#endif

extern _upb_DefPool_Init envoy_extensions_filters_network_http_connection_manager_v3_http_connection_manager_proto_upbdefinit;

UPB_INLINE const upb_MessageDef *envoy_extensions_filters_network_http_connection_manager_v3_HttpConnectionManager_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_extensions_filters_network_http_connection_manager_v3_http_connection_manager_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager");
}

UPB_INLINE const upb_MessageDef *envoy_extensions_filters_network_http_connection_manager_v3_HttpConnectionManager_Tracing_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_extensions_filters_network_http_connection_manager_v3_http_connection_manager_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager.Tracing");
}

UPB_INLINE const upb_MessageDef *envoy_extensions_filters_network_http_connection_manager_v3_HttpConnectionManager_InternalAddressConfig_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_extensions_filters_network_http_connection_manager_v3_http_connection_manager_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager.InternalAddressConfig");
}

UPB_INLINE const upb_MessageDef *envoy_extensions_filters_network_http_connection_manager_v3_HttpConnectionManager_SetCurrentClientCertDetails_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_extensions_filters_network_http_connection_manager_v3_http_connection_manager_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager.SetCurrentClientCertDetails");
}

UPB_INLINE const upb_MessageDef *envoy_extensions_filters_network_http_connection_manager_v3_HttpConnectionManager_UpgradeConfig_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_extensions_filters_network_http_connection_manager_v3_http_connection_manager_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager.UpgradeConfig");
}

UPB_INLINE const upb_MessageDef *envoy_extensions_filters_network_http_connection_manager_v3_HttpConnectionManager_PathNormalizationOptions_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_extensions_filters_network_http_connection_manager_v3_http_connection_manager_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager.PathNormalizationOptions");
}

UPB_INLINE const upb_MessageDef *envoy_extensions_filters_network_http_connection_manager_v3_HttpConnectionManager_ProxyStatusConfig_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_extensions_filters_network_http_connection_manager_v3_http_connection_manager_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager.ProxyStatusConfig");
}

UPB_INLINE const upb_MessageDef *envoy_extensions_filters_network_http_connection_manager_v3_HttpConnectionManager_HcmAccessLogOptions_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_extensions_filters_network_http_connection_manager_v3_http_connection_manager_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager.HcmAccessLogOptions");
}

UPB_INLINE const upb_MessageDef *envoy_extensions_filters_network_http_connection_manager_v3_LocalReplyConfig_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_extensions_filters_network_http_connection_manager_v3_http_connection_manager_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.extensions.filters.network.http_connection_manager.v3.LocalReplyConfig");
}

UPB_INLINE const upb_MessageDef *envoy_extensions_filters_network_http_connection_manager_v3_ResponseMapper_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_extensions_filters_network_http_connection_manager_v3_http_connection_manager_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.extensions.filters.network.http_connection_manager.v3.ResponseMapper");
}

UPB_INLINE const upb_MessageDef *envoy_extensions_filters_network_http_connection_manager_v3_Rds_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_extensions_filters_network_http_connection_manager_v3_http_connection_manager_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.extensions.filters.network.http_connection_manager.v3.Rds");
}

UPB_INLINE const upb_MessageDef *envoy_extensions_filters_network_http_connection_manager_v3_ScopedRouteConfigurationsList_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_extensions_filters_network_http_connection_manager_v3_http_connection_manager_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.extensions.filters.network.http_connection_manager.v3.ScopedRouteConfigurationsList");
}

UPB_INLINE const upb_MessageDef *envoy_extensions_filters_network_http_connection_manager_v3_ScopedRoutes_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_extensions_filters_network_http_connection_manager_v3_http_connection_manager_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.extensions.filters.network.http_connection_manager.v3.ScopedRoutes");
}

UPB_INLINE const upb_MessageDef *envoy_extensions_filters_network_http_connection_manager_v3_ScopedRoutes_ScopeKeyBuilder_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_extensions_filters_network_http_connection_manager_v3_http_connection_manager_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.extensions.filters.network.http_connection_manager.v3.ScopedRoutes.ScopeKeyBuilder");
}

UPB_INLINE const upb_MessageDef *envoy_extensions_filters_network_http_connection_manager_v3_ScopedRoutes_ScopeKeyBuilder_FragmentBuilder_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_extensions_filters_network_http_connection_manager_v3_http_connection_manager_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.extensions.filters.network.http_connection_manager.v3.ScopedRoutes.ScopeKeyBuilder.FragmentBuilder");
}

UPB_INLINE const upb_MessageDef *envoy_extensions_filters_network_http_connection_manager_v3_ScopedRoutes_ScopeKeyBuilder_FragmentBuilder_HeaderValueExtractor_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_extensions_filters_network_http_connection_manager_v3_http_connection_manager_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.extensions.filters.network.http_connection_manager.v3.ScopedRoutes.ScopeKeyBuilder.FragmentBuilder.HeaderValueExtractor");
}

UPB_INLINE const upb_MessageDef *envoy_extensions_filters_network_http_connection_manager_v3_ScopedRoutes_ScopeKeyBuilder_FragmentBuilder_HeaderValueExtractor_KvElement_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_extensions_filters_network_http_connection_manager_v3_http_connection_manager_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.extensions.filters.network.http_connection_manager.v3.ScopedRoutes.ScopeKeyBuilder.FragmentBuilder.HeaderValueExtractor.KvElement");
}

UPB_INLINE const upb_MessageDef *envoy_extensions_filters_network_http_connection_manager_v3_ScopedRds_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_extensions_filters_network_http_connection_manager_v3_http_connection_manager_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.extensions.filters.network.http_connection_manager.v3.ScopedRds");
}

UPB_INLINE const upb_MessageDef *envoy_extensions_filters_network_http_connection_manager_v3_HttpFilter_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_extensions_filters_network_http_connection_manager_v3_http_connection_manager_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.extensions.filters.network.http_connection_manager.v3.HttpFilter");
}

UPB_INLINE const upb_MessageDef *envoy_extensions_filters_network_http_connection_manager_v3_RequestIDExtension_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_extensions_filters_network_http_connection_manager_v3_http_connection_manager_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.extensions.filters.network.http_connection_manager.v3.RequestIDExtension");
}

UPB_INLINE const upb_MessageDef *envoy_extensions_filters_network_http_connection_manager_v3_EnvoyMobileHttpConnectionManager_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_extensions_filters_network_http_connection_manager_v3_http_connection_manager_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.extensions.filters.network.http_connection_manager.v3.EnvoyMobileHttpConnectionManager");
}

#ifdef __cplusplus
}  /* extern "C" */
#endif

#include "upb/port/undef.inc"

#endif  /* ENVOY_EXTENSIONS_FILTERS_NETWORK_HTTP_CONNECTION_MANAGER_V3_HTTP_CONNECTION_MANAGER_PROTO_UPB_H__UPBDEFS_H_ */
