/* This file was generated by upb_generator from the input file:
 *
 *     xds/annotations/v3/status.proto
 *
 * Do not edit -- your changes will be discarded when the file is
 * regenerated.
 * NO CHECKED-IN PROTOBUF GENCODE */

#ifndef XDS_ANNOTATIONS_V3_STATUS_PROTO_UPB_H__UPB_MINITABLE_H_
#define XDS_ANNOTATIONS_V3_STATUS_PROTO_UPB_H__UPB_MINITABLE_H_

#include "upb/generated_code_support.h"

// Must be last.
#include "upb/port/def.inc"

#ifdef __cplusplus
extern "C" {
#endif

extern const upb_MiniTable xds__annotations__v3__FileStatusAnnotation_msg_init;
extern const upb_MiniTable* xds__annotations__v3__FileStatusAnnotation_msg_init_ptr;
extern const upb_MiniTable xds__annotations__v3__MessageStatusAnnotation_msg_init;
extern const upb_MiniTable* xds__annotations__v3__MessageStatusAnnotation_msg_init_ptr;
extern const upb_MiniTable xds__annotations__v3__FieldStatusAnnotation_msg_init;
extern const upb_MiniTable* xds__annotations__v3__FieldStatusAnnotation_msg_init_ptr;
extern const upb_MiniTable xds__annotations__v3__StatusAnnotation_msg_init;
extern const upb_MiniTable* xds__annotations__v3__StatusAnnotation_msg_init_ptr;
extern const upb_MiniTableExtension xds_annotations_v3_file_status_ext;
extern const upb_MiniTableExtension xds_annotations_v3_message_status_ext;
extern const upb_MiniTableExtension xds_annotations_v3_field_status_ext;

extern const upb_MiniTableFile xds_annotations_v3_status_proto_upb_file_layout;

#ifdef __cplusplus
}  /* extern "C" */
#endif

#include "upb/port/undef.inc"

#endif  /* XDS_ANNOTATIONS_V3_STATUS_PROTO_UPB_H__UPB_MINITABLE_H_ */
