/* This file was generated by upb_generator from the input file:
 *
 *     validate/validate.proto
 *
 * Do not edit -- your changes will be discarded when the file is
 * regenerated.
 * NO CHECKED-IN PROTOBUF GENCODE */

#ifndef VALIDATE_VALIDATE_PROTO_UPB_H__UPB_MINITABLE_H_
#define VALIDATE_VALIDATE_PROTO_UPB_H__UPB_MINITABLE_H_

#include "upb/generated_code_support.h"

// Must be last.
#include "upb/port/def.inc"

#ifdef __cplusplus
extern "C" {
#endif

extern const upb_MiniTable validate__FieldRules_msg_init;
extern const upb_MiniTable* validate__FieldRules_msg_init_ptr;
extern const upb_MiniTable validate__FloatRules_msg_init;
extern const upb_MiniTable* validate__FloatRules_msg_init_ptr;
extern const upb_MiniTable validate__DoubleRules_msg_init;
extern const upb_MiniTable* validate__DoubleRules_msg_init_ptr;
extern const upb_MiniTable validate__Int32Rules_msg_init;
extern const upb_MiniTable* validate__Int32Rules_msg_init_ptr;
extern const upb_MiniTable validate__Int64Rules_msg_init;
extern const upb_MiniTable* validate__Int64Rules_msg_init_ptr;
extern const upb_MiniTable validate__UInt32Rules_msg_init;
extern const upb_MiniTable* validate__UInt32Rules_msg_init_ptr;
extern const upb_MiniTable validate__UInt64Rules_msg_init;
extern const upb_MiniTable* validate__UInt64Rules_msg_init_ptr;
extern const upb_MiniTable validate__SInt32Rules_msg_init;
extern const upb_MiniTable* validate__SInt32Rules_msg_init_ptr;
extern const upb_MiniTable validate__SInt64Rules_msg_init;
extern const upb_MiniTable* validate__SInt64Rules_msg_init_ptr;
extern const upb_MiniTable validate__Fixed32Rules_msg_init;
extern const upb_MiniTable* validate__Fixed32Rules_msg_init_ptr;
extern const upb_MiniTable validate__Fixed64Rules_msg_init;
extern const upb_MiniTable* validate__Fixed64Rules_msg_init_ptr;
extern const upb_MiniTable validate__SFixed32Rules_msg_init;
extern const upb_MiniTable* validate__SFixed32Rules_msg_init_ptr;
extern const upb_MiniTable validate__SFixed64Rules_msg_init;
extern const upb_MiniTable* validate__SFixed64Rules_msg_init_ptr;
extern const upb_MiniTable validate__BoolRules_msg_init;
extern const upb_MiniTable* validate__BoolRules_msg_init_ptr;
extern const upb_MiniTable validate__StringRules_msg_init;
extern const upb_MiniTable* validate__StringRules_msg_init_ptr;
extern const upb_MiniTable validate__BytesRules_msg_init;
extern const upb_MiniTable* validate__BytesRules_msg_init_ptr;
extern const upb_MiniTable validate__EnumRules_msg_init;
extern const upb_MiniTable* validate__EnumRules_msg_init_ptr;
extern const upb_MiniTable validate__MessageRules_msg_init;
extern const upb_MiniTable* validate__MessageRules_msg_init_ptr;
extern const upb_MiniTable validate__RepeatedRules_msg_init;
extern const upb_MiniTable* validate__RepeatedRules_msg_init_ptr;
extern const upb_MiniTable validate__MapRules_msg_init;
extern const upb_MiniTable* validate__MapRules_msg_init_ptr;
extern const upb_MiniTable validate__AnyRules_msg_init;
extern const upb_MiniTable* validate__AnyRules_msg_init_ptr;
extern const upb_MiniTable validate__DurationRules_msg_init;
extern const upb_MiniTable* validate__DurationRules_msg_init_ptr;
extern const upb_MiniTable validate__TimestampRules_msg_init;
extern const upb_MiniTable* validate__TimestampRules_msg_init_ptr;
extern const upb_MiniTableExtension validate_disabled_ext;
extern const upb_MiniTableExtension validate_ignored_ext;
extern const upb_MiniTableExtension validate_required_ext;
extern const upb_MiniTableExtension validate_rules_ext;

extern const upb_MiniTableEnum validate__KnownRegex_enum_init;
extern const upb_MiniTableFile validate_validate_proto_upb_file_layout;

#ifdef __cplusplus
}  /* extern "C" */
#endif

#include "upb/port/undef.inc"

#endif  /* VALIDATE_VALIDATE_PROTO_UPB_H__UPB_MINITABLE_H_ */
