/* This file was generated by upb_generator from the input file:
 *
 *     xds/core/v3/cidr.proto
 *
 * Do not edit -- your changes will be discarded when the file is
 * regenerated.
 * NO CHECKED-IN PROTOBUF GENCODE */

#ifndef XDS_CORE_V3_CIDR_PROTO_UPB_H__UPB_MINITABLE_H_
#define XDS_CORE_V3_CIDR_PROTO_UPB_H__UPB_MINITABLE_H_

#include "upb/generated_code_support.h"

// Must be last.
#include "upb/port/def.inc"

#ifdef __cplusplus
extern "C" {
#endif

extern const upb_MiniTable xds__core__v3__CidrRange_msg_init;
extern const upb_MiniTable* xds__core__v3__CidrRange_msg_init_ptr;

extern const upb_MiniTableFile xds_core_v3_cidr_proto_upb_file_layout;

#ifdef __cplusplus
}  /* extern "C" */
#endif

#include "upb/port/undef.inc"

#endif  /* XDS_CORE_V3_CIDR_PROTO_UPB_H__UPB_MINITABLE_H_ */
