/* This file was generated by upb_generator from the input file:
 *
 *     google/api/annotations.proto
 *
 * Do not edit -- your changes will be discarded when the file is
 * regenerated.
 * NO CHECKED-IN PROTOBUF GENCODE */

#ifndef GOOGLE_API_ANNOTATIONS_PROTO_UPB_H__UPBDEFS_H_
#define GOOGLE_API_ANNOTATIONS_PROTO_UPB_H__UPBDEFS_H_

#include "upb/reflection/def.h"
#include "upb/reflection/internal/def_pool.h"

#include "upb/port/def.inc" // Must be last.
#ifdef __cplusplus
extern "C" {
#endif

extern _upb_DefPool_Init google_api_annotations_proto_upbdefinit;

#ifdef __cplusplus
}  /* extern "C" */
#endif

#include "upb/port/undef.inc"

#endif  /* GOOGLE_API_ANNOTATIONS_PROTO_UPB_H__UPBDEFS_H_ */
