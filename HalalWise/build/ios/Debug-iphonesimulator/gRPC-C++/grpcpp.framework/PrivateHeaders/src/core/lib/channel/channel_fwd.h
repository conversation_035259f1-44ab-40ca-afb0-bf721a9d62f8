// Copyright 2022 gRPC authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef GRPC_SRC_CORE_LIB_CHANNEL_CHANNEL_FWD_H
#define GRPC_SRC_CORE_LIB_CHANNEL_CHANNEL_FWD_H

typedef struct grpc_channel_stack grpc_channel_stack;
typedef struct grpc_channel_filter grpc_channel_filter;

typedef struct grpc_channel_element grpc_channel_element;
typedef struct grpc_call_element grpc_call_element;

typedef struct grpc_call_stack grpc_call_stack;

#endif  // GRPC_SRC_CORE_LIB_CHANNEL_CHANNEL_FWD_H
