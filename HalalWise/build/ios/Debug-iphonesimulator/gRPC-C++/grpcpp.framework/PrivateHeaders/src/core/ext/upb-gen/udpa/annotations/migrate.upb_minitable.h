/* This file was generated by upb_generator from the input file:
 *
 *     udpa/annotations/migrate.proto
 *
 * Do not edit -- your changes will be discarded when the file is
 * regenerated.
 * NO CHECKED-IN PROTOBUF GENCODE */

#ifndef UDPA_ANNOTATIONS_MIGRATE_PROTO_UPB_H__UPB_MINITABLE_H_
#define UDPA_ANNOTATIONS_MIGRATE_PROTO_UPB_H__UPB_MINITABLE_H_

#include "upb/generated_code_support.h"

// Must be last.
#include "upb/port/def.inc"

#ifdef __cplusplus
extern "C" {
#endif

extern const upb_MiniTable udpa__annotations__MigrateAnnotation_msg_init;
extern const upb_MiniTable* udpa__annotations__MigrateAnnotation_msg_init_ptr;
extern const upb_MiniTable udpa__annotations__FieldMigrateAnnotation_msg_init;
extern const upb_MiniTable* udpa__annotations__FieldMigrateAnnotation_msg_init_ptr;
extern const upb_MiniTable udpa__annotations__FileMigrateAnnotation_msg_init;
extern const upb_MiniTable* udpa__annotations__FileMigrateAnnotation_msg_init_ptr;
extern const upb_MiniTableExtension udpa_annotations_message_migrate_ext;
extern const upb_MiniTableExtension udpa_annotations_field_migrate_ext;
extern const upb_MiniTableExtension udpa_annotations_enum_migrate_ext;
extern const upb_MiniTableExtension udpa_annotations_enum_value_migrate_ext;
extern const upb_MiniTableExtension udpa_annotations_file_migrate_ext;

extern const upb_MiniTableFile udpa_annotations_migrate_proto_upb_file_layout;

#ifdef __cplusplus
}  /* extern "C" */
#endif

#include "upb/port/undef.inc"

#endif  /* UDPA_ANNOTATIONS_MIGRATE_PROTO_UPB_H__UPB_MINITABLE_H_ */
