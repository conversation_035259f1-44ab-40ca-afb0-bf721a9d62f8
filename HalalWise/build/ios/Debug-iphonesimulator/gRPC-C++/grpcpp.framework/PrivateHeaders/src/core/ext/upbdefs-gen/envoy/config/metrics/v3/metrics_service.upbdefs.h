/* This file was generated by upb_generator from the input file:
 *
 *     envoy/config/metrics/v3/metrics_service.proto
 *
 * Do not edit -- your changes will be discarded when the file is
 * regenerated.
 * NO CHECKED-IN PROTOBUF GENCODE */

#ifndef ENVOY_CONFIG_METRICS_V3_METRICS_SERVICE_PROTO_UPB_H__UPBDEFS_H_
#define ENVOY_CONFIG_METRICS_V3_METRICS_SERVICE_PROTO_UPB_H__UPBDEFS_H_

#include "upb/reflection/def.h"
#include "upb/reflection/internal/def_pool.h"

#include "upb/port/def.inc" // Must be last.
#ifdef __cplusplus
extern "C" {
#endif

extern _upb_DefPool_Init envoy_config_metrics_v3_metrics_service_proto_upbdefinit;

UPB_INLINE const upb_MessageDef *envoy_config_metrics_v3_MetricsServiceConfig_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_metrics_v3_metrics_service_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.metrics.v3.MetricsServiceConfig");
}

#ifdef __cplusplus
}  /* extern "C" */
#endif

#include "upb/port/undef.inc"

#endif  /* ENVOY_CONFIG_METRICS_V3_METRICS_SERVICE_PROTO_UPB_H__UPBDEFS_H_ */
