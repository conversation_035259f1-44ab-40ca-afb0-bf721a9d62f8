/* This file was generated by upb_generator from the input file:
 *
 *     src/proto/grpc/lookup/v1/rls_config.proto
 *
 * Do not edit -- your changes will be discarded when the file is
 * regenerated.
 * NO CHECKED-IN PROTOBUF GENCODE */

#ifndef SRC_PROTO_GRPC_LOOKUP_V1_RLS_CONFIG_PROTO_UPB_H__UPB_MINITABLE_H_
#define SRC_PROTO_GRPC_LOOKUP_V1_RLS_CONFIG_PROTO_UPB_H__UPB_MINITABLE_H_

#include "upb/generated_code_support.h"

// Must be last.
#include "upb/port/def.inc"

#ifdef __cplusplus
extern "C" {
#endif

extern const upb_MiniTable grpc__lookup__v1__NameMatcher_msg_init;
extern const upb_MiniTable* grpc__lookup__v1__NameMatcher_msg_init_ptr;
extern const upb_MiniTable grpc__lookup__v1__GrpcKeyBuilder_msg_init;
extern const upb_MiniTable* grpc__lookup__v1__GrpcKeyBuilder_msg_init_ptr;
extern const upb_MiniTable grpc__lookup__v1__GrpcKeyBuilder__Name_msg_init;
extern const upb_MiniTable* grpc__lookup__v1__GrpcKeyBuilder__Name_msg_init_ptr;
extern const upb_MiniTable grpc__lookup__v1__GrpcKeyBuilder__ExtraKeys_msg_init;
extern const upb_MiniTable* grpc__lookup__v1__GrpcKeyBuilder__ExtraKeys_msg_init_ptr;
extern const upb_MiniTable grpc__lookup__v1__GrpcKeyBuilder__ConstantKeysEntry_msg_init;
extern const upb_MiniTable* grpc__lookup__v1__GrpcKeyBuilder__ConstantKeysEntry_msg_init_ptr;
extern const upb_MiniTable grpc__lookup__v1__HttpKeyBuilder_msg_init;
extern const upb_MiniTable* grpc__lookup__v1__HttpKeyBuilder_msg_init_ptr;
extern const upb_MiniTable grpc__lookup__v1__HttpKeyBuilder__ConstantKeysEntry_msg_init;
extern const upb_MiniTable* grpc__lookup__v1__HttpKeyBuilder__ConstantKeysEntry_msg_init_ptr;
extern const upb_MiniTable grpc__lookup__v1__RouteLookupConfig_msg_init;
extern const upb_MiniTable* grpc__lookup__v1__RouteLookupConfig_msg_init_ptr;
extern const upb_MiniTable grpc__lookup__v1__RouteLookupClusterSpecifier_msg_init;
extern const upb_MiniTable* grpc__lookup__v1__RouteLookupClusterSpecifier_msg_init_ptr;

extern const upb_MiniTableFile src_proto_grpc_lookup_v1_rls_config_proto_upb_file_layout;

#ifdef __cplusplus
}  /* extern "C" */
#endif

#include "upb/port/undef.inc"

#endif  /* SRC_PROTO_GRPC_LOOKUP_V1_RLS_CONFIG_PROTO_UPB_H__UPB_MINITABLE_H_ */
