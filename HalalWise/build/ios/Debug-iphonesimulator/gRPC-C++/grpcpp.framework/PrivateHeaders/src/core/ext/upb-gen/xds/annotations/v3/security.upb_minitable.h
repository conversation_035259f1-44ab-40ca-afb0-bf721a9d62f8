/* This file was generated by upb_generator from the input file:
 *
 *     xds/annotations/v3/security.proto
 *
 * Do not edit -- your changes will be discarded when the file is
 * regenerated.
 * NO CHECKED-IN PROTOBUF GENCODE */

#ifndef XDS_ANNOTATIONS_V3_SECURITY_PROTO_UPB_H__UPB_MINITABLE_H_
#define XDS_ANNOTATIONS_V3_SECURITY_PROTO_UPB_H__UPB_MINITABLE_H_

#include "upb/generated_code_support.h"

// Must be last.
#include "upb/port/def.inc"

#ifdef __cplusplus
extern "C" {
#endif

extern const upb_MiniTable xds__annotations__v3__FieldSecurityAnnotation_msg_init;
extern const upb_MiniTable* xds__annotations__v3__FieldSecurityAnnotation_msg_init_ptr;
extern const upb_MiniTableExtension xds_annotations_v3_security_ext;

extern const upb_MiniTableFile xds_annotations_v3_security_proto_upb_file_layout;

#ifdef __cplusplus
}  /* extern "C" */
#endif

#include "upb/port/undef.inc"

#endif  /* XDS_ANNOTATIONS_V3_SECURITY_PROTO_UPB_H__UPB_MINITABLE_H_ */
