/* This file was generated by upb_generator from the input file:
 *
 *     google/protobuf/descriptor.proto
 *
 * Do not edit -- your changes will be discarded when the file is
 * regenerated.
 * NO CHECKED-IN PROTOBUF GENCODE */

#ifndef GOOGLE_PROTOBUF_DESCRIPTOR_PROTO_UPB_H__UPBDEFS_H_
#define GOOGLE_PROTOBUF_DESCRIPTOR_PROTO_UPB_H__UPBDEFS_H_

#include "upb/reflection/def.h"
#include "upb/reflection/internal/def_pool.h"

#include "upb/port/def.inc" // Must be last.
#ifdef __cplusplus
extern "C" {
#endif

extern _upb_DefPool_Init google_protobuf_descriptor_proto_upbdefinit;

UPB_INLINE const upb_MessageDef *google_protobuf_FileDescriptorSet_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.FileDescriptorSet");
}

UPB_INLINE const upb_MessageDef *google_protobuf_FileDescriptorProto_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.FileDescriptorProto");
}

UPB_INLINE const upb_MessageDef *google_protobuf_DescriptorProto_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.DescriptorProto");
}

UPB_INLINE const upb_MessageDef *google_protobuf_DescriptorProto_ExtensionRange_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.DescriptorProto.ExtensionRange");
}

UPB_INLINE const upb_MessageDef *google_protobuf_DescriptorProto_ReservedRange_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.DescriptorProto.ReservedRange");
}

UPB_INLINE const upb_MessageDef *google_protobuf_ExtensionRangeOptions_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.ExtensionRangeOptions");
}

UPB_INLINE const upb_MessageDef *google_protobuf_ExtensionRangeOptions_Declaration_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.ExtensionRangeOptions.Declaration");
}

UPB_INLINE const upb_MessageDef *google_protobuf_FieldDescriptorProto_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.FieldDescriptorProto");
}

UPB_INLINE const upb_MessageDef *google_protobuf_OneofDescriptorProto_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.OneofDescriptorProto");
}

UPB_INLINE const upb_MessageDef *google_protobuf_EnumDescriptorProto_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.EnumDescriptorProto");
}

UPB_INLINE const upb_MessageDef *google_protobuf_EnumDescriptorProto_EnumReservedRange_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.EnumDescriptorProto.EnumReservedRange");
}

UPB_INLINE const upb_MessageDef *google_protobuf_EnumValueDescriptorProto_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.EnumValueDescriptorProto");
}

UPB_INLINE const upb_MessageDef *google_protobuf_ServiceDescriptorProto_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.ServiceDescriptorProto");
}

UPB_INLINE const upb_MessageDef *google_protobuf_MethodDescriptorProto_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.MethodDescriptorProto");
}

UPB_INLINE const upb_MessageDef *google_protobuf_FileOptions_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.FileOptions");
}

UPB_INLINE const upb_MessageDef *google_protobuf_MessageOptions_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.MessageOptions");
}

UPB_INLINE const upb_MessageDef *google_protobuf_FieldOptions_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.FieldOptions");
}

UPB_INLINE const upb_MessageDef *google_protobuf_FieldOptions_EditionDefault_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.FieldOptions.EditionDefault");
}

UPB_INLINE const upb_MessageDef *google_protobuf_FieldOptions_FeatureSupport_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.FieldOptions.FeatureSupport");
}

UPB_INLINE const upb_MessageDef *google_protobuf_OneofOptions_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.OneofOptions");
}

UPB_INLINE const upb_MessageDef *google_protobuf_EnumOptions_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.EnumOptions");
}

UPB_INLINE const upb_MessageDef *google_protobuf_EnumValueOptions_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.EnumValueOptions");
}

UPB_INLINE const upb_MessageDef *google_protobuf_ServiceOptions_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.ServiceOptions");
}

UPB_INLINE const upb_MessageDef *google_protobuf_MethodOptions_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.MethodOptions");
}

UPB_INLINE const upb_MessageDef *google_protobuf_UninterpretedOption_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.UninterpretedOption");
}

UPB_INLINE const upb_MessageDef *google_protobuf_UninterpretedOption_NamePart_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.UninterpretedOption.NamePart");
}

UPB_INLINE const upb_MessageDef *google_protobuf_FeatureSet_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.FeatureSet");
}

UPB_INLINE const upb_MessageDef *google_protobuf_FeatureSetDefaults_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.FeatureSetDefaults");
}

UPB_INLINE const upb_MessageDef *google_protobuf_FeatureSetDefaults_FeatureSetEditionDefault_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault");
}

UPB_INLINE const upb_MessageDef *google_protobuf_SourceCodeInfo_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.SourceCodeInfo");
}

UPB_INLINE const upb_MessageDef *google_protobuf_SourceCodeInfo_Location_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.SourceCodeInfo.Location");
}

UPB_INLINE const upb_MessageDef *google_protobuf_GeneratedCodeInfo_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.GeneratedCodeInfo");
}

UPB_INLINE const upb_MessageDef *google_protobuf_GeneratedCodeInfo_Annotation_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &google_protobuf_descriptor_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "google.protobuf.GeneratedCodeInfo.Annotation");
}

#ifdef __cplusplus
}  /* extern "C" */
#endif

#include "upb/port/undef.inc"

#endif  /* GOOGLE_PROTOBUF_DESCRIPTOR_PROTO_UPB_H__UPBDEFS_H_ */
