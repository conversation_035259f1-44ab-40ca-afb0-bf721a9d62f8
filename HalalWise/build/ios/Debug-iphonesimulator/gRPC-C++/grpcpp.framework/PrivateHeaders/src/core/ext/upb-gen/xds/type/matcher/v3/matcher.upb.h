/* This file was generated by upb_generator from the input file:
 *
 *     xds/type/matcher/v3/matcher.proto
 *
 * Do not edit -- your changes will be discarded when the file is
 * regenerated.
 * NO CHECKED-IN PROTOBUF GENCODE */

#ifndef XDS_TYPE_MATCHER_V3_MATCHER_PROTO_UPB_H__UPB_H_
#define XDS_TYPE_MATCHER_V3_MATCHER_PROTO_UPB_H__UPB_H_

#include "upb/generated_code_support.h"

#include "xds/type/matcher/v3/matcher.upb_minitable.h"

#include "xds/annotations/v3/status.upb_minitable.h"
#include "xds/core/v3/extension.upb_minitable.h"
#include "xds/type/matcher/v3/string.upb_minitable.h"
#include "validate/validate.upb_minitable.h"

// Must be last.
#include "upb/port/def.inc"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct xds_type_matcher_v3_Matcher { upb_Message UPB_PRIVATE(base); } xds_type_matcher_v3_Matcher;
typedef struct xds_type_matcher_v3_Matcher_OnMatch { upb_Message UPB_PRIVATE(base); } xds_type_matcher_v3_Matcher_OnMatch;
typedef struct xds_type_matcher_v3_Matcher_MatcherList { upb_Message UPB_PRIVATE(base); } xds_type_matcher_v3_Matcher_MatcherList;
typedef struct xds_type_matcher_v3_Matcher_MatcherList_Predicate { upb_Message UPB_PRIVATE(base); } xds_type_matcher_v3_Matcher_MatcherList_Predicate;
typedef struct xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate { upb_Message UPB_PRIVATE(base); } xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate;
typedef struct xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList { upb_Message UPB_PRIVATE(base); } xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList;
typedef struct xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher { upb_Message UPB_PRIVATE(base); } xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher;
typedef struct xds_type_matcher_v3_Matcher_MatcherTree { upb_Message UPB_PRIVATE(base); } xds_type_matcher_v3_Matcher_MatcherTree;
typedef struct xds_type_matcher_v3_Matcher_MatcherTree_MatchMap { upb_Message UPB_PRIVATE(base); } xds_type_matcher_v3_Matcher_MatcherTree_MatchMap;
typedef struct xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_MapEntry { upb_Message UPB_PRIVATE(base); } xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_MapEntry;
struct xds_core_v3_TypedExtensionConfig;
struct xds_type_matcher_v3_StringMatcher;



/* xds.type.matcher.v3.Matcher */

UPB_INLINE xds_type_matcher_v3_Matcher* xds_type_matcher_v3_Matcher_new(upb_Arena* arena) {
  return (xds_type_matcher_v3_Matcher*)_upb_Message_New(&xds__type__matcher__v3__Matcher_msg_init, arena);
}
UPB_INLINE xds_type_matcher_v3_Matcher* xds_type_matcher_v3_Matcher_parse(const char* buf, size_t size, upb_Arena* arena) {
  xds_type_matcher_v3_Matcher* ret = xds_type_matcher_v3_Matcher_new(arena);
  if (!ret) return NULL;
  if (upb_Decode(buf, size, UPB_UPCAST(ret), &xds__type__matcher__v3__Matcher_msg_init, NULL, 0, arena) !=
      kUpb_DecodeStatus_Ok) {
    return NULL;
  }
  return ret;
}
UPB_INLINE xds_type_matcher_v3_Matcher* xds_type_matcher_v3_Matcher_parse_ex(const char* buf, size_t size,
                           const upb_ExtensionRegistry* extreg,
                           int options, upb_Arena* arena) {
  xds_type_matcher_v3_Matcher* ret = xds_type_matcher_v3_Matcher_new(arena);
  if (!ret) return NULL;
  if (upb_Decode(buf, size, UPB_UPCAST(ret), &xds__type__matcher__v3__Matcher_msg_init, extreg, options,
                 arena) != kUpb_DecodeStatus_Ok) {
    return NULL;
  }
  return ret;
}
UPB_INLINE char* xds_type_matcher_v3_Matcher_serialize(const xds_type_matcher_v3_Matcher* msg, upb_Arena* arena, size_t* len) {
  char* ptr;
  (void)upb_Encode(UPB_UPCAST(msg), &xds__type__matcher__v3__Matcher_msg_init, 0, arena, &ptr, len);
  return ptr;
}
UPB_INLINE char* xds_type_matcher_v3_Matcher_serialize_ex(const xds_type_matcher_v3_Matcher* msg, int options,
                                 upb_Arena* arena, size_t* len) {
  char* ptr;
  (void)upb_Encode(UPB_UPCAST(msg), &xds__type__matcher__v3__Matcher_msg_init, options, arena, &ptr, len);
  return ptr;
}
typedef enum {
  xds_type_matcher_v3_Matcher_matcher_type_matcher_list = 1,
  xds_type_matcher_v3_Matcher_matcher_type_matcher_tree = 2,
  xds_type_matcher_v3_Matcher_matcher_type_NOT_SET = 0
} xds_type_matcher_v3_Matcher_matcher_type_oneofcases;
UPB_INLINE xds_type_matcher_v3_Matcher_matcher_type_oneofcases xds_type_matcher_v3_Matcher_matcher_type_case(const xds_type_matcher_v3_Matcher* msg) {
  const upb_MiniTableField field = {1, UPB_SIZE(20, 24), -13, 0, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  return (xds_type_matcher_v3_Matcher_matcher_type_oneofcases)upb_Message_WhichOneofFieldNumber(
      UPB_UPCAST(msg), &field);
}
UPB_INLINE void xds_type_matcher_v3_Matcher_clear_matcher_list(xds_type_matcher_v3_Matcher* msg) {
  const upb_MiniTableField field = {1, UPB_SIZE(20, 24), -13, 0, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  upb_Message_ClearBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE const xds_type_matcher_v3_Matcher_MatcherList* xds_type_matcher_v3_Matcher_matcher_list(const xds_type_matcher_v3_Matcher* msg) {
  const xds_type_matcher_v3_Matcher_MatcherList* default_val = NULL;
  const xds_type_matcher_v3_Matcher_MatcherList* ret;
  const upb_MiniTableField field = {1, UPB_SIZE(20, 24), -13, 0, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherList_msg_init);
  _upb_Message_GetNonExtensionField(UPB_UPCAST(msg), &field,
                                    &default_val, &ret);
  return ret;
}
UPB_INLINE bool xds_type_matcher_v3_Matcher_has_matcher_list(const xds_type_matcher_v3_Matcher* msg) {
  const upb_MiniTableField field = {1, UPB_SIZE(20, 24), -13, 0, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  return upb_Message_HasBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE void xds_type_matcher_v3_Matcher_clear_matcher_tree(xds_type_matcher_v3_Matcher* msg) {
  const upb_MiniTableField field = {2, UPB_SIZE(20, 24), -13, 1, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  upb_Message_ClearBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE const xds_type_matcher_v3_Matcher_MatcherTree* xds_type_matcher_v3_Matcher_matcher_tree(const xds_type_matcher_v3_Matcher* msg) {
  const xds_type_matcher_v3_Matcher_MatcherTree* default_val = NULL;
  const xds_type_matcher_v3_Matcher_MatcherTree* ret;
  const upb_MiniTableField field = {2, UPB_SIZE(20, 24), -13, 1, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherTree_msg_init);
  _upb_Message_GetNonExtensionField(UPB_UPCAST(msg), &field,
                                    &default_val, &ret);
  return ret;
}
UPB_INLINE bool xds_type_matcher_v3_Matcher_has_matcher_tree(const xds_type_matcher_v3_Matcher* msg) {
  const upb_MiniTableField field = {2, UPB_SIZE(20, 24), -13, 1, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  return upb_Message_HasBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE void xds_type_matcher_v3_Matcher_clear_on_no_match(xds_type_matcher_v3_Matcher* msg) {
  const upb_MiniTableField field = {3, 16, 64, 2, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  upb_Message_ClearBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE const xds_type_matcher_v3_Matcher_OnMatch* xds_type_matcher_v3_Matcher_on_no_match(const xds_type_matcher_v3_Matcher* msg) {
  const xds_type_matcher_v3_Matcher_OnMatch* default_val = NULL;
  const xds_type_matcher_v3_Matcher_OnMatch* ret;
  const upb_MiniTableField field = {3, 16, 64, 2, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__OnMatch_msg_init);
  _upb_Message_GetNonExtensionField(UPB_UPCAST(msg), &field,
                                    &default_val, &ret);
  return ret;
}
UPB_INLINE bool xds_type_matcher_v3_Matcher_has_on_no_match(const xds_type_matcher_v3_Matcher* msg) {
  const upb_MiniTableField field = {3, 16, 64, 2, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  return upb_Message_HasBaseField(UPB_UPCAST(msg), &field);
}

UPB_INLINE void xds_type_matcher_v3_Matcher_set_matcher_list(xds_type_matcher_v3_Matcher *msg, xds_type_matcher_v3_Matcher_MatcherList* value) {
  const upb_MiniTableField field = {1, UPB_SIZE(20, 24), -13, 0, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherList_msg_init);
  upb_Message_SetBaseField((upb_Message *)msg, &field, &value);
}
UPB_INLINE struct xds_type_matcher_v3_Matcher_MatcherList* xds_type_matcher_v3_Matcher_mutable_matcher_list(xds_type_matcher_v3_Matcher* msg, upb_Arena* arena) {
  struct xds_type_matcher_v3_Matcher_MatcherList* sub = (struct xds_type_matcher_v3_Matcher_MatcherList*)xds_type_matcher_v3_Matcher_matcher_list(msg);
  if (sub == NULL) {
    sub = (struct xds_type_matcher_v3_Matcher_MatcherList*)_upb_Message_New(&xds__type__matcher__v3__Matcher__MatcherList_msg_init, arena);
    if (sub) xds_type_matcher_v3_Matcher_set_matcher_list(msg, sub);
  }
  return sub;
}
UPB_INLINE void xds_type_matcher_v3_Matcher_set_matcher_tree(xds_type_matcher_v3_Matcher *msg, xds_type_matcher_v3_Matcher_MatcherTree* value) {
  const upb_MiniTableField field = {2, UPB_SIZE(20, 24), -13, 1, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherTree_msg_init);
  upb_Message_SetBaseField((upb_Message *)msg, &field, &value);
}
UPB_INLINE struct xds_type_matcher_v3_Matcher_MatcherTree* xds_type_matcher_v3_Matcher_mutable_matcher_tree(xds_type_matcher_v3_Matcher* msg, upb_Arena* arena) {
  struct xds_type_matcher_v3_Matcher_MatcherTree* sub = (struct xds_type_matcher_v3_Matcher_MatcherTree*)xds_type_matcher_v3_Matcher_matcher_tree(msg);
  if (sub == NULL) {
    sub = (struct xds_type_matcher_v3_Matcher_MatcherTree*)_upb_Message_New(&xds__type__matcher__v3__Matcher__MatcherTree_msg_init, arena);
    if (sub) xds_type_matcher_v3_Matcher_set_matcher_tree(msg, sub);
  }
  return sub;
}
UPB_INLINE void xds_type_matcher_v3_Matcher_set_on_no_match(xds_type_matcher_v3_Matcher *msg, xds_type_matcher_v3_Matcher_OnMatch* value) {
  const upb_MiniTableField field = {3, 16, 64, 2, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__OnMatch_msg_init);
  upb_Message_SetBaseField((upb_Message *)msg, &field, &value);
}
UPB_INLINE struct xds_type_matcher_v3_Matcher_OnMatch* xds_type_matcher_v3_Matcher_mutable_on_no_match(xds_type_matcher_v3_Matcher* msg, upb_Arena* arena) {
  struct xds_type_matcher_v3_Matcher_OnMatch* sub = (struct xds_type_matcher_v3_Matcher_OnMatch*)xds_type_matcher_v3_Matcher_on_no_match(msg);
  if (sub == NULL) {
    sub = (struct xds_type_matcher_v3_Matcher_OnMatch*)_upb_Message_New(&xds__type__matcher__v3__Matcher__OnMatch_msg_init, arena);
    if (sub) xds_type_matcher_v3_Matcher_set_on_no_match(msg, sub);
  }
  return sub;
}

/* xds.type.matcher.v3.Matcher.OnMatch */

UPB_INLINE xds_type_matcher_v3_Matcher_OnMatch* xds_type_matcher_v3_Matcher_OnMatch_new(upb_Arena* arena) {
  return (xds_type_matcher_v3_Matcher_OnMatch*)_upb_Message_New(&xds__type__matcher__v3__Matcher__OnMatch_msg_init, arena);
}
UPB_INLINE xds_type_matcher_v3_Matcher_OnMatch* xds_type_matcher_v3_Matcher_OnMatch_parse(const char* buf, size_t size, upb_Arena* arena) {
  xds_type_matcher_v3_Matcher_OnMatch* ret = xds_type_matcher_v3_Matcher_OnMatch_new(arena);
  if (!ret) return NULL;
  if (upb_Decode(buf, size, UPB_UPCAST(ret), &xds__type__matcher__v3__Matcher__OnMatch_msg_init, NULL, 0, arena) !=
      kUpb_DecodeStatus_Ok) {
    return NULL;
  }
  return ret;
}
UPB_INLINE xds_type_matcher_v3_Matcher_OnMatch* xds_type_matcher_v3_Matcher_OnMatch_parse_ex(const char* buf, size_t size,
                           const upb_ExtensionRegistry* extreg,
                           int options, upb_Arena* arena) {
  xds_type_matcher_v3_Matcher_OnMatch* ret = xds_type_matcher_v3_Matcher_OnMatch_new(arena);
  if (!ret) return NULL;
  if (upb_Decode(buf, size, UPB_UPCAST(ret), &xds__type__matcher__v3__Matcher__OnMatch_msg_init, extreg, options,
                 arena) != kUpb_DecodeStatus_Ok) {
    return NULL;
  }
  return ret;
}
UPB_INLINE char* xds_type_matcher_v3_Matcher_OnMatch_serialize(const xds_type_matcher_v3_Matcher_OnMatch* msg, upb_Arena* arena, size_t* len) {
  char* ptr;
  (void)upb_Encode(UPB_UPCAST(msg), &xds__type__matcher__v3__Matcher__OnMatch_msg_init, 0, arena, &ptr, len);
  return ptr;
}
UPB_INLINE char* xds_type_matcher_v3_Matcher_OnMatch_serialize_ex(const xds_type_matcher_v3_Matcher_OnMatch* msg, int options,
                                 upb_Arena* arena, size_t* len) {
  char* ptr;
  (void)upb_Encode(UPB_UPCAST(msg), &xds__type__matcher__v3__Matcher__OnMatch_msg_init, options, arena, &ptr, len);
  return ptr;
}
typedef enum {
  xds_type_matcher_v3_Matcher_OnMatch_on_match_matcher = 1,
  xds_type_matcher_v3_Matcher_OnMatch_on_match_action = 2,
  xds_type_matcher_v3_Matcher_OnMatch_on_match_NOT_SET = 0
} xds_type_matcher_v3_Matcher_OnMatch_on_match_oneofcases;
UPB_INLINE xds_type_matcher_v3_Matcher_OnMatch_on_match_oneofcases xds_type_matcher_v3_Matcher_OnMatch_on_match_case(const xds_type_matcher_v3_Matcher_OnMatch* msg) {
  const upb_MiniTableField field = {1, UPB_SIZE(12, 16), -9, 0, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  return (xds_type_matcher_v3_Matcher_OnMatch_on_match_oneofcases)upb_Message_WhichOneofFieldNumber(
      UPB_UPCAST(msg), &field);
}
UPB_INLINE void xds_type_matcher_v3_Matcher_OnMatch_clear_matcher(xds_type_matcher_v3_Matcher_OnMatch* msg) {
  const upb_MiniTableField field = {1, UPB_SIZE(12, 16), -9, 0, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  upb_Message_ClearBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE const xds_type_matcher_v3_Matcher* xds_type_matcher_v3_Matcher_OnMatch_matcher(const xds_type_matcher_v3_Matcher_OnMatch* msg) {
  const xds_type_matcher_v3_Matcher* default_val = NULL;
  const xds_type_matcher_v3_Matcher* ret;
  const upb_MiniTableField field = {1, UPB_SIZE(12, 16), -9, 0, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher_msg_init);
  _upb_Message_GetNonExtensionField(UPB_UPCAST(msg), &field,
                                    &default_val, &ret);
  return ret;
}
UPB_INLINE bool xds_type_matcher_v3_Matcher_OnMatch_has_matcher(const xds_type_matcher_v3_Matcher_OnMatch* msg) {
  const upb_MiniTableField field = {1, UPB_SIZE(12, 16), -9, 0, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  return upb_Message_HasBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE void xds_type_matcher_v3_Matcher_OnMatch_clear_action(xds_type_matcher_v3_Matcher_OnMatch* msg) {
  const upb_MiniTableField field = {2, UPB_SIZE(12, 16), -9, 1, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  upb_Message_ClearBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE const struct xds_core_v3_TypedExtensionConfig* xds_type_matcher_v3_Matcher_OnMatch_action(const xds_type_matcher_v3_Matcher_OnMatch* msg) {
  const struct xds_core_v3_TypedExtensionConfig* default_val = NULL;
  const struct xds_core_v3_TypedExtensionConfig* ret;
  const upb_MiniTableField field = {2, UPB_SIZE(12, 16), -9, 1, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__core__v3__TypedExtensionConfig_msg_init);
  _upb_Message_GetNonExtensionField(UPB_UPCAST(msg), &field,
                                    &default_val, &ret);
  return ret;
}
UPB_INLINE bool xds_type_matcher_v3_Matcher_OnMatch_has_action(const xds_type_matcher_v3_Matcher_OnMatch* msg) {
  const upb_MiniTableField field = {2, UPB_SIZE(12, 16), -9, 1, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  return upb_Message_HasBaseField(UPB_UPCAST(msg), &field);
}

UPB_INLINE void xds_type_matcher_v3_Matcher_OnMatch_set_matcher(xds_type_matcher_v3_Matcher_OnMatch *msg, xds_type_matcher_v3_Matcher* value) {
  const upb_MiniTableField field = {1, UPB_SIZE(12, 16), -9, 0, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher_msg_init);
  upb_Message_SetBaseField((upb_Message *)msg, &field, &value);
}
UPB_INLINE struct xds_type_matcher_v3_Matcher* xds_type_matcher_v3_Matcher_OnMatch_mutable_matcher(xds_type_matcher_v3_Matcher_OnMatch* msg, upb_Arena* arena) {
  struct xds_type_matcher_v3_Matcher* sub = (struct xds_type_matcher_v3_Matcher*)xds_type_matcher_v3_Matcher_OnMatch_matcher(msg);
  if (sub == NULL) {
    sub = (struct xds_type_matcher_v3_Matcher*)_upb_Message_New(&xds__type__matcher__v3__Matcher_msg_init, arena);
    if (sub) xds_type_matcher_v3_Matcher_OnMatch_set_matcher(msg, sub);
  }
  return sub;
}
UPB_INLINE void xds_type_matcher_v3_Matcher_OnMatch_set_action(xds_type_matcher_v3_Matcher_OnMatch *msg, struct xds_core_v3_TypedExtensionConfig* value) {
  const upb_MiniTableField field = {2, UPB_SIZE(12, 16), -9, 1, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__core__v3__TypedExtensionConfig_msg_init);
  upb_Message_SetBaseField((upb_Message *)msg, &field, &value);
}
UPB_INLINE struct xds_core_v3_TypedExtensionConfig* xds_type_matcher_v3_Matcher_OnMatch_mutable_action(xds_type_matcher_v3_Matcher_OnMatch* msg, upb_Arena* arena) {
  struct xds_core_v3_TypedExtensionConfig* sub = (struct xds_core_v3_TypedExtensionConfig*)xds_type_matcher_v3_Matcher_OnMatch_action(msg);
  if (sub == NULL) {
    sub = (struct xds_core_v3_TypedExtensionConfig*)_upb_Message_New(&xds__core__v3__TypedExtensionConfig_msg_init, arena);
    if (sub) xds_type_matcher_v3_Matcher_OnMatch_set_action(msg, sub);
  }
  return sub;
}

/* xds.type.matcher.v3.Matcher.MatcherList */

UPB_INLINE xds_type_matcher_v3_Matcher_MatcherList* xds_type_matcher_v3_Matcher_MatcherList_new(upb_Arena* arena) {
  return (xds_type_matcher_v3_Matcher_MatcherList*)_upb_Message_New(&xds__type__matcher__v3__Matcher__MatcherList_msg_init, arena);
}
UPB_INLINE xds_type_matcher_v3_Matcher_MatcherList* xds_type_matcher_v3_Matcher_MatcherList_parse(const char* buf, size_t size, upb_Arena* arena) {
  xds_type_matcher_v3_Matcher_MatcherList* ret = xds_type_matcher_v3_Matcher_MatcherList_new(arena);
  if (!ret) return NULL;
  if (upb_Decode(buf, size, UPB_UPCAST(ret), &xds__type__matcher__v3__Matcher__MatcherList_msg_init, NULL, 0, arena) !=
      kUpb_DecodeStatus_Ok) {
    return NULL;
  }
  return ret;
}
UPB_INLINE xds_type_matcher_v3_Matcher_MatcherList* xds_type_matcher_v3_Matcher_MatcherList_parse_ex(const char* buf, size_t size,
                           const upb_ExtensionRegistry* extreg,
                           int options, upb_Arena* arena) {
  xds_type_matcher_v3_Matcher_MatcherList* ret = xds_type_matcher_v3_Matcher_MatcherList_new(arena);
  if (!ret) return NULL;
  if (upb_Decode(buf, size, UPB_UPCAST(ret), &xds__type__matcher__v3__Matcher__MatcherList_msg_init, extreg, options,
                 arena) != kUpb_DecodeStatus_Ok) {
    return NULL;
  }
  return ret;
}
UPB_INLINE char* xds_type_matcher_v3_Matcher_MatcherList_serialize(const xds_type_matcher_v3_Matcher_MatcherList* msg, upb_Arena* arena, size_t* len) {
  char* ptr;
  (void)upb_Encode(UPB_UPCAST(msg), &xds__type__matcher__v3__Matcher__MatcherList_msg_init, 0, arena, &ptr, len);
  return ptr;
}
UPB_INLINE char* xds_type_matcher_v3_Matcher_MatcherList_serialize_ex(const xds_type_matcher_v3_Matcher_MatcherList* msg, int options,
                                 upb_Arena* arena, size_t* len) {
  char* ptr;
  (void)upb_Encode(UPB_UPCAST(msg), &xds__type__matcher__v3__Matcher__MatcherList_msg_init, options, arena, &ptr, len);
  return ptr;
}
UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherList_clear_matchers(xds_type_matcher_v3_Matcher_MatcherList* msg) {
  const upb_MiniTableField field = {1, 8, 0, 0, 11, (int)kUpb_FieldMode_Array | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  upb_Message_ClearBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE const xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher* const* xds_type_matcher_v3_Matcher_MatcherList_matchers(const xds_type_matcher_v3_Matcher_MatcherList* msg, size_t* size) {
  const upb_MiniTableField field = {1, 8, 0, 0, 11, (int)kUpb_FieldMode_Array | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherList__FieldMatcher_msg_init);
  const upb_Array* arr = upb_Message_GetArray(UPB_UPCAST(msg), &field);
  if (arr) {
    if (size) *size = arr->UPB_PRIVATE(size);
    return (const xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher* const*)upb_Array_DataPtr(arr);
  } else {
    if (size) *size = 0;
    return NULL;
  }
}
UPB_INLINE const upb_Array* _xds_type_matcher_v3_Matcher_MatcherList_matchers_upb_array(const xds_type_matcher_v3_Matcher_MatcherList* msg, size_t* size) {
  const upb_MiniTableField field = {1, 8, 0, 0, 11, (int)kUpb_FieldMode_Array | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherList__FieldMatcher_msg_init);
  const upb_Array* arr = upb_Message_GetArray(UPB_UPCAST(msg), &field);
  if (size) {
    *size = arr ? arr->UPB_PRIVATE(size) : 0;
  }
  return arr;
}
UPB_INLINE upb_Array* _xds_type_matcher_v3_Matcher_MatcherList_matchers_mutable_upb_array(xds_type_matcher_v3_Matcher_MatcherList* msg, size_t* size, upb_Arena* arena) {
  const upb_MiniTableField field = {1, 8, 0, 0, 11, (int)kUpb_FieldMode_Array | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherList__FieldMatcher_msg_init);
  upb_Array* arr = upb_Message_GetOrCreateMutableArray(UPB_UPCAST(msg),
                                                       &field, arena);
  if (size) {
    *size = arr ? arr->UPB_PRIVATE(size) : 0;
  }
  return arr;
}

UPB_INLINE xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher** xds_type_matcher_v3_Matcher_MatcherList_mutable_matchers(xds_type_matcher_v3_Matcher_MatcherList* msg, size_t* size) {
  upb_MiniTableField field = {1, 8, 0, 0, 11, (int)kUpb_FieldMode_Array | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherList__FieldMatcher_msg_init);
  upb_Array* arr = upb_Message_GetMutableArray(UPB_UPCAST(msg), &field);
  if (arr) {
    if (size) *size = arr->UPB_PRIVATE(size);
    return (xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher**)upb_Array_MutableDataPtr(arr);
  } else {
    if (size) *size = 0;
    return NULL;
  }
}
UPB_INLINE xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher** xds_type_matcher_v3_Matcher_MatcherList_resize_matchers(xds_type_matcher_v3_Matcher_MatcherList* msg, size_t size, upb_Arena* arena) {
  upb_MiniTableField field = {1, 8, 0, 0, 11, (int)kUpb_FieldMode_Array | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  return (xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher**)upb_Message_ResizeArrayUninitialized(UPB_UPCAST(msg),
                                                   &field, size, arena);
}
UPB_INLINE struct xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher* xds_type_matcher_v3_Matcher_MatcherList_add_matchers(xds_type_matcher_v3_Matcher_MatcherList* msg, upb_Arena* arena) {
  upb_MiniTableField field = {1, 8, 0, 0, 11, (int)kUpb_FieldMode_Array | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherList__FieldMatcher_msg_init);
  upb_Array* arr = upb_Message_GetOrCreateMutableArray(
      UPB_UPCAST(msg), &field, arena);
  if (!arr || !UPB_PRIVATE(_upb_Array_ResizeUninitialized)(
                  arr, arr->UPB_PRIVATE(size) + 1, arena)) {
    return NULL;
  }
  struct xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher* sub = (struct xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher*)_upb_Message_New(&xds__type__matcher__v3__Matcher__MatcherList__FieldMatcher_msg_init, arena);
  if (!arr || !sub) return NULL;
  UPB_PRIVATE(_upb_Array_Set)
  (arr, arr->UPB_PRIVATE(size) - 1, &sub, sizeof(sub));
  return sub;
}

/* xds.type.matcher.v3.Matcher.MatcherList.Predicate */

UPB_INLINE xds_type_matcher_v3_Matcher_MatcherList_Predicate* xds_type_matcher_v3_Matcher_MatcherList_Predicate_new(upb_Arena* arena) {
  return (xds_type_matcher_v3_Matcher_MatcherList_Predicate*)_upb_Message_New(&xds__type__matcher__v3__Matcher__MatcherList__Predicate_msg_init, arena);
}
UPB_INLINE xds_type_matcher_v3_Matcher_MatcherList_Predicate* xds_type_matcher_v3_Matcher_MatcherList_Predicate_parse(const char* buf, size_t size, upb_Arena* arena) {
  xds_type_matcher_v3_Matcher_MatcherList_Predicate* ret = xds_type_matcher_v3_Matcher_MatcherList_Predicate_new(arena);
  if (!ret) return NULL;
  if (upb_Decode(buf, size, UPB_UPCAST(ret), &xds__type__matcher__v3__Matcher__MatcherList__Predicate_msg_init, NULL, 0, arena) !=
      kUpb_DecodeStatus_Ok) {
    return NULL;
  }
  return ret;
}
UPB_INLINE xds_type_matcher_v3_Matcher_MatcherList_Predicate* xds_type_matcher_v3_Matcher_MatcherList_Predicate_parse_ex(const char* buf, size_t size,
                           const upb_ExtensionRegistry* extreg,
                           int options, upb_Arena* arena) {
  xds_type_matcher_v3_Matcher_MatcherList_Predicate* ret = xds_type_matcher_v3_Matcher_MatcherList_Predicate_new(arena);
  if (!ret) return NULL;
  if (upb_Decode(buf, size, UPB_UPCAST(ret), &xds__type__matcher__v3__Matcher__MatcherList__Predicate_msg_init, extreg, options,
                 arena) != kUpb_DecodeStatus_Ok) {
    return NULL;
  }
  return ret;
}
UPB_INLINE char* xds_type_matcher_v3_Matcher_MatcherList_Predicate_serialize(const xds_type_matcher_v3_Matcher_MatcherList_Predicate* msg, upb_Arena* arena, size_t* len) {
  char* ptr;
  (void)upb_Encode(UPB_UPCAST(msg), &xds__type__matcher__v3__Matcher__MatcherList__Predicate_msg_init, 0, arena, &ptr, len);
  return ptr;
}
UPB_INLINE char* xds_type_matcher_v3_Matcher_MatcherList_Predicate_serialize_ex(const xds_type_matcher_v3_Matcher_MatcherList_Predicate* msg, int options,
                                 upb_Arena* arena, size_t* len) {
  char* ptr;
  (void)upb_Encode(UPB_UPCAST(msg), &xds__type__matcher__v3__Matcher__MatcherList__Predicate_msg_init, options, arena, &ptr, len);
  return ptr;
}
typedef enum {
  xds_type_matcher_v3_Matcher_MatcherList_Predicate_match_type_single_predicate = 1,
  xds_type_matcher_v3_Matcher_MatcherList_Predicate_match_type_or_matcher = 2,
  xds_type_matcher_v3_Matcher_MatcherList_Predicate_match_type_and_matcher = 3,
  xds_type_matcher_v3_Matcher_MatcherList_Predicate_match_type_not_matcher = 4,
  xds_type_matcher_v3_Matcher_MatcherList_Predicate_match_type_NOT_SET = 0
} xds_type_matcher_v3_Matcher_MatcherList_Predicate_match_type_oneofcases;
UPB_INLINE xds_type_matcher_v3_Matcher_MatcherList_Predicate_match_type_oneofcases xds_type_matcher_v3_Matcher_MatcherList_Predicate_match_type_case(const xds_type_matcher_v3_Matcher_MatcherList_Predicate* msg) {
  const upb_MiniTableField field = {1, UPB_SIZE(12, 16), -9, 0, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  return (xds_type_matcher_v3_Matcher_MatcherList_Predicate_match_type_oneofcases)upb_Message_WhichOneofFieldNumber(
      UPB_UPCAST(msg), &field);
}
UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherList_Predicate_clear_single_predicate(xds_type_matcher_v3_Matcher_MatcherList_Predicate* msg) {
  const upb_MiniTableField field = {1, UPB_SIZE(12, 16), -9, 0, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  upb_Message_ClearBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE const xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate* xds_type_matcher_v3_Matcher_MatcherList_Predicate_single_predicate(const xds_type_matcher_v3_Matcher_MatcherList_Predicate* msg) {
  const xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate* default_val = NULL;
  const xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate* ret;
  const upb_MiniTableField field = {1, UPB_SIZE(12, 16), -9, 0, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherList__Predicate__SinglePredicate_msg_init);
  _upb_Message_GetNonExtensionField(UPB_UPCAST(msg), &field,
                                    &default_val, &ret);
  return ret;
}
UPB_INLINE bool xds_type_matcher_v3_Matcher_MatcherList_Predicate_has_single_predicate(const xds_type_matcher_v3_Matcher_MatcherList_Predicate* msg) {
  const upb_MiniTableField field = {1, UPB_SIZE(12, 16), -9, 0, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  return upb_Message_HasBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherList_Predicate_clear_or_matcher(xds_type_matcher_v3_Matcher_MatcherList_Predicate* msg) {
  const upb_MiniTableField field = {2, UPB_SIZE(12, 16), -9, 1, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  upb_Message_ClearBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE const xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList* xds_type_matcher_v3_Matcher_MatcherList_Predicate_or_matcher(const xds_type_matcher_v3_Matcher_MatcherList_Predicate* msg) {
  const xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList* default_val = NULL;
  const xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList* ret;
  const upb_MiniTableField field = {2, UPB_SIZE(12, 16), -9, 1, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherList__Predicate__PredicateList_msg_init);
  _upb_Message_GetNonExtensionField(UPB_UPCAST(msg), &field,
                                    &default_val, &ret);
  return ret;
}
UPB_INLINE bool xds_type_matcher_v3_Matcher_MatcherList_Predicate_has_or_matcher(const xds_type_matcher_v3_Matcher_MatcherList_Predicate* msg) {
  const upb_MiniTableField field = {2, UPB_SIZE(12, 16), -9, 1, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  return upb_Message_HasBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherList_Predicate_clear_and_matcher(xds_type_matcher_v3_Matcher_MatcherList_Predicate* msg) {
  const upb_MiniTableField field = {3, UPB_SIZE(12, 16), -9, 2, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  upb_Message_ClearBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE const xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList* xds_type_matcher_v3_Matcher_MatcherList_Predicate_and_matcher(const xds_type_matcher_v3_Matcher_MatcherList_Predicate* msg) {
  const xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList* default_val = NULL;
  const xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList* ret;
  const upb_MiniTableField field = {3, UPB_SIZE(12, 16), -9, 2, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherList__Predicate__PredicateList_msg_init);
  _upb_Message_GetNonExtensionField(UPB_UPCAST(msg), &field,
                                    &default_val, &ret);
  return ret;
}
UPB_INLINE bool xds_type_matcher_v3_Matcher_MatcherList_Predicate_has_and_matcher(const xds_type_matcher_v3_Matcher_MatcherList_Predicate* msg) {
  const upb_MiniTableField field = {3, UPB_SIZE(12, 16), -9, 2, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  return upb_Message_HasBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherList_Predicate_clear_not_matcher(xds_type_matcher_v3_Matcher_MatcherList_Predicate* msg) {
  const upb_MiniTableField field = {4, UPB_SIZE(12, 16), -9, 3, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  upb_Message_ClearBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE const xds_type_matcher_v3_Matcher_MatcherList_Predicate* xds_type_matcher_v3_Matcher_MatcherList_Predicate_not_matcher(const xds_type_matcher_v3_Matcher_MatcherList_Predicate* msg) {
  const xds_type_matcher_v3_Matcher_MatcherList_Predicate* default_val = NULL;
  const xds_type_matcher_v3_Matcher_MatcherList_Predicate* ret;
  const upb_MiniTableField field = {4, UPB_SIZE(12, 16), -9, 3, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherList__Predicate_msg_init);
  _upb_Message_GetNonExtensionField(UPB_UPCAST(msg), &field,
                                    &default_val, &ret);
  return ret;
}
UPB_INLINE bool xds_type_matcher_v3_Matcher_MatcherList_Predicate_has_not_matcher(const xds_type_matcher_v3_Matcher_MatcherList_Predicate* msg) {
  const upb_MiniTableField field = {4, UPB_SIZE(12, 16), -9, 3, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  return upb_Message_HasBaseField(UPB_UPCAST(msg), &field);
}

UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherList_Predicate_set_single_predicate(xds_type_matcher_v3_Matcher_MatcherList_Predicate *msg, xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate* value) {
  const upb_MiniTableField field = {1, UPB_SIZE(12, 16), -9, 0, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherList__Predicate__SinglePredicate_msg_init);
  upb_Message_SetBaseField((upb_Message *)msg, &field, &value);
}
UPB_INLINE struct xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate* xds_type_matcher_v3_Matcher_MatcherList_Predicate_mutable_single_predicate(xds_type_matcher_v3_Matcher_MatcherList_Predicate* msg, upb_Arena* arena) {
  struct xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate* sub = (struct xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate*)xds_type_matcher_v3_Matcher_MatcherList_Predicate_single_predicate(msg);
  if (sub == NULL) {
    sub = (struct xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate*)_upb_Message_New(&xds__type__matcher__v3__Matcher__MatcherList__Predicate__SinglePredicate_msg_init, arena);
    if (sub) xds_type_matcher_v3_Matcher_MatcherList_Predicate_set_single_predicate(msg, sub);
  }
  return sub;
}
UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherList_Predicate_set_or_matcher(xds_type_matcher_v3_Matcher_MatcherList_Predicate *msg, xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList* value) {
  const upb_MiniTableField field = {2, UPB_SIZE(12, 16), -9, 1, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherList__Predicate__PredicateList_msg_init);
  upb_Message_SetBaseField((upb_Message *)msg, &field, &value);
}
UPB_INLINE struct xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList* xds_type_matcher_v3_Matcher_MatcherList_Predicate_mutable_or_matcher(xds_type_matcher_v3_Matcher_MatcherList_Predicate* msg, upb_Arena* arena) {
  struct xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList* sub = (struct xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList*)xds_type_matcher_v3_Matcher_MatcherList_Predicate_or_matcher(msg);
  if (sub == NULL) {
    sub = (struct xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList*)_upb_Message_New(&xds__type__matcher__v3__Matcher__MatcherList__Predicate__PredicateList_msg_init, arena);
    if (sub) xds_type_matcher_v3_Matcher_MatcherList_Predicate_set_or_matcher(msg, sub);
  }
  return sub;
}
UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherList_Predicate_set_and_matcher(xds_type_matcher_v3_Matcher_MatcherList_Predicate *msg, xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList* value) {
  const upb_MiniTableField field = {3, UPB_SIZE(12, 16), -9, 2, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherList__Predicate__PredicateList_msg_init);
  upb_Message_SetBaseField((upb_Message *)msg, &field, &value);
}
UPB_INLINE struct xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList* xds_type_matcher_v3_Matcher_MatcherList_Predicate_mutable_and_matcher(xds_type_matcher_v3_Matcher_MatcherList_Predicate* msg, upb_Arena* arena) {
  struct xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList* sub = (struct xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList*)xds_type_matcher_v3_Matcher_MatcherList_Predicate_and_matcher(msg);
  if (sub == NULL) {
    sub = (struct xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList*)_upb_Message_New(&xds__type__matcher__v3__Matcher__MatcherList__Predicate__PredicateList_msg_init, arena);
    if (sub) xds_type_matcher_v3_Matcher_MatcherList_Predicate_set_and_matcher(msg, sub);
  }
  return sub;
}
UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherList_Predicate_set_not_matcher(xds_type_matcher_v3_Matcher_MatcherList_Predicate *msg, xds_type_matcher_v3_Matcher_MatcherList_Predicate* value) {
  const upb_MiniTableField field = {4, UPB_SIZE(12, 16), -9, 3, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherList__Predicate_msg_init);
  upb_Message_SetBaseField((upb_Message *)msg, &field, &value);
}
UPB_INLINE struct xds_type_matcher_v3_Matcher_MatcherList_Predicate* xds_type_matcher_v3_Matcher_MatcherList_Predicate_mutable_not_matcher(xds_type_matcher_v3_Matcher_MatcherList_Predicate* msg, upb_Arena* arena) {
  struct xds_type_matcher_v3_Matcher_MatcherList_Predicate* sub = (struct xds_type_matcher_v3_Matcher_MatcherList_Predicate*)xds_type_matcher_v3_Matcher_MatcherList_Predicate_not_matcher(msg);
  if (sub == NULL) {
    sub = (struct xds_type_matcher_v3_Matcher_MatcherList_Predicate*)_upb_Message_New(&xds__type__matcher__v3__Matcher__MatcherList__Predicate_msg_init, arena);
    if (sub) xds_type_matcher_v3_Matcher_MatcherList_Predicate_set_not_matcher(msg, sub);
  }
  return sub;
}

/* xds.type.matcher.v3.Matcher.MatcherList.Predicate.SinglePredicate */

UPB_INLINE xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate* xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_new(upb_Arena* arena) {
  return (xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate*)_upb_Message_New(&xds__type__matcher__v3__Matcher__MatcherList__Predicate__SinglePredicate_msg_init, arena);
}
UPB_INLINE xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate* xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_parse(const char* buf, size_t size, upb_Arena* arena) {
  xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate* ret = xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_new(arena);
  if (!ret) return NULL;
  if (upb_Decode(buf, size, UPB_UPCAST(ret), &xds__type__matcher__v3__Matcher__MatcherList__Predicate__SinglePredicate_msg_init, NULL, 0, arena) !=
      kUpb_DecodeStatus_Ok) {
    return NULL;
  }
  return ret;
}
UPB_INLINE xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate* xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_parse_ex(const char* buf, size_t size,
                           const upb_ExtensionRegistry* extreg,
                           int options, upb_Arena* arena) {
  xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate* ret = xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_new(arena);
  if (!ret) return NULL;
  if (upb_Decode(buf, size, UPB_UPCAST(ret), &xds__type__matcher__v3__Matcher__MatcherList__Predicate__SinglePredicate_msg_init, extreg, options,
                 arena) != kUpb_DecodeStatus_Ok) {
    return NULL;
  }
  return ret;
}
UPB_INLINE char* xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_serialize(const xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate* msg, upb_Arena* arena, size_t* len) {
  char* ptr;
  (void)upb_Encode(UPB_UPCAST(msg), &xds__type__matcher__v3__Matcher__MatcherList__Predicate__SinglePredicate_msg_init, 0, arena, &ptr, len);
  return ptr;
}
UPB_INLINE char* xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_serialize_ex(const xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate* msg, int options,
                                 upb_Arena* arena, size_t* len) {
  char* ptr;
  (void)upb_Encode(UPB_UPCAST(msg), &xds__type__matcher__v3__Matcher__MatcherList__Predicate__SinglePredicate_msg_init, options, arena, &ptr, len);
  return ptr;
}
typedef enum {
  xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_matcher_value_match = 2,
  xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_matcher_custom_match = 3,
  xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_matcher_NOT_SET = 0
} xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_matcher_oneofcases;
UPB_INLINE xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_matcher_oneofcases xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_matcher_case(const xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate* msg) {
  const upb_MiniTableField field = {2, UPB_SIZE(20, 24), UPB_SIZE(-17, -13), 1, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  return (xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_matcher_oneofcases)upb_Message_WhichOneofFieldNumber(
      UPB_UPCAST(msg), &field);
}
UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_clear_input(xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate* msg) {
  const upb_MiniTableField field = {1, UPB_SIZE(12, 16), 64, 0, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  upb_Message_ClearBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE const struct xds_core_v3_TypedExtensionConfig* xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_input(const xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate* msg) {
  const struct xds_core_v3_TypedExtensionConfig* default_val = NULL;
  const struct xds_core_v3_TypedExtensionConfig* ret;
  const upb_MiniTableField field = {1, UPB_SIZE(12, 16), 64, 0, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__core__v3__TypedExtensionConfig_msg_init);
  _upb_Message_GetNonExtensionField(UPB_UPCAST(msg), &field,
                                    &default_val, &ret);
  return ret;
}
UPB_INLINE bool xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_has_input(const xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate* msg) {
  const upb_MiniTableField field = {1, UPB_SIZE(12, 16), 64, 0, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  return upb_Message_HasBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_clear_value_match(xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate* msg) {
  const upb_MiniTableField field = {2, UPB_SIZE(20, 24), UPB_SIZE(-17, -13), 1, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  upb_Message_ClearBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE const struct xds_type_matcher_v3_StringMatcher* xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_value_match(const xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate* msg) {
  const struct xds_type_matcher_v3_StringMatcher* default_val = NULL;
  const struct xds_type_matcher_v3_StringMatcher* ret;
  const upb_MiniTableField field = {2, UPB_SIZE(20, 24), UPB_SIZE(-17, -13), 1, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__StringMatcher_msg_init);
  _upb_Message_GetNonExtensionField(UPB_UPCAST(msg), &field,
                                    &default_val, &ret);
  return ret;
}
UPB_INLINE bool xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_has_value_match(const xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate* msg) {
  const upb_MiniTableField field = {2, UPB_SIZE(20, 24), UPB_SIZE(-17, -13), 1, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  return upb_Message_HasBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_clear_custom_match(xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate* msg) {
  const upb_MiniTableField field = {3, UPB_SIZE(20, 24), UPB_SIZE(-17, -13), 2, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  upb_Message_ClearBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE const struct xds_core_v3_TypedExtensionConfig* xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_custom_match(const xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate* msg) {
  const struct xds_core_v3_TypedExtensionConfig* default_val = NULL;
  const struct xds_core_v3_TypedExtensionConfig* ret;
  const upb_MiniTableField field = {3, UPB_SIZE(20, 24), UPB_SIZE(-17, -13), 2, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__core__v3__TypedExtensionConfig_msg_init);
  _upb_Message_GetNonExtensionField(UPB_UPCAST(msg), &field,
                                    &default_val, &ret);
  return ret;
}
UPB_INLINE bool xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_has_custom_match(const xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate* msg) {
  const upb_MiniTableField field = {3, UPB_SIZE(20, 24), UPB_SIZE(-17, -13), 2, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  return upb_Message_HasBaseField(UPB_UPCAST(msg), &field);
}

UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_set_input(xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate *msg, struct xds_core_v3_TypedExtensionConfig* value) {
  const upb_MiniTableField field = {1, UPB_SIZE(12, 16), 64, 0, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__core__v3__TypedExtensionConfig_msg_init);
  upb_Message_SetBaseField((upb_Message *)msg, &field, &value);
}
UPB_INLINE struct xds_core_v3_TypedExtensionConfig* xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_mutable_input(xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate* msg, upb_Arena* arena) {
  struct xds_core_v3_TypedExtensionConfig* sub = (struct xds_core_v3_TypedExtensionConfig*)xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_input(msg);
  if (sub == NULL) {
    sub = (struct xds_core_v3_TypedExtensionConfig*)_upb_Message_New(&xds__core__v3__TypedExtensionConfig_msg_init, arena);
    if (sub) xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_set_input(msg, sub);
  }
  return sub;
}
UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_set_value_match(xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate *msg, struct xds_type_matcher_v3_StringMatcher* value) {
  const upb_MiniTableField field = {2, UPB_SIZE(20, 24), UPB_SIZE(-17, -13), 1, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__StringMatcher_msg_init);
  upb_Message_SetBaseField((upb_Message *)msg, &field, &value);
}
UPB_INLINE struct xds_type_matcher_v3_StringMatcher* xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_mutable_value_match(xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate* msg, upb_Arena* arena) {
  struct xds_type_matcher_v3_StringMatcher* sub = (struct xds_type_matcher_v3_StringMatcher*)xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_value_match(msg);
  if (sub == NULL) {
    sub = (struct xds_type_matcher_v3_StringMatcher*)_upb_Message_New(&xds__type__matcher__v3__StringMatcher_msg_init, arena);
    if (sub) xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_set_value_match(msg, sub);
  }
  return sub;
}
UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_set_custom_match(xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate *msg, struct xds_core_v3_TypedExtensionConfig* value) {
  const upb_MiniTableField field = {3, UPB_SIZE(20, 24), UPB_SIZE(-17, -13), 2, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__core__v3__TypedExtensionConfig_msg_init);
  upb_Message_SetBaseField((upb_Message *)msg, &field, &value);
}
UPB_INLINE struct xds_core_v3_TypedExtensionConfig* xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_mutable_custom_match(xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate* msg, upb_Arena* arena) {
  struct xds_core_v3_TypedExtensionConfig* sub = (struct xds_core_v3_TypedExtensionConfig*)xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_custom_match(msg);
  if (sub == NULL) {
    sub = (struct xds_core_v3_TypedExtensionConfig*)_upb_Message_New(&xds__core__v3__TypedExtensionConfig_msg_init, arena);
    if (sub) xds_type_matcher_v3_Matcher_MatcherList_Predicate_SinglePredicate_set_custom_match(msg, sub);
  }
  return sub;
}

/* xds.type.matcher.v3.Matcher.MatcherList.Predicate.PredicateList */

UPB_INLINE xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList* xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList_new(upb_Arena* arena) {
  return (xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList*)_upb_Message_New(&xds__type__matcher__v3__Matcher__MatcherList__Predicate__PredicateList_msg_init, arena);
}
UPB_INLINE xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList* xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList_parse(const char* buf, size_t size, upb_Arena* arena) {
  xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList* ret = xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList_new(arena);
  if (!ret) return NULL;
  if (upb_Decode(buf, size, UPB_UPCAST(ret), &xds__type__matcher__v3__Matcher__MatcherList__Predicate__PredicateList_msg_init, NULL, 0, arena) !=
      kUpb_DecodeStatus_Ok) {
    return NULL;
  }
  return ret;
}
UPB_INLINE xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList* xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList_parse_ex(const char* buf, size_t size,
                           const upb_ExtensionRegistry* extreg,
                           int options, upb_Arena* arena) {
  xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList* ret = xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList_new(arena);
  if (!ret) return NULL;
  if (upb_Decode(buf, size, UPB_UPCAST(ret), &xds__type__matcher__v3__Matcher__MatcherList__Predicate__PredicateList_msg_init, extreg, options,
                 arena) != kUpb_DecodeStatus_Ok) {
    return NULL;
  }
  return ret;
}
UPB_INLINE char* xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList_serialize(const xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList* msg, upb_Arena* arena, size_t* len) {
  char* ptr;
  (void)upb_Encode(UPB_UPCAST(msg), &xds__type__matcher__v3__Matcher__MatcherList__Predicate__PredicateList_msg_init, 0, arena, &ptr, len);
  return ptr;
}
UPB_INLINE char* xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList_serialize_ex(const xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList* msg, int options,
                                 upb_Arena* arena, size_t* len) {
  char* ptr;
  (void)upb_Encode(UPB_UPCAST(msg), &xds__type__matcher__v3__Matcher__MatcherList__Predicate__PredicateList_msg_init, options, arena, &ptr, len);
  return ptr;
}
UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList_clear_predicate(xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList* msg) {
  const upb_MiniTableField field = {1, 8, 0, 0, 11, (int)kUpb_FieldMode_Array | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  upb_Message_ClearBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE const xds_type_matcher_v3_Matcher_MatcherList_Predicate* const* xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList_predicate(const xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList* msg, size_t* size) {
  const upb_MiniTableField field = {1, 8, 0, 0, 11, (int)kUpb_FieldMode_Array | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherList__Predicate_msg_init);
  const upb_Array* arr = upb_Message_GetArray(UPB_UPCAST(msg), &field);
  if (arr) {
    if (size) *size = arr->UPB_PRIVATE(size);
    return (const xds_type_matcher_v3_Matcher_MatcherList_Predicate* const*)upb_Array_DataPtr(arr);
  } else {
    if (size) *size = 0;
    return NULL;
  }
}
UPB_INLINE const upb_Array* _xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList_predicate_upb_array(const xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList* msg, size_t* size) {
  const upb_MiniTableField field = {1, 8, 0, 0, 11, (int)kUpb_FieldMode_Array | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherList__Predicate_msg_init);
  const upb_Array* arr = upb_Message_GetArray(UPB_UPCAST(msg), &field);
  if (size) {
    *size = arr ? arr->UPB_PRIVATE(size) : 0;
  }
  return arr;
}
UPB_INLINE upb_Array* _xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList_predicate_mutable_upb_array(xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList* msg, size_t* size, upb_Arena* arena) {
  const upb_MiniTableField field = {1, 8, 0, 0, 11, (int)kUpb_FieldMode_Array | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherList__Predicate_msg_init);
  upb_Array* arr = upb_Message_GetOrCreateMutableArray(UPB_UPCAST(msg),
                                                       &field, arena);
  if (size) {
    *size = arr ? arr->UPB_PRIVATE(size) : 0;
  }
  return arr;
}

UPB_INLINE xds_type_matcher_v3_Matcher_MatcherList_Predicate** xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList_mutable_predicate(xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList* msg, size_t* size) {
  upb_MiniTableField field = {1, 8, 0, 0, 11, (int)kUpb_FieldMode_Array | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherList__Predicate_msg_init);
  upb_Array* arr = upb_Message_GetMutableArray(UPB_UPCAST(msg), &field);
  if (arr) {
    if (size) *size = arr->UPB_PRIVATE(size);
    return (xds_type_matcher_v3_Matcher_MatcherList_Predicate**)upb_Array_MutableDataPtr(arr);
  } else {
    if (size) *size = 0;
    return NULL;
  }
}
UPB_INLINE xds_type_matcher_v3_Matcher_MatcherList_Predicate** xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList_resize_predicate(xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList* msg, size_t size, upb_Arena* arena) {
  upb_MiniTableField field = {1, 8, 0, 0, 11, (int)kUpb_FieldMode_Array | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  return (xds_type_matcher_v3_Matcher_MatcherList_Predicate**)upb_Message_ResizeArrayUninitialized(UPB_UPCAST(msg),
                                                   &field, size, arena);
}
UPB_INLINE struct xds_type_matcher_v3_Matcher_MatcherList_Predicate* xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList_add_predicate(xds_type_matcher_v3_Matcher_MatcherList_Predicate_PredicateList* msg, upb_Arena* arena) {
  upb_MiniTableField field = {1, 8, 0, 0, 11, (int)kUpb_FieldMode_Array | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherList__Predicate_msg_init);
  upb_Array* arr = upb_Message_GetOrCreateMutableArray(
      UPB_UPCAST(msg), &field, arena);
  if (!arr || !UPB_PRIVATE(_upb_Array_ResizeUninitialized)(
                  arr, arr->UPB_PRIVATE(size) + 1, arena)) {
    return NULL;
  }
  struct xds_type_matcher_v3_Matcher_MatcherList_Predicate* sub = (struct xds_type_matcher_v3_Matcher_MatcherList_Predicate*)_upb_Message_New(&xds__type__matcher__v3__Matcher__MatcherList__Predicate_msg_init, arena);
  if (!arr || !sub) return NULL;
  UPB_PRIVATE(_upb_Array_Set)
  (arr, arr->UPB_PRIVATE(size) - 1, &sub, sizeof(sub));
  return sub;
}

/* xds.type.matcher.v3.Matcher.MatcherList.FieldMatcher */

UPB_INLINE xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher* xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher_new(upb_Arena* arena) {
  return (xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher*)_upb_Message_New(&xds__type__matcher__v3__Matcher__MatcherList__FieldMatcher_msg_init, arena);
}
UPB_INLINE xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher* xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher_parse(const char* buf, size_t size, upb_Arena* arena) {
  xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher* ret = xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher_new(arena);
  if (!ret) return NULL;
  if (upb_Decode(buf, size, UPB_UPCAST(ret), &xds__type__matcher__v3__Matcher__MatcherList__FieldMatcher_msg_init, NULL, 0, arena) !=
      kUpb_DecodeStatus_Ok) {
    return NULL;
  }
  return ret;
}
UPB_INLINE xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher* xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher_parse_ex(const char* buf, size_t size,
                           const upb_ExtensionRegistry* extreg,
                           int options, upb_Arena* arena) {
  xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher* ret = xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher_new(arena);
  if (!ret) return NULL;
  if (upb_Decode(buf, size, UPB_UPCAST(ret), &xds__type__matcher__v3__Matcher__MatcherList__FieldMatcher_msg_init, extreg, options,
                 arena) != kUpb_DecodeStatus_Ok) {
    return NULL;
  }
  return ret;
}
UPB_INLINE char* xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher_serialize(const xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher* msg, upb_Arena* arena, size_t* len) {
  char* ptr;
  (void)upb_Encode(UPB_UPCAST(msg), &xds__type__matcher__v3__Matcher__MatcherList__FieldMatcher_msg_init, 0, arena, &ptr, len);
  return ptr;
}
UPB_INLINE char* xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher_serialize_ex(const xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher* msg, int options,
                                 upb_Arena* arena, size_t* len) {
  char* ptr;
  (void)upb_Encode(UPB_UPCAST(msg), &xds__type__matcher__v3__Matcher__MatcherList__FieldMatcher_msg_init, options, arena, &ptr, len);
  return ptr;
}
UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher_clear_predicate(xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher* msg) {
  const upb_MiniTableField field = {1, UPB_SIZE(12, 16), 64, 0, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  upb_Message_ClearBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE const xds_type_matcher_v3_Matcher_MatcherList_Predicate* xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher_predicate(const xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher* msg) {
  const xds_type_matcher_v3_Matcher_MatcherList_Predicate* default_val = NULL;
  const xds_type_matcher_v3_Matcher_MatcherList_Predicate* ret;
  const upb_MiniTableField field = {1, UPB_SIZE(12, 16), 64, 0, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherList__Predicate_msg_init);
  _upb_Message_GetNonExtensionField(UPB_UPCAST(msg), &field,
                                    &default_val, &ret);
  return ret;
}
UPB_INLINE bool xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher_has_predicate(const xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher* msg) {
  const upb_MiniTableField field = {1, UPB_SIZE(12, 16), 64, 0, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  return upb_Message_HasBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher_clear_on_match(xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher* msg) {
  const upb_MiniTableField field = {2, UPB_SIZE(16, 24), 65, 1, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  upb_Message_ClearBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE const xds_type_matcher_v3_Matcher_OnMatch* xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher_on_match(const xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher* msg) {
  const xds_type_matcher_v3_Matcher_OnMatch* default_val = NULL;
  const xds_type_matcher_v3_Matcher_OnMatch* ret;
  const upb_MiniTableField field = {2, UPB_SIZE(16, 24), 65, 1, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__OnMatch_msg_init);
  _upb_Message_GetNonExtensionField(UPB_UPCAST(msg), &field,
                                    &default_val, &ret);
  return ret;
}
UPB_INLINE bool xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher_has_on_match(const xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher* msg) {
  const upb_MiniTableField field = {2, UPB_SIZE(16, 24), 65, 1, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  return upb_Message_HasBaseField(UPB_UPCAST(msg), &field);
}

UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher_set_predicate(xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher *msg, xds_type_matcher_v3_Matcher_MatcherList_Predicate* value) {
  const upb_MiniTableField field = {1, UPB_SIZE(12, 16), 64, 0, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherList__Predicate_msg_init);
  upb_Message_SetBaseField((upb_Message *)msg, &field, &value);
}
UPB_INLINE struct xds_type_matcher_v3_Matcher_MatcherList_Predicate* xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher_mutable_predicate(xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher* msg, upb_Arena* arena) {
  struct xds_type_matcher_v3_Matcher_MatcherList_Predicate* sub = (struct xds_type_matcher_v3_Matcher_MatcherList_Predicate*)xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher_predicate(msg);
  if (sub == NULL) {
    sub = (struct xds_type_matcher_v3_Matcher_MatcherList_Predicate*)_upb_Message_New(&xds__type__matcher__v3__Matcher__MatcherList__Predicate_msg_init, arena);
    if (sub) xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher_set_predicate(msg, sub);
  }
  return sub;
}
UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher_set_on_match(xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher *msg, xds_type_matcher_v3_Matcher_OnMatch* value) {
  const upb_MiniTableField field = {2, UPB_SIZE(16, 24), 65, 1, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__OnMatch_msg_init);
  upb_Message_SetBaseField((upb_Message *)msg, &field, &value);
}
UPB_INLINE struct xds_type_matcher_v3_Matcher_OnMatch* xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher_mutable_on_match(xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher* msg, upb_Arena* arena) {
  struct xds_type_matcher_v3_Matcher_OnMatch* sub = (struct xds_type_matcher_v3_Matcher_OnMatch*)xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher_on_match(msg);
  if (sub == NULL) {
    sub = (struct xds_type_matcher_v3_Matcher_OnMatch*)_upb_Message_New(&xds__type__matcher__v3__Matcher__OnMatch_msg_init, arena);
    if (sub) xds_type_matcher_v3_Matcher_MatcherList_FieldMatcher_set_on_match(msg, sub);
  }
  return sub;
}

/* xds.type.matcher.v3.Matcher.MatcherTree */

UPB_INLINE xds_type_matcher_v3_Matcher_MatcherTree* xds_type_matcher_v3_Matcher_MatcherTree_new(upb_Arena* arena) {
  return (xds_type_matcher_v3_Matcher_MatcherTree*)_upb_Message_New(&xds__type__matcher__v3__Matcher__MatcherTree_msg_init, arena);
}
UPB_INLINE xds_type_matcher_v3_Matcher_MatcherTree* xds_type_matcher_v3_Matcher_MatcherTree_parse(const char* buf, size_t size, upb_Arena* arena) {
  xds_type_matcher_v3_Matcher_MatcherTree* ret = xds_type_matcher_v3_Matcher_MatcherTree_new(arena);
  if (!ret) return NULL;
  if (upb_Decode(buf, size, UPB_UPCAST(ret), &xds__type__matcher__v3__Matcher__MatcherTree_msg_init, NULL, 0, arena) !=
      kUpb_DecodeStatus_Ok) {
    return NULL;
  }
  return ret;
}
UPB_INLINE xds_type_matcher_v3_Matcher_MatcherTree* xds_type_matcher_v3_Matcher_MatcherTree_parse_ex(const char* buf, size_t size,
                           const upb_ExtensionRegistry* extreg,
                           int options, upb_Arena* arena) {
  xds_type_matcher_v3_Matcher_MatcherTree* ret = xds_type_matcher_v3_Matcher_MatcherTree_new(arena);
  if (!ret) return NULL;
  if (upb_Decode(buf, size, UPB_UPCAST(ret), &xds__type__matcher__v3__Matcher__MatcherTree_msg_init, extreg, options,
                 arena) != kUpb_DecodeStatus_Ok) {
    return NULL;
  }
  return ret;
}
UPB_INLINE char* xds_type_matcher_v3_Matcher_MatcherTree_serialize(const xds_type_matcher_v3_Matcher_MatcherTree* msg, upb_Arena* arena, size_t* len) {
  char* ptr;
  (void)upb_Encode(UPB_UPCAST(msg), &xds__type__matcher__v3__Matcher__MatcherTree_msg_init, 0, arena, &ptr, len);
  return ptr;
}
UPB_INLINE char* xds_type_matcher_v3_Matcher_MatcherTree_serialize_ex(const xds_type_matcher_v3_Matcher_MatcherTree* msg, int options,
                                 upb_Arena* arena, size_t* len) {
  char* ptr;
  (void)upb_Encode(UPB_UPCAST(msg), &xds__type__matcher__v3__Matcher__MatcherTree_msg_init, options, arena, &ptr, len);
  return ptr;
}
typedef enum {
  xds_type_matcher_v3_Matcher_MatcherTree_tree_type_exact_match_map = 2,
  xds_type_matcher_v3_Matcher_MatcherTree_tree_type_prefix_match_map = 3,
  xds_type_matcher_v3_Matcher_MatcherTree_tree_type_custom_match = 4,
  xds_type_matcher_v3_Matcher_MatcherTree_tree_type_NOT_SET = 0
} xds_type_matcher_v3_Matcher_MatcherTree_tree_type_oneofcases;
UPB_INLINE xds_type_matcher_v3_Matcher_MatcherTree_tree_type_oneofcases xds_type_matcher_v3_Matcher_MatcherTree_tree_type_case(const xds_type_matcher_v3_Matcher_MatcherTree* msg) {
  const upb_MiniTableField field = {2, UPB_SIZE(20, 24), UPB_SIZE(-17, -13), 1, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  return (xds_type_matcher_v3_Matcher_MatcherTree_tree_type_oneofcases)upb_Message_WhichOneofFieldNumber(
      UPB_UPCAST(msg), &field);
}
UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherTree_clear_input(xds_type_matcher_v3_Matcher_MatcherTree* msg) {
  const upb_MiniTableField field = {1, UPB_SIZE(12, 16), 64, 0, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  upb_Message_ClearBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE const struct xds_core_v3_TypedExtensionConfig* xds_type_matcher_v3_Matcher_MatcherTree_input(const xds_type_matcher_v3_Matcher_MatcherTree* msg) {
  const struct xds_core_v3_TypedExtensionConfig* default_val = NULL;
  const struct xds_core_v3_TypedExtensionConfig* ret;
  const upb_MiniTableField field = {1, UPB_SIZE(12, 16), 64, 0, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__core__v3__TypedExtensionConfig_msg_init);
  _upb_Message_GetNonExtensionField(UPB_UPCAST(msg), &field,
                                    &default_val, &ret);
  return ret;
}
UPB_INLINE bool xds_type_matcher_v3_Matcher_MatcherTree_has_input(const xds_type_matcher_v3_Matcher_MatcherTree* msg) {
  const upb_MiniTableField field = {1, UPB_SIZE(12, 16), 64, 0, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  return upb_Message_HasBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherTree_clear_exact_match_map(xds_type_matcher_v3_Matcher_MatcherTree* msg) {
  const upb_MiniTableField field = {2, UPB_SIZE(20, 24), UPB_SIZE(-17, -13), 1, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  upb_Message_ClearBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE const xds_type_matcher_v3_Matcher_MatcherTree_MatchMap* xds_type_matcher_v3_Matcher_MatcherTree_exact_match_map(const xds_type_matcher_v3_Matcher_MatcherTree* msg) {
  const xds_type_matcher_v3_Matcher_MatcherTree_MatchMap* default_val = NULL;
  const xds_type_matcher_v3_Matcher_MatcherTree_MatchMap* ret;
  const upb_MiniTableField field = {2, UPB_SIZE(20, 24), UPB_SIZE(-17, -13), 1, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherTree__MatchMap_msg_init);
  _upb_Message_GetNonExtensionField(UPB_UPCAST(msg), &field,
                                    &default_val, &ret);
  return ret;
}
UPB_INLINE bool xds_type_matcher_v3_Matcher_MatcherTree_has_exact_match_map(const xds_type_matcher_v3_Matcher_MatcherTree* msg) {
  const upb_MiniTableField field = {2, UPB_SIZE(20, 24), UPB_SIZE(-17, -13), 1, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  return upb_Message_HasBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherTree_clear_prefix_match_map(xds_type_matcher_v3_Matcher_MatcherTree* msg) {
  const upb_MiniTableField field = {3, UPB_SIZE(20, 24), UPB_SIZE(-17, -13), 2, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  upb_Message_ClearBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE const xds_type_matcher_v3_Matcher_MatcherTree_MatchMap* xds_type_matcher_v3_Matcher_MatcherTree_prefix_match_map(const xds_type_matcher_v3_Matcher_MatcherTree* msg) {
  const xds_type_matcher_v3_Matcher_MatcherTree_MatchMap* default_val = NULL;
  const xds_type_matcher_v3_Matcher_MatcherTree_MatchMap* ret;
  const upb_MiniTableField field = {3, UPB_SIZE(20, 24), UPB_SIZE(-17, -13), 2, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherTree__MatchMap_msg_init);
  _upb_Message_GetNonExtensionField(UPB_UPCAST(msg), &field,
                                    &default_val, &ret);
  return ret;
}
UPB_INLINE bool xds_type_matcher_v3_Matcher_MatcherTree_has_prefix_match_map(const xds_type_matcher_v3_Matcher_MatcherTree* msg) {
  const upb_MiniTableField field = {3, UPB_SIZE(20, 24), UPB_SIZE(-17, -13), 2, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  return upb_Message_HasBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherTree_clear_custom_match(xds_type_matcher_v3_Matcher_MatcherTree* msg) {
  const upb_MiniTableField field = {4, UPB_SIZE(20, 24), UPB_SIZE(-17, -13), 3, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  upb_Message_ClearBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE const struct xds_core_v3_TypedExtensionConfig* xds_type_matcher_v3_Matcher_MatcherTree_custom_match(const xds_type_matcher_v3_Matcher_MatcherTree* msg) {
  const struct xds_core_v3_TypedExtensionConfig* default_val = NULL;
  const struct xds_core_v3_TypedExtensionConfig* ret;
  const upb_MiniTableField field = {4, UPB_SIZE(20, 24), UPB_SIZE(-17, -13), 3, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__core__v3__TypedExtensionConfig_msg_init);
  _upb_Message_GetNonExtensionField(UPB_UPCAST(msg), &field,
                                    &default_val, &ret);
  return ret;
}
UPB_INLINE bool xds_type_matcher_v3_Matcher_MatcherTree_has_custom_match(const xds_type_matcher_v3_Matcher_MatcherTree* msg) {
  const upb_MiniTableField field = {4, UPB_SIZE(20, 24), UPB_SIZE(-17, -13), 3, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  return upb_Message_HasBaseField(UPB_UPCAST(msg), &field);
}

UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherTree_set_input(xds_type_matcher_v3_Matcher_MatcherTree *msg, struct xds_core_v3_TypedExtensionConfig* value) {
  const upb_MiniTableField field = {1, UPB_SIZE(12, 16), 64, 0, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__core__v3__TypedExtensionConfig_msg_init);
  upb_Message_SetBaseField((upb_Message *)msg, &field, &value);
}
UPB_INLINE struct xds_core_v3_TypedExtensionConfig* xds_type_matcher_v3_Matcher_MatcherTree_mutable_input(xds_type_matcher_v3_Matcher_MatcherTree* msg, upb_Arena* arena) {
  struct xds_core_v3_TypedExtensionConfig* sub = (struct xds_core_v3_TypedExtensionConfig*)xds_type_matcher_v3_Matcher_MatcherTree_input(msg);
  if (sub == NULL) {
    sub = (struct xds_core_v3_TypedExtensionConfig*)_upb_Message_New(&xds__core__v3__TypedExtensionConfig_msg_init, arena);
    if (sub) xds_type_matcher_v3_Matcher_MatcherTree_set_input(msg, sub);
  }
  return sub;
}
UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherTree_set_exact_match_map(xds_type_matcher_v3_Matcher_MatcherTree *msg, xds_type_matcher_v3_Matcher_MatcherTree_MatchMap* value) {
  const upb_MiniTableField field = {2, UPB_SIZE(20, 24), UPB_SIZE(-17, -13), 1, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherTree__MatchMap_msg_init);
  upb_Message_SetBaseField((upb_Message *)msg, &field, &value);
}
UPB_INLINE struct xds_type_matcher_v3_Matcher_MatcherTree_MatchMap* xds_type_matcher_v3_Matcher_MatcherTree_mutable_exact_match_map(xds_type_matcher_v3_Matcher_MatcherTree* msg, upb_Arena* arena) {
  struct xds_type_matcher_v3_Matcher_MatcherTree_MatchMap* sub = (struct xds_type_matcher_v3_Matcher_MatcherTree_MatchMap*)xds_type_matcher_v3_Matcher_MatcherTree_exact_match_map(msg);
  if (sub == NULL) {
    sub = (struct xds_type_matcher_v3_Matcher_MatcherTree_MatchMap*)_upb_Message_New(&xds__type__matcher__v3__Matcher__MatcherTree__MatchMap_msg_init, arena);
    if (sub) xds_type_matcher_v3_Matcher_MatcherTree_set_exact_match_map(msg, sub);
  }
  return sub;
}
UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherTree_set_prefix_match_map(xds_type_matcher_v3_Matcher_MatcherTree *msg, xds_type_matcher_v3_Matcher_MatcherTree_MatchMap* value) {
  const upb_MiniTableField field = {3, UPB_SIZE(20, 24), UPB_SIZE(-17, -13), 2, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherTree__MatchMap_msg_init);
  upb_Message_SetBaseField((upb_Message *)msg, &field, &value);
}
UPB_INLINE struct xds_type_matcher_v3_Matcher_MatcherTree_MatchMap* xds_type_matcher_v3_Matcher_MatcherTree_mutable_prefix_match_map(xds_type_matcher_v3_Matcher_MatcherTree* msg, upb_Arena* arena) {
  struct xds_type_matcher_v3_Matcher_MatcherTree_MatchMap* sub = (struct xds_type_matcher_v3_Matcher_MatcherTree_MatchMap*)xds_type_matcher_v3_Matcher_MatcherTree_prefix_match_map(msg);
  if (sub == NULL) {
    sub = (struct xds_type_matcher_v3_Matcher_MatcherTree_MatchMap*)_upb_Message_New(&xds__type__matcher__v3__Matcher__MatcherTree__MatchMap_msg_init, arena);
    if (sub) xds_type_matcher_v3_Matcher_MatcherTree_set_prefix_match_map(msg, sub);
  }
  return sub;
}
UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherTree_set_custom_match(xds_type_matcher_v3_Matcher_MatcherTree *msg, struct xds_core_v3_TypedExtensionConfig* value) {
  const upb_MiniTableField field = {4, UPB_SIZE(20, 24), UPB_SIZE(-17, -13), 3, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__core__v3__TypedExtensionConfig_msg_init);
  upb_Message_SetBaseField((upb_Message *)msg, &field, &value);
}
UPB_INLINE struct xds_core_v3_TypedExtensionConfig* xds_type_matcher_v3_Matcher_MatcherTree_mutable_custom_match(xds_type_matcher_v3_Matcher_MatcherTree* msg, upb_Arena* arena) {
  struct xds_core_v3_TypedExtensionConfig* sub = (struct xds_core_v3_TypedExtensionConfig*)xds_type_matcher_v3_Matcher_MatcherTree_custom_match(msg);
  if (sub == NULL) {
    sub = (struct xds_core_v3_TypedExtensionConfig*)_upb_Message_New(&xds__core__v3__TypedExtensionConfig_msg_init, arena);
    if (sub) xds_type_matcher_v3_Matcher_MatcherTree_set_custom_match(msg, sub);
  }
  return sub;
}

/* xds.type.matcher.v3.Matcher.MatcherTree.MatchMap */

UPB_INLINE xds_type_matcher_v3_Matcher_MatcherTree_MatchMap* xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_new(upb_Arena* arena) {
  return (xds_type_matcher_v3_Matcher_MatcherTree_MatchMap*)_upb_Message_New(&xds__type__matcher__v3__Matcher__MatcherTree__MatchMap_msg_init, arena);
}
UPB_INLINE xds_type_matcher_v3_Matcher_MatcherTree_MatchMap* xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_parse(const char* buf, size_t size, upb_Arena* arena) {
  xds_type_matcher_v3_Matcher_MatcherTree_MatchMap* ret = xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_new(arena);
  if (!ret) return NULL;
  if (upb_Decode(buf, size, UPB_UPCAST(ret), &xds__type__matcher__v3__Matcher__MatcherTree__MatchMap_msg_init, NULL, 0, arena) !=
      kUpb_DecodeStatus_Ok) {
    return NULL;
  }
  return ret;
}
UPB_INLINE xds_type_matcher_v3_Matcher_MatcherTree_MatchMap* xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_parse_ex(const char* buf, size_t size,
                           const upb_ExtensionRegistry* extreg,
                           int options, upb_Arena* arena) {
  xds_type_matcher_v3_Matcher_MatcherTree_MatchMap* ret = xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_new(arena);
  if (!ret) return NULL;
  if (upb_Decode(buf, size, UPB_UPCAST(ret), &xds__type__matcher__v3__Matcher__MatcherTree__MatchMap_msg_init, extreg, options,
                 arena) != kUpb_DecodeStatus_Ok) {
    return NULL;
  }
  return ret;
}
UPB_INLINE char* xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_serialize(const xds_type_matcher_v3_Matcher_MatcherTree_MatchMap* msg, upb_Arena* arena, size_t* len) {
  char* ptr;
  (void)upb_Encode(UPB_UPCAST(msg), &xds__type__matcher__v3__Matcher__MatcherTree__MatchMap_msg_init, 0, arena, &ptr, len);
  return ptr;
}
UPB_INLINE char* xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_serialize_ex(const xds_type_matcher_v3_Matcher_MatcherTree_MatchMap* msg, int options,
                                 upb_Arena* arena, size_t* len) {
  char* ptr;
  (void)upb_Encode(UPB_UPCAST(msg), &xds__type__matcher__v3__Matcher__MatcherTree__MatchMap_msg_init, options, arena, &ptr, len);
  return ptr;
}
UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_clear_map(xds_type_matcher_v3_Matcher_MatcherTree_MatchMap* msg) {
  const upb_MiniTableField field = {1, 8, 0, 0, 11, (int)kUpb_FieldMode_Map | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  upb_Message_ClearBaseField(UPB_UPCAST(msg), &field);
}
UPB_INLINE size_t xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_map_size(const xds_type_matcher_v3_Matcher_MatcherTree_MatchMap* msg) {
  const upb_MiniTableField field = {1, 8, 0, 0, 11, (int)kUpb_FieldMode_Map | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  const upb_Map* map = upb_Message_GetMap(UPB_UPCAST(msg), &field);
  return map ? _upb_Map_Size(map) : 0;
}
UPB_INLINE bool xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_map_get(const xds_type_matcher_v3_Matcher_MatcherTree_MatchMap* msg, upb_StringView key, xds_type_matcher_v3_Matcher_OnMatch** val) {
  const upb_MiniTableField field = {1, 8, 0, 0, 11, (int)kUpb_FieldMode_Map | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherTree__MatchMap__MapEntry_msg_init);
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__OnMatch_msg_init);
  const upb_Map* map = upb_Message_GetMap(UPB_UPCAST(msg), &field);
  if (!map) return false;
  return _upb_Map_Get(map, &key, 0, val, sizeof(*val));
}
UPB_INLINE const xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_MapEntry* xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_map_next(const xds_type_matcher_v3_Matcher_MatcherTree_MatchMap* msg, size_t* iter) {
  const upb_MiniTableField field = {1, 8, 0, 0, 11, (int)kUpb_FieldMode_Map | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherTree__MatchMap__MapEntry_msg_init);
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__OnMatch_msg_init);
  const upb_Map* map = upb_Message_GetMap(UPB_UPCAST(msg), &field);
  if (!map) return NULL;
  return (const xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_MapEntry*)_upb_map_next(map, iter);
}
UPB_INLINE const upb_Map* _xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_map_upb_map(xds_type_matcher_v3_Matcher_MatcherTree_MatchMap* msg) {
  const upb_MiniTableField field = {1, 8, 0, 0, 11, (int)kUpb_FieldMode_Map | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherTree__MatchMap__MapEntry_msg_init);
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__OnMatch_msg_init);
  return upb_Message_GetMap(UPB_UPCAST(msg), &field);
}
UPB_INLINE upb_Map* _xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_map_mutable_upb_map(xds_type_matcher_v3_Matcher_MatcherTree_MatchMap* msg, upb_Arena* a) {
  const upb_MiniTableField field = {1, 8, 0, 0, 11, (int)kUpb_FieldMode_Map | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherTree__MatchMap__MapEntry_msg_init);
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__OnMatch_msg_init);
  return _upb_Message_GetOrCreateMutableMap(UPB_UPCAST(msg), &field, 0, sizeof(xds_type_matcher_v3_Matcher_OnMatch*), a);
}

UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_map_clear(xds_type_matcher_v3_Matcher_MatcherTree_MatchMap* msg) {
  const upb_MiniTableField field = {1, 8, 0, 0, 11, (int)kUpb_FieldMode_Map | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  upb_Map* map = (upb_Map*)upb_Message_GetMap(UPB_UPCAST(msg), &field);
  if (!map) return;
  _upb_Map_Clear(map);
}
UPB_INLINE bool xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_map_set(xds_type_matcher_v3_Matcher_MatcherTree_MatchMap* msg, upb_StringView key, xds_type_matcher_v3_Matcher_OnMatch* val, upb_Arena* a) {
  const upb_MiniTableField field = {1, 8, 0, 0, 11, (int)kUpb_FieldMode_Map | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherTree__MatchMap__MapEntry_msg_init);
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__OnMatch_msg_init);
  upb_Map* map = _upb_Message_GetOrCreateMutableMap(UPB_UPCAST(msg),
                                                    &field, 0, sizeof(val), a);
  return _upb_Map_Insert(map, &key, 0, &val, sizeof(val), a) !=
         kUpb_MapInsertStatus_OutOfMemory;
}
UPB_INLINE bool xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_map_delete(xds_type_matcher_v3_Matcher_MatcherTree_MatchMap* msg, upb_StringView key) {
  const upb_MiniTableField field = {1, 8, 0, 0, 11, (int)kUpb_FieldMode_Map | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  upb_Map* map = (upb_Map*)upb_Message_GetMap(UPB_UPCAST(msg), &field);
  if (!map) return false;
  return _upb_Map_Delete(map, &key, 0, NULL);
}
UPB_INLINE xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_MapEntry* xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_map_nextmutable(xds_type_matcher_v3_Matcher_MatcherTree_MatchMap* msg, size_t* iter) {
  const upb_MiniTableField field = {1, 8, 0, 0, 11, (int)kUpb_FieldMode_Map | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__MatcherTree__MatchMap__MapEntry_msg_init);
  UPB_PRIVATE(_upb_MiniTable_StrongReference)(&xds__type__matcher__v3__Matcher__OnMatch_msg_init);
  upb_Map* map = (upb_Map*)upb_Message_GetMap(UPB_UPCAST(msg), &field);
  if (!map) return NULL;
  return (xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_MapEntry*)_upb_map_next(map, iter);
}

/* xds.type.matcher.v3.Matcher.MatcherTree.MatchMap.MapEntry */

UPB_INLINE upb_StringView xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_MapEntry_key(const xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_MapEntry* msg) {
  upb_StringView ret;
  _upb_msg_map_key(msg, &ret, 0);
  return ret;
}
UPB_INLINE const xds_type_matcher_v3_Matcher_OnMatch* xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_MapEntry_value(const xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_MapEntry* msg) {
  xds_type_matcher_v3_Matcher_OnMatch* ret;
  _upb_msg_map_value(msg, &ret, sizeof(ret));
  return ret;
}
UPB_INLINE bool xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_MapEntry_has_value(const xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_MapEntry* msg) {
  const upb_MiniTableField field = {2, 32, 64, 0, 11, (int)kUpb_FieldMode_Scalar | ((int)UPB_SIZE(kUpb_FieldRep_4Byte, kUpb_FieldRep_8Byte) << kUpb_FieldRep_Shift)};
  return upb_Message_HasBaseField(UPB_UPCAST(msg), &field);
}

UPB_INLINE void xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_MapEntry_set_value(xds_type_matcher_v3_Matcher_MatcherTree_MatchMap_MapEntry *msg, xds_type_matcher_v3_Matcher_OnMatch* value) {
  _upb_msg_map_set_value(msg, &value, sizeof(xds_type_matcher_v3_Matcher_OnMatch*));
}

#ifdef __cplusplus
}  /* extern "C" */
#endif

#include "upb/port/undef.inc"

#endif  /* XDS_TYPE_MATCHER_V3_MATCHER_PROTO_UPB_H__UPB_H_ */
