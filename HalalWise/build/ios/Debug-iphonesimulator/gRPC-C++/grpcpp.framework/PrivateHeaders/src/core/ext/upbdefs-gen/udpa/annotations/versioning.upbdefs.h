/* This file was generated by upb_generator from the input file:
 *
 *     udpa/annotations/versioning.proto
 *
 * Do not edit -- your changes will be discarded when the file is
 * regenerated.
 * NO CHECKED-IN PROTOBUF GENCODE */

#ifndef UDPA_ANNOTATIONS_VERSIONING_PROTO_UPB_H__UPBDEFS_H_
#define UDPA_ANNOTATIONS_VERSIONING_PROTO_UPB_H__UPBDEFS_H_

#include "upb/reflection/def.h"
#include "upb/reflection/internal/def_pool.h"

#include "upb/port/def.inc" // Must be last.
#ifdef __cplusplus
extern "C" {
#endif

extern _upb_DefPool_Init udpa_annotations_versioning_proto_upbdefinit;

UPB_INLINE const upb_MessageDef *udpa_annotations_VersioningAnnotation_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &udpa_annotations_versioning_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "udpa.annotations.VersioningAnnotation");
}

#ifdef __cplusplus
}  /* extern "C" */
#endif

#include "upb/port/undef.inc"

#endif  /* UDPA_ANNOTATIONS_VERSIONING_PROTO_UPB_H__UPBDEFS_H_ */
