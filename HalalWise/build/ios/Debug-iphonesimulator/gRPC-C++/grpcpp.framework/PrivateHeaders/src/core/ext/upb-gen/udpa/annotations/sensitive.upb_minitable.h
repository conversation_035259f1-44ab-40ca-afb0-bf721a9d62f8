/* This file was generated by upb_generator from the input file:
 *
 *     udpa/annotations/sensitive.proto
 *
 * Do not edit -- your changes will be discarded when the file is
 * regenerated.
 * NO CHECKED-IN PROTOBUF GENCODE */

#ifndef UDPA_ANNOTATIONS_SENSITIVE_PROTO_UPB_H__UPB_MINITABLE_H_
#define UDPA_ANNOTATIONS_SENSITIVE_PROTO_UPB_H__UPB_MINITABLE_H_

#include "upb/generated_code_support.h"

// Must be last.
#include "upb/port/def.inc"

#ifdef __cplusplus
extern "C" {
#endif

extern const upb_MiniTableExtension udpa_annotations_sensitive_ext;

extern const upb_MiniTableFile udpa_annotations_sensitive_proto_upb_file_layout;

#ifdef __cplusplus
}  /* extern "C" */
#endif

#include "upb/port/undef.inc"

#endif  /* UDPA_ANNOTATIONS_SENSITIVE_PROTO_UPB_H__UPB_MINITABLE_H_ */
