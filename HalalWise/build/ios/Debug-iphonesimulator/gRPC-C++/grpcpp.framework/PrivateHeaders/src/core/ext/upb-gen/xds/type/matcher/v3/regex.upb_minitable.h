/* This file was generated by upb_generator from the input file:
 *
 *     xds/type/matcher/v3/regex.proto
 *
 * Do not edit -- your changes will be discarded when the file is
 * regenerated.
 * NO CHECKED-IN PROTOBUF GENCODE */

#ifndef XDS_TYPE_MATCHER_V3_REGEX_PROTO_UPB_H__UPB_MINITABLE_H_
#define XDS_TYPE_MATCHER_V3_REGEX_PROTO_UPB_H__UPB_MINITABLE_H_

#include "upb/generated_code_support.h"

// Must be last.
#include "upb/port/def.inc"

#ifdef __cplusplus
extern "C" {
#endif

extern const upb_MiniTable xds__type__matcher__v3__RegexMatcher_msg_init;
extern const upb_MiniTable* xds__type__matcher__v3__RegexMatcher_msg_init_ptr;
extern const upb_MiniTable xds__type__matcher__v3__RegexMatcher__GoogleRE2_msg_init;
extern const upb_MiniTable* xds__type__matcher__v3__RegexMatcher__GoogleRE2_msg_init_ptr;

extern const upb_MiniTableFile xds_type_matcher_v3_regex_proto_upb_file_layout;

#ifdef __cplusplus
}  /* extern "C" */
#endif

#include "upb/port/undef.inc"

#endif  /* XDS_TYPE_MATCHER_V3_REGEX_PROTO_UPB_H__UPB_MINITABLE_H_ */
