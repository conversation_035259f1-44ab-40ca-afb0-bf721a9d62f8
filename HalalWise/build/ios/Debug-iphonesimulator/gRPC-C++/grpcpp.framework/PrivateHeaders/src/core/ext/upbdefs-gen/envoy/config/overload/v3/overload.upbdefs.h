/* This file was generated by upb_generator from the input file:
 *
 *     envoy/config/overload/v3/overload.proto
 *
 * Do not edit -- your changes will be discarded when the file is
 * regenerated.
 * NO CHECKED-IN PROTOBUF GENCODE */

#ifndef ENVOY_CONFIG_OVERLOAD_V3_OVERLOAD_PROTO_UPB_H__UPBDEFS_H_
#define ENVOY_CONFIG_OVERLOAD_V3_OVERLOAD_PROTO_UPB_H__UPBDEFS_H_

#include "upb/reflection/def.h"
#include "upb/reflection/internal/def_pool.h"

#include "upb/port/def.inc" // Must be last.
#ifdef __cplusplus
extern "C" {
#endif

extern _upb_DefPool_Init envoy_config_overload_v3_overload_proto_upbdefinit;

UPB_INLINE const upb_MessageDef *envoy_config_overload_v3_ResourceMonitor_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_overload_v3_overload_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.overload.v3.ResourceMonitor");
}

UPB_INLINE const upb_MessageDef *envoy_config_overload_v3_ThresholdTrigger_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_overload_v3_overload_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.overload.v3.ThresholdTrigger");
}

UPB_INLINE const upb_MessageDef *envoy_config_overload_v3_ScaledTrigger_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_overload_v3_overload_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.overload.v3.ScaledTrigger");
}

UPB_INLINE const upb_MessageDef *envoy_config_overload_v3_Trigger_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_overload_v3_overload_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.overload.v3.Trigger");
}

UPB_INLINE const upb_MessageDef *envoy_config_overload_v3_ScaleTimersOverloadActionConfig_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_overload_v3_overload_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.overload.v3.ScaleTimersOverloadActionConfig");
}

UPB_INLINE const upb_MessageDef *envoy_config_overload_v3_ScaleTimersOverloadActionConfig_ScaleTimer_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_overload_v3_overload_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.overload.v3.ScaleTimersOverloadActionConfig.ScaleTimer");
}

UPB_INLINE const upb_MessageDef *envoy_config_overload_v3_OverloadAction_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_overload_v3_overload_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.overload.v3.OverloadAction");
}

UPB_INLINE const upb_MessageDef *envoy_config_overload_v3_LoadShedPoint_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_overload_v3_overload_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.overload.v3.LoadShedPoint");
}

UPB_INLINE const upb_MessageDef *envoy_config_overload_v3_BufferFactoryConfig_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_overload_v3_overload_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.overload.v3.BufferFactoryConfig");
}

UPB_INLINE const upb_MessageDef *envoy_config_overload_v3_OverloadManager_getmsgdef(upb_DefPool *s) {
  _upb_DefPool_LoadDefInit(s, &envoy_config_overload_v3_overload_proto_upbdefinit);
  return upb_DefPool_FindMessageByName(s, "envoy.config.overload.v3.OverloadManager");
}

#ifdef __cplusplus
}  /* extern "C" */
#endif

#include "upb/port/undef.inc"

#endif  /* ENVOY_CONFIG_OVERLOAD_V3_OVERLOAD_PROTO_UPB_H__UPBDEFS_H_ */
