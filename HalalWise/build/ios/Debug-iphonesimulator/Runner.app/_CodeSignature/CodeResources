<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>AppFrameworkInfo.plist</key>
		<data>
		mnLbgBhrpRwdlXh4UKzYj73lYuA=
		</data>
		<key><EMAIL></key>
		<data>
		WBDcNG/0BUOtsKQgKYOLyuqAbAM=
		</data>
		<key>AppIcon76x76@2x~ipad.png</key>
		<data>
		Bqtil6RquU1Hfn8gu0IYARWvCIM=
		</data>
		<key>Assets.car</key>
		<data>
		lOre7xmf1bdBg9wMp7fxPO2NKgo=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<data>
		28xWMBQ91UzszfdXY91SqhC7ecg=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/Info.plist</key>
		<data>
		n2t8gsDpfE6XkhG31p7IQJRxTxU=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<data>
		ZVgM1+KwZcZnwhgaI0F7Bt1ba2c=
		</data>
		<key>Base.lproj/Main.storyboardc/BYZ-38-t0r-view-8bC-Xf-vdC.nib</key>
		<data>
		hMnf/VIyTGR2nRcoLS3JCfeGmDs=
		</data>
		<key>Base.lproj/Main.storyboardc/Info.plist</key>
		<data>
		MDrKFvFWroTb0+KEbQShBcoBvo4=
		</data>
		<key>Base.lproj/Main.storyboardc/UIViewController-BYZ-38-t0r.nib</key>
		<data>
		nFC1waP0YzYOchnqa85lPwrC73s=
		</data>
		<key>Frameworks/App.framework/App</key>
		<data>
		iCeE5NXbH/wEEqnTvzgYy7yTYDA=
		</data>
		<key>Frameworks/App.framework/Info.plist</key>
		<data>
		h5OB7aKzS5WR9SemvZAyN6FEkJs=
		</data>
		<key>Frameworks/App.framework/_CodeSignature/CodeResources</key>
		<data>
		eqyIKASqwebsJ/ZP5W0+HrX7tJI=
		</data>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.bin</key>
		<data>
		ME0cAg6cl/bTZmwXEVgMugDccUI=
		</data>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.json</key>
		<data>
		oG5/OGn+vw7vp/nu5DUDoZJ4nFc=
		</data>
		<key>Frameworks/App.framework/flutter_assets/FontManifest.json</key>
		<data>
		vKJkVIcw+LGHFnKJGwrQwCREv68=
		</data>
		<key>Frameworks/App.framework/flutter_assets/NOTICES.Z</key>
		<data>
		EnHwW8jhvr2oYFXWLIKbS2MHsMU=
		</data>
		<key>Frameworks/App.framework/flutter_assets/NativeAssetsManifest.json</key>
		<data>
		re4p7E8rPLLsN+wzaPN/+AVpXTY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<data>
		/CUoTuPQqqdexfyOT9lpJhV+2MQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/isolate_snapshot_data</key>
		<data>
		PgYZNgZ7nxC3tFt+nGCEL6wxuNY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/kernel_blob.bin</key>
		<data>
		QaDRcdo6eaihIrjQocYeTcMOAZg=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf</key>
		<data>
		Bvk+P1ykE1PGRdktwgwDyz6AOqM=
		</data>
		<key>Frameworks/App.framework/flutter_assets/shaders/ink_sparkle.frag</key>
		<data>
		VvTF10G1gIeea4aI0DhJjCjHgXQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/vm_snapshot_data</key>
		<data>
		SoHnDHqzNKozN7o/zhsvOoXVErY=
		</data>
		<key>Frameworks/FirebaseAppCheckInterop.framework/FirebaseAppCheckInterop</key>
		<data>
		NQXLk5KcPpDbBrXIMMf601v/usc=
		</data>
		<key>Frameworks/FirebaseAppCheckInterop.framework/Info.plist</key>
		<data>
		kI2YN1075/vY2hSshtIn1OclcQI=
		</data>
		<key>Frameworks/FirebaseAppCheckInterop.framework/_CodeSignature/CodeResources</key>
		<data>
		LJIs79dRIcsorgDEyy3FR6rMxvc=
		</data>
		<key>Frameworks/FirebaseCore.framework/FirebaseCore</key>
		<data>
		ptE0EGs4pM3lREeyoBCEB5gBQII=
		</data>
		<key>Frameworks/FirebaseCore.framework/FirebaseCore_Privacy.bundle/Info.plist</key>
		<data>
		vOGVdbLQkGSExJb+7Lh7moq0jPM=
		</data>
		<key>Frameworks/FirebaseCore.framework/FirebaseCore_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		sa2OhFlqdCIyz9oV7fUdDKWzFL0=
		</data>
		<key>Frameworks/FirebaseCore.framework/Info.plist</key>
		<data>
		2kHQsgpUHV//6/D+l51b17d0QkU=
		</data>
		<key>Frameworks/FirebaseCore.framework/_CodeSignature/CodeResources</key>
		<data>
		REGhnXJZgis+nzfWoWJyicoCswg=
		</data>
		<key>Frameworks/FirebaseCoreExtension.framework/FirebaseCoreExtension</key>
		<data>
		X9RLjFuygjg4BfP2CBnsL2rFrcM=
		</data>
		<key>Frameworks/FirebaseCoreExtension.framework/FirebaseCoreExtension_Privacy.bundle/Info.plist</key>
		<data>
		dtBudRqFKD49GtKJHxFHd6Jsaz4=
		</data>
		<key>Frameworks/FirebaseCoreExtension.framework/FirebaseCoreExtension_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		6iBlSEWzrD6zouHx4NqeDhjKSGQ=
		</data>
		<key>Frameworks/FirebaseCoreExtension.framework/Info.plist</key>
		<data>
		ujK0prjXjiqMtsDWOauI5wqrRxE=
		</data>
		<key>Frameworks/FirebaseCoreExtension.framework/_CodeSignature/CodeResources</key>
		<data>
		uoELoq9h5Ato2A6bjKSbg30+Zwo=
		</data>
		<key>Frameworks/FirebaseCoreInternal.framework/FirebaseCoreInternal</key>
		<data>
		da2u612rRWnX1Wb1zd+aG0/yg4w=
		</data>
		<key>Frameworks/FirebaseCoreInternal.framework/FirebaseCoreInternal_Privacy.bundle/Info.plist</key>
		<data>
		8ZZOOlSaEuH96dDyDj98JfJfXhY=
		</data>
		<key>Frameworks/FirebaseCoreInternal.framework/FirebaseCoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		ifoThrqbbqoLG4yjAruMQRaf0Dw=
		</data>
		<key>Frameworks/FirebaseCoreInternal.framework/Info.plist</key>
		<data>
		yWEK9+7JsoUCKAbzLzL/zqi/UQY=
		</data>
		<key>Frameworks/FirebaseCoreInternal.framework/_CodeSignature/CodeResources</key>
		<data>
		6BuIhS5P9LmsGA0AGMDRIG1bkLU=
		</data>
		<key>Frameworks/FirebaseFirestore.framework/FirebaseFirestore</key>
		<data>
		Hkkw+bEY/uGqVQAcBa00Dl9mVg4=
		</data>
		<key>Frameworks/FirebaseFirestore.framework/FirebaseFirestore_Privacy.bundle/Info.plist</key>
		<data>
		jbnci7uW5Y4BV56pEdgLb7Yyutc=
		</data>
		<key>Frameworks/FirebaseFirestore.framework/FirebaseFirestore_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		WXQUJr75eMRgiVnLGyf8Gr3uLUU=
		</data>
		<key>Frameworks/FirebaseFirestore.framework/Info.plist</key>
		<data>
		OyT2M26WWHf06QqSV5sPNvVBfQU=
		</data>
		<key>Frameworks/FirebaseFirestore.framework/_CodeSignature/CodeResources</key>
		<data>
		DQ3MnZeukXmwj/963/I4NbMEg0M=
		</data>
		<key>Frameworks/FirebaseFirestoreInternal.framework/FirebaseFirestoreInternal</key>
		<data>
		sSMXNBF10RTBX4BY/BewCTbHYik=
		</data>
		<key>Frameworks/FirebaseFirestoreInternal.framework/FirebaseFirestoreInternal_Privacy.bundle/Info.plist</key>
		<data>
		CZJrX7mFbmH3uNTNW10N6Ej7PNY=
		</data>
		<key>Frameworks/FirebaseFirestoreInternal.framework/FirebaseFirestoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		WXQUJr75eMRgiVnLGyf8Gr3uLUU=
		</data>
		<key>Frameworks/FirebaseFirestoreInternal.framework/Info.plist</key>
		<data>
		pIPA03SoOQKhNleU12X+EuHdTIg=
		</data>
		<key>Frameworks/FirebaseFirestoreInternal.framework/_CodeSignature/CodeResources</key>
		<data>
		MDQHPNJrUIwZwUWpxM/whlBUQCA=
		</data>
		<key>Frameworks/FirebaseSharedSwift.framework/FirebaseSharedSwift</key>
		<data>
		4sxuSni/J8UXx7DR+Qa18dmyNVY=
		</data>
		<key>Frameworks/FirebaseSharedSwift.framework/Info.plist</key>
		<data>
		S4j6omDxD9GRIl0yCaOY3pwbOIQ=
		</data>
		<key>Frameworks/FirebaseSharedSwift.framework/_CodeSignature/CodeResources</key>
		<data>
		63FkqBIexupoc+wvRE6ih8vUF6Y=
		</data>
		<key>Frameworks/Flutter.framework/Flutter</key>
		<data>
		UZLumYtRhr2hqqJS3MlIaqwvcmw=
		</data>
		<key>Frameworks/Flutter.framework/Headers/Flutter.h</key>
		<data>
		wTPJHICwW6wxY3b87ek7ITN5kJk=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterAppDelegate.h</key>
		<data>
		zbvYFr9dywry0lMMrHuNOOaNgkY=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterBinaryMessenger.h</key>
		<data>
		ksjIMu5IPw+Q3rw2YkAx0KjxkdM=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterCallbackCache.h</key>
		<data>
		V/wkSSsyYdMoexF6wPrC3KgkL4g=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterChannels.h</key>
		<data>
		vFsZXNqjflvqKqAzsIptQaTSJho=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterCodecs.h</key>
		<data>
		sUgX1PJzkvyinL5i7nS1ro/Kd5o=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterDartProject.h</key>
		<data>
		SpNs7IhIC7xP34Ej+LQCaEZkqik=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngine.h</key>
		<data>
		AqVvCbPmgWMQKrRnib05Okrjbp0=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngineGroup.h</key>
		<data>
		bkw+DmHReHDg1PPcvmSjuLZrheA=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterHeadlessDartRunner.h</key>
		<data>
		UqnnVWwQEYYX56eu7lt6dpR3LIc=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterHourFormat.h</key>
		<data>
		VjAwScWkWWSrDeetip3K4yhuwDU=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterMacros.h</key>
		<data>
		crQ9782ULebLQfIR+MbBkjB7d+k=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlatformViews.h</key>
		<data>
		ocQVSiAiUMYfVtZIn48LpYTJA5w=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlugin.h</key>
		<data>
		EARXud6pHb7ZYP8eXPDnluMqcXk=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<data>
		qWHw5VIWEa0NmJ1PMhD16nlfRKk=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterTexture.h</key>
		<data>
		31prWLso2k5PfMMSbf5hGl+VE6Y=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterViewController.h</key>
		<data>
		LDr6kSVbUfyQFAxLwCACF5S2VEA=
		</data>
		<key>Frameworks/Flutter.framework/Info.plist</key>
		<data>
		wdCov3MmYWiDiPpgy0pmhBH1sCU=
		</data>
		<key>Frameworks/Flutter.framework/Modules/module.modulemap</key>
		<data>
		wJV5dCKEGl+FAtDc8wJJh/fvKXs=
		</data>
		<key>Frameworks/Flutter.framework/PrivacyInfo.xcprivacy</key>
		<data>
		D+cqXttvC7E/uziGjFdqFabWd7A=
		</data>
		<key>Frameworks/Flutter.framework/_CodeSignature/CodeResources</key>
		<data>
		L4gGRJ+QMbVTbPWMivfe2jUptCM=
		</data>
		<key>Frameworks/Flutter.framework/icudtl.dat</key>
		<data>
		ipm8hg7aB3LzsfShJfpNR0QQ4hw=
		</data>
		<key>Frameworks/GoogleUtilities.framework/GoogleUtilities</key>
		<data>
		KmaXdH00U5Uj1D6TcDUSchcL3tc=
		</data>
		<key>Frameworks/GoogleUtilities.framework/GoogleUtilities_Privacy.bundle/Info.plist</key>
		<data>
		Cc6RNxHdKBeEld2ouXK3WTWDCBc=
		</data>
		<key>Frameworks/GoogleUtilities.framework/GoogleUtilities_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		9Dge7JFNlx7Vk430tsjNsK3d0Ng=
		</data>
		<key>Frameworks/GoogleUtilities.framework/Info.plist</key>
		<data>
		0tvgABpzVxU6QMx7EHT+oomNMsk=
		</data>
		<key>Frameworks/GoogleUtilities.framework/_CodeSignature/CodeResources</key>
		<data>
		1Vqzd5lYDpulaw2KCIjG2fO+6QQ=
		</data>
		<key>Frameworks/Try.framework/Info.plist</key>
		<data>
		xWanp67sxgQf+Qcx4cQLk1agg2g=
		</data>
		<key>Frameworks/Try.framework/Try</key>
		<data>
		XZ75b8n98W+ytIfz/cnF1MaacUg=
		</data>
		<key>Frameworks/Try.framework/_CodeSignature/CodeResources</key>
		<data>
		wNEVyJgm/YP9lEzqXk2sX2Wlc3M=
		</data>
		<key>Frameworks/absl.framework/Info.plist</key>
		<data>
		0B6AHOmYPM5M6M2/vz0M1EjHMXU=
		</data>
		<key>Frameworks/absl.framework/_CodeSignature/CodeResources</key>
		<data>
		Ue1weXU4LPBQ4vB/ALl2BK+9Hjo=
		</data>
		<key>Frameworks/absl.framework/absl</key>
		<data>
		HoD42g3w0tfwFrOaJWeWUBkzC1o=
		</data>
		<key>Frameworks/absl.framework/xcprivacy.bundle/Info.plist</key>
		<data>
		7r1hgAtSNNrJDEk0dYupqZDaRxE=
		</data>
		<key>Frameworks/absl.framework/xcprivacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		1GKSpq20OoFIxAuJBtHAw+vMZO8=
		</data>
		<key>Frameworks/camera_avfoundation.framework/Info.plist</key>
		<data>
		qVZ8C8cWR7ZX8S+PIEnxiQ7a4uc=
		</data>
		<key>Frameworks/camera_avfoundation.framework/_CodeSignature/CodeResources</key>
		<data>
		H3A/Ysa8kwh854teC9m9dESt7Z8=
		</data>
		<key>Frameworks/camera_avfoundation.framework/camera_avfoundation</key>
		<data>
		ctOJTW6037Xsbt3oPe1SwybbBUE=
		</data>
		<key>Frameworks/camera_avfoundation.framework/camera_avfoundation_privacy.bundle/Info.plist</key>
		<data>
		xjSwShlpYapjFxIHClEHwAaRLbQ=
		</data>
		<key>Frameworks/camera_avfoundation.framework/camera_avfoundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>Frameworks/grpc.framework/Info.plist</key>
		<data>
		ZgvFcOumm+3ON7Va/QSZrLTdR9I=
		</data>
		<key>Frameworks/grpc.framework/_CodeSignature/CodeResources</key>
		<data>
		d10owa15VLRlRZhTyMCT/R3xPM0=
		</data>
		<key>Frameworks/grpc.framework/grpc</key>
		<data>
		Zg2XXj9Kih5wbl19e+IIbTU3L9U=
		</data>
		<key>Frameworks/grpc.framework/grpc.bundle/Info.plist</key>
		<data>
		g5HI9h/KKKyMwdjEE7rlfx3W9oU=
		</data>
		<key>Frameworks/grpc.framework/grpc.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		PFHYbs0V3eUFDWQyYQcwEetuqEk=
		</data>
		<key>Frameworks/grpcpp.framework/Info.plist</key>
		<data>
		6vnO0kmW1QJPA0lZQgKrzP+D5Iw=
		</data>
		<key>Frameworks/grpcpp.framework/_CodeSignature/CodeResources</key>
		<data>
		o/4mDz7HHN8+827ipQOc2dxE1iA=
		</data>
		<key>Frameworks/grpcpp.framework/gRPCCertificates-Cpp.bundle/Info.plist</key>
		<data>
		2O4QaSFL5nIbLg4Q2PijCpIEwcM=
		</data>
		<key>Frameworks/grpcpp.framework/gRPCCertificates-Cpp.bundle/roots.pem</key>
		<data>
		fKPpDnXJArmBT4pB8zmpeWVIzCc=
		</data>
		<key>Frameworks/grpcpp.framework/grpcpp</key>
		<data>
		CSzNy8BJZLP6Kl8ssgsoAQW24Lk=
		</data>
		<key>Frameworks/grpcpp.framework/grpcpp.bundle/Info.plist</key>
		<data>
		/6YVz3claF1B/ahxAsVueCV3Peg=
		</data>
		<key>Frameworks/grpcpp.framework/grpcpp.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		PFHYbs0V3eUFDWQyYQcwEetuqEk=
		</data>
		<key>Frameworks/image_picker_ios.framework/Info.plist</key>
		<data>
		DzNRZ8c6/6iP7o/nzFHuUyUosyY=
		</data>
		<key>Frameworks/image_picker_ios.framework/_CodeSignature/CodeResources</key>
		<data>
		ZASlGdni8hh6gBMgdSeEluCKmfc=
		</data>
		<key>Frameworks/image_picker_ios.framework/image_picker_ios</key>
		<data>
		WCya6w3Zb4fnvuApYshi96Hmy/I=
		</data>
		<key>Frameworks/image_picker_ios.framework/image_picker_ios_privacy.bundle/Info.plist</key>
		<data>
		OHphdEci9GJwCI+JJ4cIOT+vFF0=
		</data>
		<key>Frameworks/image_picker_ios.framework/image_picker_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>Frameworks/leveldb.framework/Info.plist</key>
		<data>
		3O4lKCTs8EL0uncpVIEQUaUUCOY=
		</data>
		<key>Frameworks/leveldb.framework/_CodeSignature/CodeResources</key>
		<data>
		vLc8FYoFI8CCCIYRGLJ/8Tcj6kw=
		</data>
		<key>Frameworks/leveldb.framework/leveldb</key>
		<data>
		vRbGSMXiyC5YYkHk05TIDYr+Xgw=
		</data>
		<key>Frameworks/leveldb.framework/leveldb_Privacy.bundle/Info.plist</key>
		<data>
		IR9wDr8xqBqROuGgE7qbP6DCm8o=
		</data>
		<key>Frameworks/leveldb.framework/leveldb_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		JTGYQTSkuJ7uGfD1SfjQTq3ngyk=
		</data>
		<key>Frameworks/nanopb.framework/Info.plist</key>
		<data>
		5HeRAxazdTvPmVd+pAvvofFAnKo=
		</data>
		<key>Frameworks/nanopb.framework/_CodeSignature/CodeResources</key>
		<data>
		8OVPS8gX3qRRILWrSqjS9pNd55E=
		</data>
		<key>Frameworks/nanopb.framework/nanopb</key>
		<data>
		ziA516xYka3/5drv6u3BoOcc1Hw=
		</data>
		<key>Frameworks/nanopb.framework/nanopb_Privacy.bundle/Info.plist</key>
		<data>
		7SWvVTbC7+pahO/DmKFcoenJSrw=
		</data>
		<key>Frameworks/nanopb.framework/nanopb_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		KY5lfwC2TvsgFj4wt7hkMmainbs=
		</data>
		<key>Frameworks/openssl_grpc.framework/Info.plist</key>
		<data>
		poSgeHyHvq6IxPF7hyTm4XvXUhw=
		</data>
		<key>Frameworks/openssl_grpc.framework/_CodeSignature/CodeResources</key>
		<data>
		G4dB40MW7AG9w/SYhWdcpGBHzKA=
		</data>
		<key>Frameworks/openssl_grpc.framework/openssl_grpc</key>
		<data>
		EEesuE4Dl10BDvOu9G7AkEc0Zis=
		</data>
		<key>Frameworks/openssl_grpc.framework/openssl_grpc.bundle/Info.plist</key>
		<data>
		3mhkgTNow0No3PyexBpncvyD7uk=
		</data>
		<key>Frameworks/openssl_grpc.framework/openssl_grpc.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		PFHYbs0V3eUFDWQyYQcwEetuqEk=
		</data>
		<key>Frameworks/path_provider_foundation.framework/Info.plist</key>
		<data>
		95GT2dpgJaS82BDv9XeMqSJCB4Y=
		</data>
		<key>Frameworks/path_provider_foundation.framework/_CodeSignature/CodeResources</key>
		<data>
		bCQeBUar2fu52D8Bv+S5mkJZs/g=
		</data>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation</key>
		<data>
		hLv0PSnGafF9/schBEFH7zoxEvc=
		</data>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/Info.plist</key>
		<data>
		t8Glxk0LGE8nBZo3SN+z8IilF6I=
		</data>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/Info.plist</key>
		<data>
		kkxdHftIpTZ3XbG0n5tYlnqK4fY=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/_CodeSignature/CodeResources</key>
		<data>
		d6xMCJxdIomO8dWllFX9r8ZbYUI=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation</key>
		<data>
		a5vr/RTwbo2DA6m9dVNE8l7l7HE=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/Info.plist</key>
		<data>
		wEBryONcR9fGWToCQQ5GVO/WQjQ=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		6uLTlq7fgHHWA8emYDf4ImHC+AY=
		</data>
		<key>Frameworks/speech_to_text.framework/Info.plist</key>
		<data>
		QrlG1n7MdX0coTR816++cHB9XW4=
		</data>
		<key>Frameworks/speech_to_text.framework/_CodeSignature/CodeResources</key>
		<data>
		Olxl1fSSLhQZINhIOUpv1KJWbs0=
		</data>
		<key>Frameworks/speech_to_text.framework/speech_to_text</key>
		<data>
		vllZmxrVlHvS6kLCXHBGazxb69Q=
		</data>
		<key>Frameworks/sqflite_darwin.framework/Info.plist</key>
		<data>
		0x/HiZwGrA+bFgzE1nRHUYewiKE=
		</data>
		<key>Frameworks/sqflite_darwin.framework/_CodeSignature/CodeResources</key>
		<data>
		sTHw3+jqjRM8QHH1izapAvLOkME=
		</data>
		<key>Frameworks/sqflite_darwin.framework/sqflite_darwin</key>
		<data>
		nJtP07Szfwi9gbsMzmpDb0PY5U8=
		</data>
		<key>Frameworks/sqflite_darwin.framework/sqflite_darwin_privacy.bundle/Info.plist</key>
		<data>
		5GRQw1qzWOUepkIOC4L4OGowNTY=
		</data>
		<key>Frameworks/sqflite_darwin.framework/sqflite_darwin_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		YIiJ5tHvqBeSpBm2mcfVZdaGz3E=
		</data>
		<key>Info.plist</key>
		<data>
		jOBysfaR1kXpxfFNUsqZqE3/zxM=
		</data>
		<key>PkgInfo</key>
		<data>
		n57qDP4tZfLD1rCS43W0B4LQjzE=
		</data>
		<key>Runner.debug.dylib</key>
		<data>
		8gIc5rXtDPo0824JHH8L9teqBDY=
		</data>
		<key>__preview.dylib</key>
		<data>
		1ANE2k9ZXA5Ga777JBdk/rUchDI=
		</data>
		<key>permission_handler_apple_privacy.bundle/Info.plist</key>
		<data>
		SPm6yq98GuoPz2D7OqVu+IuLh2c=
		</data>
		<key>permission_handler_apple_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		PgAJpgZlblxKbgx9eihlgflAQU8=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>AppFrameworkInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Zb9VR5aeuJMnm/RgXM3cr4LUNi9UZgxKD7xAgkid0NI=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			Gb4XFIHccaCygD680B3YsMX9V3je40wKPKvJSMIl8k4=
			</data>
		</dict>
		<key>AppIcon76x76@2x~ipad.png</key>
		<dict>
			<key>hash2</key>
			<data>
			QcfUL25h+P5/MLH/oiVq7LyWgr4G0YxKMGIEPhouVHw=
			</data>
		</dict>
		<key>Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			vaSEYL+aQujKqftqrf8iAL4OShq+Pr+U3T9Oyv+iOSM=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			by6WshwXWgbEYiAy2bvh0UtjSVa3EwySkNFc1FazGdY=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			HyVdXMU7Ux4/KalAao30mpWOK/lEPT4gvYN09wf31cg=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			VPNjf2cf66XxnoLsT0p/tEi7PPwPsYDwiapXH8jwU+I=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/BYZ-38-t0r-view-8bC-Xf-vdC.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			BY/hOMO0FcCl8mCMQqjVbFeb8Q97c1G9lHscfspHFNk=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			PpvapAjR62rl6Ym4E6hkTgpKmBICxTaQXeUqcpHmmqQ=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/UIViewController-BYZ-38-t0r.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			y90o2JQjssm+7ysnziyWCNMNbGqdLnZ595pTgURE5T8=
			</data>
		</dict>
		<key>Frameworks/App.framework/App</key>
		<dict>
			<key>hash2</key>
			<data>
			9HisVm7a/8pBO1fy0YqURpRxvfrcbu+SkmPs8K6SmKI=
			</data>
		</dict>
		<key>Frameworks/App.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			SES+IrctFtb2ATzloiQHKMcw7x/vnPQ6XFUZixhhSAI=
			</data>
		</dict>
		<key>Frameworks/App.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			oMO6WvOBaAXtckho0CDlNoVz7p+8IjjrDnGa7oamOSo=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			AK9VrT1vIYmP534P8JLRoc2lLJQbaGDpko1FyK+MCV0=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Sps95+7JukayebvM0TLjL1LW1VXXndpKp/O8tOm9ZR8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/FontManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zX4DZFvESy3Ue3y2JvUcTsv1Whl6t3JBYotHrBZfviE=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/NOTICES.Z</key>
		<dict>
			<key>hash2</key>
			<data>
			bHq0RZaS/IHgQRxwdaKIIeniHvWU2Y6HevaFLWjDIqw=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/NativeAssetsManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			lUijHkoEgTXB2U+Rkyi/tirix7s8q5ZVfHlB2ql3dss=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			2YZbZxoJ1oPROoYwidiCXg9ho3aWzl19RIvIAjqmJFM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/isolate_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			Y6T4MBhQBv9hSvbFzkj/7l2qb75MlOBIl0B6jKveoKE=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/kernel_blob.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			NGRaEPfUq3QfrmDKum1/Mc+er6V/tK5/i8JhMce3MEg=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			Z8RP6Rg7AC553ef2l34piGYcmj5KPF/OloeH79vtgjw=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/shaders/ink_sparkle.frag</key>
		<dict>
			<key>hash2</key>
			<data>
			TGVjYgE+Oyl6guvhhPPrWfynkxkJeFjSzSLsQqn7Q3M=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/vm_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			RTqty/gYjcrXxgR3QLjNukvL8gGpfV6MdaYktQJaIa8=
			</data>
		</dict>
		<key>Frameworks/FirebaseAppCheckInterop.framework/FirebaseAppCheckInterop</key>
		<dict>
			<key>hash2</key>
			<data>
			L3nnkzmU84cLQNsmx4MTUwtu5Fprz8t91W5vEAYCDS4=
			</data>
		</dict>
		<key>Frameworks/FirebaseAppCheckInterop.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			py2EMRB7rajgmMiSERYAG6KgUgTpz8OrrYIOHUW8aUw=
			</data>
		</dict>
		<key>Frameworks/FirebaseAppCheckInterop.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			4JC6J4GTjiKrkn9wYK2yifzL3JQXZ+upvRoHO7KPLMg=
			</data>
		</dict>
		<key>Frameworks/FirebaseCore.framework/FirebaseCore</key>
		<dict>
			<key>hash2</key>
			<data>
			EwnvhqX86JMOoceGiYGK805TS3eC8Bblisx5OwgZnSk=
			</data>
		</dict>
		<key>Frameworks/FirebaseCore.framework/FirebaseCore_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			PJ+iR89eHIMN6DSroQYv1nrLIiKrZzmCQ7ua/MKAfKE=
			</data>
		</dict>
		<key>Frameworks/FirebaseCore.framework/FirebaseCore_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			EeMfX2tg6A69WQFUn85QQ/mvPmg/h0AilFAAtAUwbD8=
			</data>
		</dict>
		<key>Frameworks/FirebaseCore.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			0SLEgv0qR6Fp/N6fbjDo1jVyAOegmbzW0CFFo2IIJ7o=
			</data>
		</dict>
		<key>Frameworks/FirebaseCore.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			VAEkg7ganeBf23+9JiA0uO1Dkn3AQpuB1KvXy3tRRE4=
			</data>
		</dict>
		<key>Frameworks/FirebaseCoreExtension.framework/FirebaseCoreExtension</key>
		<dict>
			<key>hash2</key>
			<data>
			0LCRWyZk/vS9iYw5a5afuKyHv39RxELTHdMEdy13Tmw=
			</data>
		</dict>
		<key>Frameworks/FirebaseCoreExtension.framework/FirebaseCoreExtension_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			XZDtaHcQSIR45vgFT4XbBhE1WP6cptoZ8/sqo4reQ/I=
			</data>
		</dict>
		<key>Frameworks/FirebaseCoreExtension.framework/FirebaseCoreExtension_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			E3fszt/4+DIPeAQOnJ0+F+zA6laVlLVq8O8XvkQZfHo=
			</data>
		</dict>
		<key>Frameworks/FirebaseCoreExtension.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			sgEweDBMHstrxj5i4Qcrl+yEjFZgvt++ViPkGQGjgL8=
			</data>
		</dict>
		<key>Frameworks/FirebaseCoreExtension.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			zZ2WxGczEB8qb8e1ywwbikweRn2KYUCQV4VE80soVHE=
			</data>
		</dict>
		<key>Frameworks/FirebaseCoreInternal.framework/FirebaseCoreInternal</key>
		<dict>
			<key>hash2</key>
			<data>
			01+tmb7uYgWKFoq/d0owTlia14Y/GjuiTkUikAIgxIo=
			</data>
		</dict>
		<key>Frameworks/FirebaseCoreInternal.framework/FirebaseCoreInternal_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			zV7lp0qB9JjJomOs3ZFCZoEv0ontjZuoqxQ24f//8Dg=
			</data>
		</dict>
		<key>Frameworks/FirebaseCoreInternal.framework/FirebaseCoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			W3/peUI97ePgivwppC8A9ghiddxUioTCl3QjWGPu0+8=
			</data>
		</dict>
		<key>Frameworks/FirebaseCoreInternal.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			KczLY/rNB1Thf160giiyUlIa7LrsH4ONrfSaRGIZGxI=
			</data>
		</dict>
		<key>Frameworks/FirebaseCoreInternal.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			ovtd97lzMC8zY/DOeY5BnP4yBEvuKHPe9efDbQeePuA=
			</data>
		</dict>
		<key>Frameworks/FirebaseFirestore.framework/FirebaseFirestore</key>
		<dict>
			<key>hash2</key>
			<data>
			UaRXP1Mg/vkLr2xYfcwEQJiZSOXMKV/lcv9wDYaaSoY=
			</data>
		</dict>
		<key>Frameworks/FirebaseFirestore.framework/FirebaseFirestore_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			7L+94wQgaS54enZ4daRorC1Lv9s4bc3IKkCJw7k9alE=
			</data>
		</dict>
		<key>Frameworks/FirebaseFirestore.framework/FirebaseFirestore_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			z7s8T3ambVNpi66R9xEMAPIUjm5vE619MlkpCbwBDlE=
			</data>
		</dict>
		<key>Frameworks/FirebaseFirestore.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			3ATpomoNC9uoFALdYtB8WzDHBZi4l4zdSeCl8WOhRfI=
			</data>
		</dict>
		<key>Frameworks/FirebaseFirestore.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			dIjuaYZFlj0fKAFwygfSiH9NE+Cdb92R0Wmv0JRS5WE=
			</data>
		</dict>
		<key>Frameworks/FirebaseFirestoreInternal.framework/FirebaseFirestoreInternal</key>
		<dict>
			<key>hash2</key>
			<data>
			q8BWU/ka40PgWAYhk3H5klZVG0rii8aaaFSKNibM5Po=
			</data>
		</dict>
		<key>Frameworks/FirebaseFirestoreInternal.framework/FirebaseFirestoreInternal_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			T3ferXuCL5MENrQnB4lvOxEmkoHeWFk+01sYWfPDNdQ=
			</data>
		</dict>
		<key>Frameworks/FirebaseFirestoreInternal.framework/FirebaseFirestoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			z7s8T3ambVNpi66R9xEMAPIUjm5vE619MlkpCbwBDlE=
			</data>
		</dict>
		<key>Frameworks/FirebaseFirestoreInternal.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			oSQAkDAZP8byJh0H29I3ckLyIsnYzmpDx3MIMu6dL8g=
			</data>
		</dict>
		<key>Frameworks/FirebaseFirestoreInternal.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			cz3kZ0J7NMBZcTuZGkjVPzIvjHWBvQ38rxfv/f9F22c=
			</data>
		</dict>
		<key>Frameworks/FirebaseSharedSwift.framework/FirebaseSharedSwift</key>
		<dict>
			<key>hash2</key>
			<data>
			B9L5Ik/TJsAgKjff8afekUhsLPPg4Kzr6y+JJq7rtbU=
			</data>
		</dict>
		<key>Frameworks/FirebaseSharedSwift.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			3r7acosXJsR12uxhmCIXJSkp4mqs6cTixgZlIjpKrYU=
			</data>
		</dict>
		<key>Frameworks/FirebaseSharedSwift.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			glzxLAyTxuSUPGEuLk52ATYTBUgvvOk/GesZD8qti6w=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Flutter</key>
		<dict>
			<key>hash2</key>
			<data>
			mWLaEPaPd42r40U8mhyH4sai30Tpbv5xa8eIAZKWvM4=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/Flutter.h</key>
		<dict>
			<key>hash2</key>
			<data>
			auaf7wPxiASCYD2ACy1dfbMJvmONwFvSz1BWYAQrrSw=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterAppDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			o0iigVsmgwmtZfSv3X7hReDNYP5rXblslDnqq2s6UQc=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterBinaryMessenger.h</key>
		<dict>
			<key>hash2</key>
			<data>
			EXDk4t+7qCpyQkar+q9WHqY9bcK8eyohCwGVtBJhMy8=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterCallbackCache.h</key>
		<dict>
			<key>hash2</key>
			<data>
			0h9+vK5K+r8moTsiGBfs6+TM9Qog089afHAy3gbcwDU=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterChannels.h</key>
		<dict>
			<key>hash2</key>
			<data>
			kg195C3vZLiOn8KeFQUy7DoVuA9VZDpqoBLVn64uGaI=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterCodecs.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ZyqlHYuZbpFevVeny9Wdl0rVFgS7szIyssSiCyaaeFM=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterDartProject.h</key>
		<dict>
			<key>hash2</key>
			<data>
			U8q/0Ibt9q4O2HMsCdUwITtJdTx8Ljhlx+0aY83fH6s=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngine.h</key>
		<dict>
			<key>hash2</key>
			<data>
			RAOC6nDhZdghbAzsIZgVeq6qPt+MUNTfm/vkUnhmZO4=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngineGroup.h</key>
		<dict>
			<key>hash2</key>
			<data>
			SqzvIxqBXEJ3U9LJ32hCEXsrH2P16gumQ+gQx6Pdlf4=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterHeadlessDartRunner.h</key>
		<dict>
			<key>hash2</key>
			<data>
			nmZjZpvFCXrygf4U9aPkNi8VcI7cL5AtA+CY5uUWIL0=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterHourFormat.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Q4SLFSghL/5EFJPyLg7PNi9J/xpkVVfzro0VQiQHtrY=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterMacros.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ebBVHSZcUnAbN4hRcYq3ttt6++z1Ybc8KVSYhVToD5k=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlatformViews.h</key>
		<dict>
			<key>hash2</key>
			<data>
			4hl+kRU4PNNKdAHvYrliObXzSjRzow9Z18oOMRZIa0o=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlugin.h</key>
		<dict>
			<key>hash2</key>
			<data>
			HqbvCHqKWTzs5GjLAwupqEIYVi9yf5CrMdMe31EOwUA=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			+PMn+5SDj2Vd6RU8CQIt/JYl3T+8Dhp7HImqAzocoNk=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterTexture.h</key>
		<dict>
			<key>hash2</key>
			<data>
			JcpN4a9sv6xynlD3Ri611N5y+HoupUWp2hyrIXB/I8Y=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterViewController.h</key>
		<dict>
			<key>hash2</key>
			<data>
			yEgZTlCNrK/A/QBjEwNGB6ffC+A9gorPvnNgSbYuQ7Y=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Bj4k7mfeh7+t8VzFbCt3JOJSUlmyjmMd1klhQK0rJOs=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			0VjriRpZ7AZZaP/0mMAPMJPhi6LoMB4MhXzL5j24tGs=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			n5XX54YqS1a2btkmvW1iLSplRagn0ZhHJ4tDjVcdQhI=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			MbF8RLSNhgeqvayPs+eMjCvNpixlUdWyIyyNFvTHcqA=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/icudtl.dat</key>
		<dict>
			<key>hash2</key>
			<data>
			wSU3Ai74GJkae/7UGnbY1q6WL/vA5lEax2Kl0IRef3w=
			</data>
		</dict>
		<key>Frameworks/GoogleUtilities.framework/GoogleUtilities</key>
		<dict>
			<key>hash2</key>
			<data>
			beAjQGu0Cc2SRo8scNhNMMWcNk8mBy6Vne/4PfkSmB8=
			</data>
		</dict>
		<key>Frameworks/GoogleUtilities.framework/GoogleUtilities_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			OToXtH5jPA+JOZFblD0sEm11NiihTCuievLJG9awW+I=
			</data>
		</dict>
		<key>Frameworks/GoogleUtilities.framework/GoogleUtilities_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			+Btc+PBDZicS7KnpeFdnJkzxkAJf5720l3cpbAaN5Tw=
			</data>
		</dict>
		<key>Frameworks/GoogleUtilities.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			wLvUSiEShwMbMoXwGFH54du6dThykEsOlPhucGdNQHU=
			</data>
		</dict>
		<key>Frameworks/GoogleUtilities.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			ij0d3OWMjddAJOGONcOdBo6wTGvd53XQ6jz6XP81sqY=
			</data>
		</dict>
		<key>Frameworks/Try.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			8frPIUDmRM2vYBx6ytMvJw+g9jwJd0t1XrBuEn1823s=
			</data>
		</dict>
		<key>Frameworks/Try.framework/Try</key>
		<dict>
			<key>hash2</key>
			<data>
			On1wYKmlCL9sQ8hzAPJr51Lf8mOQiD//43pL7en7mvs=
			</data>
		</dict>
		<key>Frameworks/Try.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			0ZYdpY4EUlsCeRqfY4vEfZ8JZBsJ93YJIQ8R11M8FqM=
			</data>
		</dict>
		<key>Frameworks/absl.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			COJpzOaU35z+A31KewFcZGxoCNWJBL41Ml4Iz+QnWFg=
			</data>
		</dict>
		<key>Frameworks/absl.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			CTPMqFg29owSML9AEmjaJD0HM4Mo9TuR2oJgBOBgbRQ=
			</data>
		</dict>
		<key>Frameworks/absl.framework/absl</key>
		<dict>
			<key>hash2</key>
			<data>
			H5fPY/wD6+zoB5Y4S8quw4Ip0sqVRQtRF6QOuytdwyY=
			</data>
		</dict>
		<key>Frameworks/absl.framework/xcprivacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			9OFIREmml81Bt91W+zAtPxcuknfmgkYpHC9XvTB8v/E=
			</data>
		</dict>
		<key>Frameworks/absl.framework/xcprivacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			8jIheunt8qtqVBoo7/UM/gUwPCtHVv/5XMpy7MvDuJg=
			</data>
		</dict>
		<key>Frameworks/camera_avfoundation.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			TCaHgwR+w7crwFgx6+vQAwi/q5xIwxQu/CTXFLEeDag=
			</data>
		</dict>
		<key>Frameworks/camera_avfoundation.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			ugprzWYn7pxB8wrLrwABJtTHNSfxG5enEhDXApiDAG0=
			</data>
		</dict>
		<key>Frameworks/camera_avfoundation.framework/camera_avfoundation</key>
		<dict>
			<key>hash2</key>
			<data>
			XyoBWXEtJIMmBjt2kcNock8EsmvRIt0e2+UlZaLXxEs=
			</data>
		</dict>
		<key>Frameworks/camera_avfoundation.framework/camera_avfoundation_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			UeJe3RZkcQqj21n3vsQGP+azXzevwyZK8Uyl67of1NQ=
			</data>
		</dict>
		<key>Frameworks/camera_avfoundation.framework/camera_avfoundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>Frameworks/grpc.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			7VblzljHO3uLhj8/XG2IO0dHXOgGwO/I76BhyE3/U/g=
			</data>
		</dict>
		<key>Frameworks/grpc.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			BzRsC5FnP0G9+c6AbJ0eZ1dYGJanriMH9wfyT7CLaxo=
			</data>
		</dict>
		<key>Frameworks/grpc.framework/grpc</key>
		<dict>
			<key>hash2</key>
			<data>
			fVgLPNN04cywon5/PgWolZkWEY6HrwzNX2t9xtMtJlY=
			</data>
		</dict>
		<key>Frameworks/grpc.framework/grpc.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			bxR/lUabZVua3x5IRbaU7xLbT5OjTFvKV+4NlacH5CE=
			</data>
		</dict>
		<key>Frameworks/grpc.framework/grpc.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			A7LHCDOjMaKx79Ef8WjtAqjq39Xn0fvzDuzHUJpK6kc=
			</data>
		</dict>
		<key>Frameworks/grpcpp.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			7MJAbrCjuQ0NVMv+qKS/lZC242r7q1MgMgSjT1zydqY=
			</data>
		</dict>
		<key>Frameworks/grpcpp.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			E1f9A7ec02FcVKXIUPpJ7yZh3IbkuxU9BhET063YyZs=
			</data>
		</dict>
		<key>Frameworks/grpcpp.framework/gRPCCertificates-Cpp.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			a7spu16G2TG+AbZXmqC+g7VdBZhTz6Y7laGt2oL/SuE=
			</data>
		</dict>
		<key>Frameworks/grpcpp.framework/gRPCCertificates-Cpp.bundle/roots.pem</key>
		<dict>
			<key>hash2</key>
			<data>
			lhQzRMSuEJWIElssQdXa9lSl+vxuI/rDf3uj0p2n53Y=
			</data>
		</dict>
		<key>Frameworks/grpcpp.framework/grpcpp</key>
		<dict>
			<key>hash2</key>
			<data>
			WtByeDEBx8rUO9MwwrRnqlqQpigRkiCla3J3WxPfNFU=
			</data>
		</dict>
		<key>Frameworks/grpcpp.framework/grpcpp.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			vwFCfjlXmHX7t+Tkrhcz2YEIn1fRgYqHMPoEvcbcbQU=
			</data>
		</dict>
		<key>Frameworks/grpcpp.framework/grpcpp.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			A7LHCDOjMaKx79Ef8WjtAqjq39Xn0fvzDuzHUJpK6kc=
			</data>
		</dict>
		<key>Frameworks/image_picker_ios.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			0D2se6H39JQau+oLsmaYR0YZdMiXMZ65eStWvFD/nDE=
			</data>
		</dict>
		<key>Frameworks/image_picker_ios.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			tzW9yLMtKobcFDV2UDrxEDPXJUQD8G1WWJCZV7JHuQI=
			</data>
		</dict>
		<key>Frameworks/image_picker_ios.framework/image_picker_ios</key>
		<dict>
			<key>hash2</key>
			<data>
			sX2sDeEFjgYHE5FgS+zX+l0aTRRtMGOgPdM+CoGgsDM=
			</data>
		</dict>
		<key>Frameworks/image_picker_ios.framework/image_picker_ios_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			erBgt5ldrfnZu+XhKYt1Hd/ncvszgY2iAHZ49kjHvKY=
			</data>
		</dict>
		<key>Frameworks/image_picker_ios.framework/image_picker_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>Frameworks/leveldb.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			F230VNgWFEnk+vbU9LDp6q/47Nll2dLMnQsuWXYkQlE=
			</data>
		</dict>
		<key>Frameworks/leveldb.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			T5hbIhgpAudiVnGoZD4uEEmgm0W0UjnzHECRal1SQPw=
			</data>
		</dict>
		<key>Frameworks/leveldb.framework/leveldb</key>
		<dict>
			<key>hash2</key>
			<data>
			oH7TIHV9GIea+ZsVWv+9E8viPZWkGoE4ZeFtgS9vxaM=
			</data>
		</dict>
		<key>Frameworks/leveldb.framework/leveldb_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			yfl4EEBnJKo9jHvNo0aeQNCwVmYY+DXDa6HedOQeoKo=
			</data>
		</dict>
		<key>Frameworks/leveldb.framework/leveldb_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			CsAKC37/AHnc4lLimrQdcc9YxslP5OROoRDO9cYpcOE=
			</data>
		</dict>
		<key>Frameworks/nanopb.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			3EjRXNHEIhF2e9c+t9tOEpSdsVCXmAdxZ+WG8oUXf/g=
			</data>
		</dict>
		<key>Frameworks/nanopb.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			Of8dDOB88LrG912WO9xt4AGR/AM+0TR0Ja8eYH4mZiQ=
			</data>
		</dict>
		<key>Frameworks/nanopb.framework/nanopb</key>
		<dict>
			<key>hash2</key>
			<data>
			ee+nqzUSNu9TcxEJH+R0RB+aWXgGiyMGoRNo+TJS7B8=
			</data>
		</dict>
		<key>Frameworks/nanopb.framework/nanopb_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			EfziXQW4TzaDNOPdkK/N6E/ytnnxgeidm80LIEU/ibY=
			</data>
		</dict>
		<key>Frameworks/nanopb.framework/nanopb_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			cpujy9D0WMeM1h7fFzUO2v4ONMqG4xTsZMjLIszSG1Q=
			</data>
		</dict>
		<key>Frameworks/openssl_grpc.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			SYTvPfPDPWS+0LIazdcvsA8aibXt0pqckD9KTrCu7qs=
			</data>
		</dict>
		<key>Frameworks/openssl_grpc.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			MSS/CDCbhfYsd5r4hhZS2YhXAMreqxmw/N9L5kzRYXs=
			</data>
		</dict>
		<key>Frameworks/openssl_grpc.framework/openssl_grpc</key>
		<dict>
			<key>hash2</key>
			<data>
			Amp7+kE6cCEPoyyhdZwAbJPqI8A3slu8Hs4GCykZ3S8=
			</data>
		</dict>
		<key>Frameworks/openssl_grpc.framework/openssl_grpc.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			cwEqSRbVwdaMI+UEn8Q1labmtI0sLX200scUmBOBUQ0=
			</data>
		</dict>
		<key>Frameworks/openssl_grpc.framework/openssl_grpc.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			A7LHCDOjMaKx79Ef8WjtAqjq39Xn0fvzDuzHUJpK6kc=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			HxWDMbzcMTCVM+iKPI2HIqqN+TH4gP63X25kAseFHns=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			vefcOGqgeJ3edxmDt1FROSKz20WHZn1K1Tm704kOiG8=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation</key>
		<dict>
			<key>hash2</key>
			<data>
			c8A/3dJW1sxdUTXXZ/voMy/gaWbmyCWt5C76ffpmUII=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			KoZLRmOgUecD5LRxZi0DPDkMsu2NYZIOIJHx2mE4i50=
			</data>
		</dict>
		<key>Frameworks/path_provider_foundation.framework/path_provider_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			eMKB9fsyiNvcDeQhLA/fqvfXoFSpAR/amhs9ysgLFu4=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			g8jj6MeGQZQfnLXbz2/3JgsmUDJYCMy+KOnFJBL7/Gc=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation</key>
		<dict>
			<key>hash2</key>
			<data>
			1Cei+lUI2a6WhkgMNKxPWbhGkqT9FcNSPPnOugC9DmE=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			LAkfNPSBKgw/2nCOt7PMgYAHJf6rn+GpSz0RF54Ihfg=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			BWQouTi9VwGKYAGdFcPB7i+nJ/I2h1mLu9k0hIsYCxo=
			</data>
		</dict>
		<key>Frameworks/speech_to_text.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			ra3dmKiwcEZp7zUCza3p2PSo07KQ+PtsoZKd3ZErcLs=
			</data>
		</dict>
		<key>Frameworks/speech_to_text.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			BEU7uM/xlBhdUnUtesNYVOjjt/xJt986Yx599U3QIR8=
			</data>
		</dict>
		<key>Frameworks/speech_to_text.framework/speech_to_text</key>
		<dict>
			<key>hash2</key>
			<data>
			RTERriGCX3KWGwM/b2qBNqN6AE8KdG323Ae5TiYWtNw=
			</data>
		</dict>
		<key>Frameworks/sqflite_darwin.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			JsV5x42yixsJOJtoAaayCQI6y5O4s7l5fLiMn7sNt2g=
			</data>
		</dict>
		<key>Frameworks/sqflite_darwin.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			rgS+FIZJBUkoIG8iLmaSI8bXp4D8ie17xFlgbKzlvfw=
			</data>
		</dict>
		<key>Frameworks/sqflite_darwin.framework/sqflite_darwin</key>
		<dict>
			<key>hash2</key>
			<data>
			rtLjix8I5O6UPcazMucPOYgth3jzqTvw8wvYAVO6SPQ=
			</data>
		</dict>
		<key>Frameworks/sqflite_darwin.framework/sqflite_darwin_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			bGyLeZRoLK+XXCRkdMKl3jMkHoklgf7BXesEiVJJAf4=
			</data>
		</dict>
		<key>Frameworks/sqflite_darwin.framework/sqflite_darwin_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			RyJqKWCN8gatChEOav61p3/1dawd+cdr/bLW37P6/tE=
			</data>
		</dict>
		<key>Runner.debug.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			/RZwEWhTuPYnqdzticjW+YJW0SNQ/Qq+jbbv+zdiAps=
			</data>
		</dict>
		<key>__preview.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			tyfxWVNA7zRMx13PHEXnYoa6ZFGCfJ3ZkS0QRacGEas=
			</data>
		</dict>
		<key>permission_handler_apple_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			NF+YUTBE/Ch2p1vrUOlNokKgvXJaziRf2a/wxYk+pPw=
			</data>
		</dict>
		<key>permission_handler_apple_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			ETZWiZY6EZHpaiLgs59i8FuG0NJKvoBAXBpc7vCamxs=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
