<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/GTMDebugSelectorValidation.h</key>
		<data>
		AOmwXwdRzqyU9zBaOz6AZ/j3HUs=
		</data>
		<key>Headers/GTMDebugThreadValidation.h</key>
		<data>
		ufLEPuSnlBD+3Vj2JF0W9dd1LRA=
		</data>
		<key>Headers/GTMDefines.h</key>
		<data>
		t+OQv3r6RomF13NLLJ9WpN5Vrz8=
		</data>
		<key>Headers/GTMLogger.h</key>
		<data>
		EnWCozMyfVvwXG9Vo1Bw0V+1mYU=
		</data>
		<key>Headers/GTMMethodCheck.h</key>
		<data>
		TVsdzuIPq74vWNS9GazJnMRLZrg=
		</data>
		<key>Headers/GTMNSData+zlib.h</key>
		<data>
		k1bO1+UoC/bV7fbU0JReGfE4qmo=
		</data>
		<key>Headers/GTMNSDictionary+URLArguments.h</key>
		<data>
		CMLY4xyy1xs0FmZV8yfJgx99G14=
		</data>
		<key>Headers/GTMNSString+URLArguments.h</key>
		<data>
		ips8wXBEK8crK2K0oS0JPgd8L9o=
		</data>
		<key>Headers/GoogleToolboxForMac-umbrella.h</key>
		<data>
		QBKh/w7BGyfIXjdy9CILJn4LV+I=
		</data>
		<key>Info.plist</key>
		<data>
		nW1rgSIVPBVGkY25N8DB6Y3WyxU=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		9a/HdunoiUsh1Jz+XjntMeZ2smU=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/GTMDebugSelectorValidation.h</key>
		<dict>
			<key>hash</key>
			<data>
			AOmwXwdRzqyU9zBaOz6AZ/j3HUs=
			</data>
			<key>hash2</key>
			<data>
			GEVsGtKEQzYfiO24CPRPG+PoRa0Svm+inCIsWRODjx4=
			</data>
		</dict>
		<key>Headers/GTMDebugThreadValidation.h</key>
		<dict>
			<key>hash</key>
			<data>
			ufLEPuSnlBD+3Vj2JF0W9dd1LRA=
			</data>
			<key>hash2</key>
			<data>
			LUSLqnMxNRAN2TIOTlqqDAGKnv5NmjmIt42RgbwlbLc=
			</data>
		</dict>
		<key>Headers/GTMDefines.h</key>
		<dict>
			<key>hash</key>
			<data>
			t+OQv3r6RomF13NLLJ9WpN5Vrz8=
			</data>
			<key>hash2</key>
			<data>
			dNQuieS+GnBgjjeI/PADalRe3lVmH4AoMQ/54krmHK4=
			</data>
		</dict>
		<key>Headers/GTMLogger.h</key>
		<dict>
			<key>hash</key>
			<data>
			EnWCozMyfVvwXG9Vo1Bw0V+1mYU=
			</data>
			<key>hash2</key>
			<data>
			yFvkPUc0WouzJtD9DCKRRfaD8n8QRqUcwqA2DNhP9KU=
			</data>
		</dict>
		<key>Headers/GTMMethodCheck.h</key>
		<dict>
			<key>hash</key>
			<data>
			TVsdzuIPq74vWNS9GazJnMRLZrg=
			</data>
			<key>hash2</key>
			<data>
			dINaOQkNn5zMmCXnIMGGndmVqhKENUQ0W+3pOv7AVsA=
			</data>
		</dict>
		<key>Headers/GTMNSData+zlib.h</key>
		<dict>
			<key>hash</key>
			<data>
			k1bO1+UoC/bV7fbU0JReGfE4qmo=
			</data>
			<key>hash2</key>
			<data>
			SOeeKCPkAEVtIlAI7+B/lUNYLKeDMVmuDfaSk4RR3Mo=
			</data>
		</dict>
		<key>Headers/GTMNSDictionary+URLArguments.h</key>
		<dict>
			<key>hash</key>
			<data>
			CMLY4xyy1xs0FmZV8yfJgx99G14=
			</data>
			<key>hash2</key>
			<data>
			ucz70wxkOUbODe84Ppw720yHfUQvMDc8uukuYqh//1Y=
			</data>
		</dict>
		<key>Headers/GTMNSString+URLArguments.h</key>
		<dict>
			<key>hash</key>
			<data>
			ips8wXBEK8crK2K0oS0JPgd8L9o=
			</data>
			<key>hash2</key>
			<data>
			UutpyHfK9Mdw/DONimY7sMbOh5k3QMLCdhY4WsHT8qE=
			</data>
		</dict>
		<key>Headers/GoogleToolboxForMac-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			QBKh/w7BGyfIXjdy9CILJn4LV+I=
			</data>
			<key>hash2</key>
			<data>
			H7ZCYY5gygj0C8NMtSOBRXv5h0lCcqY8+Q8IyPPXvN8=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			9a/HdunoiUsh1Jz+XjntMeZ2smU=
			</data>
			<key>hash2</key>
			<data>
			8ODoXYuVNQuAoPdV2jgOXoSHFtWLzZEaoEjfgscuLkA=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
