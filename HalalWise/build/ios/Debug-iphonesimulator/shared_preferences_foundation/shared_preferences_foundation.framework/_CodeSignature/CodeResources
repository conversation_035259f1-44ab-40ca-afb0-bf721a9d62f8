<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/shared_preferences_foundation-Swift.h</key>
		<data>
		oY/+foRW5QEUi10VXv+ZsxdDhFA=
		</data>
		<key>Headers/shared_preferences_foundation-umbrella.h</key>
		<data>
		3HwT7vyU2akEuk9WxLekNTic7eA=
		</data>
		<key>Info.plist</key>
		<data>
		j1k8vHELIyMGlCFM9dcno7tg0rM=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		2JlPqRKaQWgT2bb1vkexg1Nq/sA=
		</data>
		<key>Modules/shared_preferences_foundation.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		Ehgt4kmk8S/Ra1T9M6zNZZxVGls=
		</data>
		<key>Modules/shared_preferences_foundation.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		MaTH6hbLqFCaRKkiGg5I8kdUFnQ=
		</data>
		<key>Modules/shared_preferences_foundation.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/shared_preferences_foundation.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		SQALSmKcJPj3EY6zbKO9Yrd1HM8=
		</data>
		<key>Modules/shared_preferences_foundation.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<data>
		TLUYnYvfFXexeJB9ZwrB2toQWy4=
		</data>
		<key>Modules/shared_preferences_foundation.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		gcwBsH4BgyFY4sVtNt+/xOKS3vY=
		</data>
		<key>Modules/shared_preferences_foundation.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		aTpvT/s2baGvekXskROst6sEFm0=
		</data>
		<key>Modules/shared_preferences_foundation.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		RXZGr3on+aYhYJD958UKWfoYnmc=
		</data>
		<key>shared_preferences_foundation_privacy.bundle/Info.plist</key>
		<data>
		BDbb8440V5fKhmwtc6mnuIchGhE=
		</data>
		<key>shared_preferences_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		6uLTlq7fgHHWA8emYDf4ImHC+AY=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/shared_preferences_foundation-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			OG2iBicK9+mIV4+X+r+plKj89SBPIeXZgxjuSevwvAA=
			</data>
		</dict>
		<key>Headers/shared_preferences_foundation-umbrella.h</key>
		<dict>
			<key>hash2</key>
			<data>
			su5Uv36/prKNtVojyB1A6T1k258lLooi1Hi7YUVwbbA=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			Q0UmBz/CGB1z75ZFA4+mlzbd0g+wymylVwJDqUxGkZM=
			</data>
		</dict>
		<key>Modules/shared_preferences_foundation.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			c8a0a1171BP7/zWkX+ROCVAJz3NQVhgKjOV3PTpnv/s=
			</data>
		</dict>
		<key>Modules/shared_preferences_foundation.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			aT25w8VugCGzlbpxsBCzEk1TVIb0aKWwS3l+ivpf0J8=
			</data>
		</dict>
		<key>Modules/shared_preferences_foundation.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/shared_preferences_foundation.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			ZHICZT/EKhNoLxcZoYIzTfy8rOvcX968ScmPP69Zt9A=
			</data>
		</dict>
		<key>Modules/shared_preferences_foundation.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			theLYn4Es6F2uYB//loO0qcVhGOI0fhu8lDsEfAd05I=
			</data>
		</dict>
		<key>Modules/shared_preferences_foundation.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Qnesa0n4URGWAopawg9bGx36dUwkYV00BoCJ8LFzlyg=
			</data>
		</dict>
		<key>Modules/shared_preferences_foundation.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			Z4lULitR5puQzYcA6DzQtUbQOjmaFxGcP5NReMiDhhk=
			</data>
		</dict>
		<key>Modules/shared_preferences_foundation.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			9LeXNLZxiTAKKtFF4lsLQabf/iJxwtnIjWU5O0hWSqA=
			</data>
		</dict>
		<key>shared_preferences_foundation_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			QwhUtshyybkth9gjXSAy1iIx2ClhXcPHeXO3uqKf4kQ=
			</data>
		</dict>
		<key>shared_preferences_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			BWQouTi9VwGKYAGdFcPB7i+nJ/I2h1mLu9k0hIsYCxo=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
