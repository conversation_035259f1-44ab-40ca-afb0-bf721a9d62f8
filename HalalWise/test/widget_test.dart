import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:halalwise/main.dart';

void main() {
  testWidgets('HalalWise app smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const HalalWiseApp());

    // Verify that the app title is displayed
    expect(find.text('HalalWise'), findsOneWidget);

    // Verify that the welcome message is displayed
    expect(find.text('Assalamu Alaikum!'), findsOneWidget);

    // Verify that quick action buttons are present
    expect(find.text('Scan Barcode'), findsOneWidget);
    expect(find.text('Scan Label'), findsOneWidget);
    expect(find.text('Manual Input'), findsOneWidget);
    expect(find.text('Voice Input'), findsOneWidget);
  });

  testWidgets('Navigation to barcode scanner', (WidgetTester tester) async {
    await tester.pumpWidget(const HalalWiseApp());

    // Tap on the Scan Barcode button
    await tester.tap(find.text('Scan Barcode'));
    await tester.pumpAndSettle();

    // Verify that we navigated to the barcode scanner
    expect(find.text('Scan Barcode'), findsOneWidget);
  });
}
