name: halalwise
description: A modern AI-powered app to verify halal and health status of food items
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter

  # UI and Navigation
  cupertino_icons: ^1.0.6
  flutter_localizations:
    sdk: flutter
  intl: ^0.18.1

  # State Management
  provider: ^6.1.1

  # Database
  sqflite: ^2.3.0
  path: ^1.8.3

  # Cloud Database
  firebase_core: ^2.24.2
  cloud_firestore: ^4.13.6

  # Camera and Scanning
  camera: ^0.10.5+5
  mobile_scanner: ^3.5.6
  image_picker: ^1.0.4

  # OCR and Image Processing
  google_mlkit_text_recognition: ^0.10.0
  image: ^4.1.3

  # HTTP and API
  http: ^1.1.0
  dio: ^5.3.2

  # Storage and Caching
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Permissions
  permission_handler: ^11.0.1

  # Audio for voice input
  speech_to_text: ^6.6.0

  # Utilities
  uuid: ^4.1.0
  crypto: ^3.0.3
  get_it: ^7.6.4

  # UI Components
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0

  # Accessibility
  flutter_tts: ^3.8.5

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  hive_generator: ^2.0.1
  build_runner: ^2.4.7

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/

  fonts:
    - family: NotoSansArabic
      fonts:
        - asset: assets/fonts/NotoSansArabic-Regular.ttf
        - asset: assets/fonts/NotoSansArabic-Bold.ttf
          weight: 700
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins-Regular.ttf
        - asset: assets/fonts/Poppins-Medium.ttf
          weight: 500
        - asset: assets/fonts/Poppins-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Poppins-Bold.ttf
          weight: 700

flutter_intl:
  enabled: true
  class_name: S
  main_locale: en
  arb_dir: lib/l10n
  output_dir: lib/generated
