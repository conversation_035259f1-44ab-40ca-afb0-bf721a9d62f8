#!/bin/bash

# HalalWise Build Script
echo "🚀 Building HalalWise App..."

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter is not installed. Please install Flutter first."
    exit 1
fi

# Get dependencies
echo "📦 Getting dependencies..."
flutter pub get

# Run code generation if needed
echo "🔧 Running code generation..."
flutter packages pub run build_runner build --delete-conflicting-outputs

# Analyze code
echo "🔍 Analyzing code..."
flutter analyze

# Run tests
echo "🧪 Running tests..."
flutter test

# Build for Android
echo "🤖 Building for Android..."
flutter build apk --release

# Build for iOS (if on macOS)
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🍎 Building for iOS..."
    flutter build ios --release --no-codesign
else
    echo "⚠️  Skipping iOS build (not on macOS)"
fi

echo "✅ Build completed successfully!"
echo "📱 Android APK: build/app/outputs/flutter-apk/app-release.apk"

if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🍎 iOS build: build/ios/iphoneos/Runner.app"
fi
