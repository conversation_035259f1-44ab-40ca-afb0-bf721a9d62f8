#!/bin/bash

# HalalWise Setup Validation Script
echo "🔍 HalalWise Setup Validation"
echo "============================="
echo ""

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check Flutter installation
echo "🔍 Checking Flutter..."
if command -v flutter &> /dev/null; then
    FLUTTER_VERSION=$(flutter --version | head -n1 | cut -d' ' -f2)
    print_success "Flutter $FLUTTER_VERSION installed"
    
    # Check Flutter doctor
    echo ""
    print_info "Running Flutter doctor..."
    flutter doctor
else
    print_error "Flutter not found in PATH"
    echo "   Please install Flutter: https://docs.flutter.dev/get-started/install"
    echo "   Or add to PATH: export PATH=\"\$PATH:~/flutter/bin\""
fi

echo ""

# Check Node.js installation
echo "🔍 Checking Node.js..."
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    print_success "Node.js $NODE_VERSION installed"
    
    if command -v npm &> /dev/null; then
        NPM_VERSION=$(npm --version)
        print_success "npm $NPM_VERSION available"
    else
        print_warning "npm not found"
    fi
else
    print_warning "Node.js not found (optional for backend)"
    echo "   Install from: https://nodejs.org/"
fi

echo ""

# Check project files
echo "🔍 Checking project files..."

# Check pubspec.yaml
if [ -f "pubspec.yaml" ]; then
    print_success "pubspec.yaml found"
    
    # Check for duplicate dependencies
    if grep -n "shared_preferences" pubspec.yaml | wc -l | grep -q "1"; then
        print_success "No duplicate dependencies in pubspec.yaml"
    else
        print_error "Duplicate dependencies found in pubspec.yaml"
    fi
else
    print_error "pubspec.yaml not found"
fi

# Check main.dart
if [ -f "lib/main.dart" ]; then
    print_success "lib/main.dart found"
else
    print_error "lib/main.dart not found"
fi

# Check AI service
if [ -f "lib/core/network/ai_service.dart" ]; then
    print_success "AI service found"
    
    # Check if API key is configured
    if grep -q "AIzaSyD8uelHhB2IEZWduY92L2CTt7sXzsD7oV8" lib/core/network/ai_service.dart; then
        print_success "Gemini API key configured"
    else
        print_warning "Gemini API key not found in AI service"
    fi
else
    print_error "AI service not found"
fi

# Check database helper
if [ -f "lib/core/database/database_helper.dart" ]; then
    print_success "Database helper found"
else
    print_error "Database helper not found"
fi

# Check sample data
if [ -f "lib/core/database/sample_data.dart" ]; then
    print_success "Sample data found"
else
    print_error "Sample data not found"
fi

echo ""

# Check backend files
echo "🔍 Checking backend files..."

if [ -d "backend" ]; then
    print_success "Backend directory found"
    
    if [ -f "backend/server.js" ]; then
        print_success "Backend server.js found"
    else
        print_error "Backend server.js not found"
    fi
    
    if [ -f "backend/package.json" ]; then
        print_success "Backend package.json found"
    else
        print_error "Backend package.json not found"
    fi
    
    if [ -f "backend/.env" ]; then
        print_success "Backend .env found"
        
        # Check if API key is in .env
        if grep -q "AIzaSyD8uelHhB2IEZWduY92L2CTt7sXzsD7oV8" backend/.env; then
            print_success "Gemini API key configured in backend"
        else
            print_warning "Gemini API key not found in backend .env"
        fi
    else
        print_warning "Backend .env not found (will use defaults)"
    fi
else
    print_warning "Backend directory not found (optional)"
fi

echo ""

# Check documentation
echo "🔍 Checking documentation..."

docs=("README.md" "PREVIEW_GUIDE.md" "LOCAL_SETUP_GUIDE.md" "GEMINI_INTEGRATION_GUIDE.md")
for doc in "${docs[@]}"; do
    if [ -f "$doc" ]; then
        print_success "$doc found"
    else
        print_warning "$doc not found"
    fi
done

echo ""

# Summary and recommendations
echo "📋 Summary and Recommendations"
echo "=============================="

if command -v flutter &> /dev/null && [ -f "pubspec.yaml" ] && [ -f "lib/main.dart" ]; then
    print_success "Core Flutter setup is ready!"
    echo ""
    print_info "Next steps:"
    echo "   1. Run: flutter pub get"
    echo "   2. Run: flutter run -d chrome"
    echo "   3. Test with barcode: 5449000000996"
    echo ""
else
    print_error "Setup incomplete. Please check the issues above."
    echo ""
    print_info "Quick fixes:"
    echo "   1. Install Flutter if missing"
    echo "   2. Ensure you're in the HalalWise directory"
    echo "   3. Check file permissions"
    echo ""
fi

# Test barcode suggestions
echo "🧪 Test Barcodes (Pre-loaded in Database)"
echo "========================================="
echo "   5449000000996 → Coca-Cola (Halal ✅)"
echo "   7622210951557 → Oreo (Questionable ⚠️)"
echo "   3017620422003 → Nutella (Questionable ⚠️)"
echo "   8901030865507 → Maggi Noodles (Halal ✅)"
echo "   4902777193731 → Kit Kat (Questionable ⚠️)"
echo ""

print_info "Ready to launch? Run: ./launch_halalwise.sh"
