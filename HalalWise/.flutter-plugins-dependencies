{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "camera_avfoundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.19/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-4.17.5/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_tts", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_tts-3.8.5/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_mlkit_commons", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_commons-0.5.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_mlkit_text_recognition", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_text_recognition-0.10.0/", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "image_picker_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "mobile_scanner", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mobile_scanner-3.5.7/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "speech_to_text", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-6.6.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "camera_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_android-0.10.10+3/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"], "dev_dependency": false}, {"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-4.17.5/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_plugin_android_lifecycle", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_tts", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_tts-3.8.5/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_mlkit_commons", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_commons-0.5.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_mlkit_text_recognition", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_text_recognition-0.10.0/", "native_build": true, "dependencies": ["google_mlkit_commons"], "dev_dependency": false}, {"name": "image_picker_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"], "dev_dependency": false}, {"name": "mobile_scanner", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mobile_scanner-3.5.7/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "speech_to_text", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-6.6.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-4.17.5/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "file_selector_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_tts", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_tts-3.8.5/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/", "native_build": false, "dependencies": ["file_selector_macos"], "dev_dependency": false}, {"name": "mobile_scanner", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mobile_scanner-3.5.7/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "speech_to_text_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text_macos-1.1.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "file_selector_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/", "native_build": false, "dependencies": ["file_selector_linux"], "dev_dependency": false}, {"name": "path_provider_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/", "native_build": false, "dependencies": ["path_provider_linux"], "dev_dependency": false}], "windows": [{"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-4.17.5/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "file_selector_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_tts", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_tts-3.8.5/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/", "native_build": false, "dependencies": ["file_selector_windows"], "dev_dependency": false}, {"name": "path_provider_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/", "native_build": false, "dependencies": ["path_provider_windows"], "dev_dependency": false}], "web": [{"name": "camera_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_web-0.3.5/", "dependencies": [], "dev_dependency": false}, {"name": "cloud_firestore_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_web-3.12.5/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_core_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/", "dependencies": [], "dev_dependency": false}, {"name": "flutter_tts", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_tts-3.8.5/", "dependencies": [], "dev_dependency": false}, {"name": "image_picker_for_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/", "dependencies": [], "dev_dependency": false}, {"name": "mobile_scanner", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mobile_scanner-3.5.7/", "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_html", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/", "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/", "dependencies": [], "dev_dependency": false}, {"name": "speech_to_text", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-6.6.2/", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "camera", "dependencies": ["camera_android", "camera_avfoundation", "camera_web", "flutter_plugin_android_lifecycle"]}, {"name": "camera_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "camera_avfoundation", "dependencies": []}, {"name": "camera_web", "dependencies": []}, {"name": "cloud_firestore", "dependencies": ["cloud_firestore_web", "firebase_core"]}, {"name": "cloud_firestore_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "file_selector_linux", "dependencies": []}, {"name": "file_selector_macos", "dependencies": []}, {"name": "file_selector_windows", "dependencies": []}, {"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "flutter_tts", "dependencies": []}, {"name": "google_mlkit_commons", "dependencies": []}, {"name": "google_mlkit_text_recognition", "dependencies": ["google_mlkit_commons"]}, {"name": "image_picker", "dependencies": ["image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_windows"]}, {"name": "image_picker_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "image_picker_for_web", "dependencies": []}, {"name": "image_picker_ios", "dependencies": []}, {"name": "image_picker_linux", "dependencies": ["file_selector_linux"]}, {"name": "image_picker_macos", "dependencies": ["file_selector_macos"]}, {"name": "image_picker_windows", "dependencies": ["file_selector_windows"]}, {"name": "mobile_scanner", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_html", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "speech_to_text", "dependencies": ["speech_to_text_macos"]}, {"name": "speech_to_text_macos", "dependencies": []}, {"name": "sqflite", "dependencies": ["sqflite_android", "sqflite_darwin"]}, {"name": "sqflite_android", "dependencies": []}, {"name": "sqflite_darwin", "dependencies": []}], "date_created": "2025-05-28 00:26:34.814849", "version": "3.32.0", "swift_package_manager_enabled": {"ios": false, "macos": false}}