# 📱 HalalWise App Preview Guide

> **Complete guide to preview your HalalWise app with backend and database**

## 🎯 What You'll See

After following this guide, you'll have:
- ✅ **Working Flutter app** with beautiful Islamic UI
- ✅ **Functional barcode scanner** with camera integration
- ✅ **AI-powered analysis** using your Gemini API key
- ✅ **Local database** with 1000+ ingredients and sample products
- ✅ **Backend server** for API management and analytics
- ✅ **Usage dashboard** showing API costs and limits

---

## 🚀 Quick Preview (5 Minutes)

### Option A: Flutter App Only
```bash
# 1. Navigate to project
cd HalalWise

# 2. Install dependencies
flutter pub get

# 3. Run the app
./preview_app.sh
# OR manually: flutter run -d chrome
```

### Option B: Full Stack (App + Backend)
```bash
# Terminal 1: Start Backend
cd HalalWise/backend
npm install
npm start

# Terminal 2: Start Flutter App
cd HalalWise
flutter run -d chrome
```

---

## 📋 Detailed Setup Instructions

### Prerequisites Check
```bash
# Required software
✅ Flutter 3.10+ installed
✅ Node.js 18+ installed (for backend)
✅ Chrome browser (for web preview)
✅ Android Studio/VS Code (optional)

# Check versions
flutter --version
node --version
```

### Step 1: Flutter App Setup
```bash
# Navigate to project
cd HalalWise

# Install Flutter dependencies
flutter pub get

# Check for issues
flutter doctor

# Run the app
flutter run -d chrome
```

### Step 2: Backend Server Setup (Optional)
```bash
# Open new terminal
cd HalalWise/backend

# Install Node.js dependencies
npm install

# Start the server
npm start
# OR use the script: ./start.sh

# Server will start on http://localhost:3000
```

### Step 3: Test the Integration
```bash
# 1. Open app in browser (should auto-open)
# 2. Navigate to Settings
# 3. Check API Usage widget
# 4. Try scanning a barcode: 5449000000996 (Coca-Cola)
# 5. View the analysis results
```

---

## 🧪 Testing Features

### Test Barcodes (Pre-loaded in Database)
```
Coca-Cola Classic: 5449000000996
├── Status: Halal ✅
├── Confidence: 95%
└── Source: Local database

Oreo Cookies: 7622210951557
├── Status: Questionable ⚠️
├── Confidence: 70%
└── Reason: Contains questionable emulsifiers

Nutella: 3017620422003
├── Status: Questionable ⚠️
├── Confidence: 60%
└── Reason: Lecithin source unclear

Maggi Noodles: 8901030865507
├── Status: Halal ✅
├── Confidence: 90%
└── Certifications: JAKIM, Halal India

Kit Kat: 4902777193731
├── Status: Questionable ⚠️
├── Confidence: 50%
└── Reason: Regional variations exist
```

### Test Unknown Products (Will Use AI)
```
Try scanning any other barcode:
├── App will check local database first
├── If not found, will use Gemini AI
├── Results will be cached for future use
└── Usage will be tracked in settings
```

### Test Manual Input
```
1. Go to Manual Input (placeholder page)
2. Enter product name: "Chicken Biryani"
3. Add ingredients: "Rice, chicken, spices, oil"
4. Get AI analysis (when implemented)
```

---

## 📊 Backend API Testing

### Health Check
```bash
curl http://localhost:3000/api/health

Response:
{
  "status": "healthy",
  "timestamp": "2024-12-01T10:00:00Z"
}
```

### Test Analysis Endpoint
```bash
curl -X POST http://localhost:3000/api/analyze \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Analyze halal status: Chicken sandwich with mayo",
    "type": "halal_analysis"
  }'

Response:
{
  "result": "Based on the ingredients...",
  "metadata": {
    "inputTokens": 15,
    "outputTokens": 45,
    "estimatedCost": 0.00002
  }
}
```

### Check Usage Statistics
```bash
curl http://localhost:3000/api/usage

Response:
{
  "today": {
    "totalCalls": 5,
    "totalTokens": 300,
    "types": {
      "halal_analysis": 5
    }
  },
  "limits": {
    "dailyCalls": 50,
    "remainingCalls": 45
  }
}
```

---

## 🎨 UI Features to Test

### Home Screen
```
✅ Islamic greeting: "Assalamu Alaikum!"
✅ Quick action buttons with icons
✅ Recent scans section (empty initially)
✅ Tips for better results
✅ Smooth animations and transitions
```

### Barcode Scanner
```
✅ Camera permission request
✅ Real-time barcode detection
✅ Custom overlay with scanning frame
✅ Manual barcode input option
✅ Flash toggle functionality
```

### Results Page
```
✅ Product information display
✅ Halal status with color coding
✅ Confidence percentage
✅ Detailed reasoning
✅ Health score visualization
✅ Ingredients list with highlighting
✅ Nutritional information
✅ Allergen warnings
✅ Certification badges
```

### Settings Page
```
✅ API usage dashboard
✅ Daily usage tracking
✅ Cost estimation
✅ Remaining calls counter
✅ Progress bar visualization
✅ API key configuration
```

---

## 🔧 Troubleshooting

### Common Issues

#### "Flutter not found"
```bash
# Install Flutter
https://docs.flutter.dev/get-started/install

# Add to PATH
export PATH="$PATH:/path/to/flutter/bin"
```

#### "Node.js not found"
```bash
# Install Node.js
https://nodejs.org/

# Verify installation
node --version
npm --version
```

#### "Camera permission denied"
```bash
# For Chrome: Allow camera access when prompted
# For mobile: Check app permissions in settings
```

#### "API key not working"
```bash
# Verify API key in lib/core/network/ai_service.dart
# Check Gemini API quota: https://makersuite.google.com/
# Ensure key has proper permissions
```

#### "Backend connection failed"
```bash
# Check if backend is running: http://localhost:3000/api/health
# Verify port 3000 is not in use
# Check firewall settings
```

#### "Database errors"
```bash
# Clear app data and restart
# Check device storage space
# Verify SQLite permissions
```

---

## 📱 Device-Specific Testing

### Chrome Browser (Recommended for Preview)
```
✅ Full UI functionality
✅ Barcode scanner (with webcam)
✅ Database operations
✅ API integration
⚠️ Camera quality may vary
```

### Android Device/Emulator
```
✅ Native performance
✅ High-quality camera
✅ Full feature set
✅ Real-world testing
📱 Connect via USB or use emulator
```

### iOS Simulator (macOS only)
```
✅ iOS-specific testing
✅ Native UI elements
⚠️ Camera simulation only
🍎 Requires Xcode installation
```

---

## 📊 Performance Monitoring

### Key Metrics to Watch
```
App Performance:
├── Startup time: < 3 seconds
├── Scan time: < 2 seconds
├── Database queries: < 100ms
└── Memory usage: < 100MB

API Performance:
├── Response time: < 2 seconds
├── Success rate: > 95%
├── Daily usage: Track in settings
└── Cost per analysis: ~$0.0001
```

### Monitoring Tools
```
Flutter:
├── Flutter Inspector (VS Code/Android Studio)
├── Performance overlay: flutter run --profile
└── Memory profiling: DevTools

Backend:
├── Console logs: npm start
├── API health: /api/health endpoint
└── Usage stats: /api/usage endpoint
```

---

## 🎯 Next Steps After Preview

### Immediate Actions
1. **Test all features** thoroughly
2. **Check API usage** in settings
3. **Verify database** functionality
4. **Test on mobile device** for real-world experience

### Short-term Improvements
1. **Implement missing features** (OCR, manual input, voice)
2. **Add more test products** to database
3. **Optimize AI prompts** for better accuracy
4. **Enhance UI/UX** based on testing

### Long-term Goals
1. **Deploy to app stores** (iOS/Android)
2. **Scale backend** for production
3. **Add user authentication** and cloud sync
4. **Implement premium features** and monetization

---

## 🤝 Support

### Getting Help
- **GitHub Issues**: Technical problems
- **Documentation**: Check README.md files
- **Discord Community**: Real-time support
- **Email**: Business inquiries

### Sharing Feedback
- **Screenshots**: Share your preview results
- **Bug Reports**: Report any issues found
- **Feature Requests**: Suggest improvements
- **Success Stories**: Share your experience

---

**🚀 Ready to preview? Run the commands above and see your HalalWise app in action!**

**Questions?** Check the troubleshooting section or ask for help in our community.
