{"name": "firebase_core", "version": "2.32.0", "summary": "Flutter plugin for Firebase Core, enabling connecting to multiple Firebase apps.", "description": "Flutter plugin for Firebase Core, enabling connecting to multiple Firebase apps.", "homepage": "https://firebase.google.com/docs/flutter/setup", "license": {"file": "../LICENSE"}, "authors": "The Chromium Authors", "source": {"path": "."}, "source_files": "Classes/**/*", "platforms": {"osx": "10.13"}, "dependencies": {"FlutterMacOS": [], "Firebase/CoreOnly": ["~> 10.25.0"]}, "static_framework": true, "pod_target_xcconfig": {"GCC_PREPROCESSOR_DEFINITIONS": "LIBRARY_VERSION=\\@\\\"2.32.0\\\" LIBRARY_NAME=\\@\\\"flutter-fire-core\\\"", "DEFINES_MODULE": "YES"}}