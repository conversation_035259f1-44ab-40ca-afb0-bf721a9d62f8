{"name": "mobile_scanner", "version": "3.5.6", "summary": "An universal scanner for Flutter based on MLKit.", "description": "An universal scanner for Flutter based on MLKit.", "homepage": "http://example.com", "license": {"file": "../LICENSE"}, "authors": {"Your Company": "<EMAIL>"}, "source": {"path": "."}, "source_files": "Classes/**/*", "dependencies": {"FlutterMacOS": []}, "platforms": {"osx": "10.14"}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES"}, "swift_versions": "5.0", "swift_version": "5.0"}