{"name": "cloud_firestore", "version": "4.17.5", "summary": "Flutter plugin for Cloud Firestore, a cloud-hosted, noSQL database with live synchronization and offline support on Android and iOS.", "description": "Flutter plugin for Cloud Firestore, a cloud-hosted, noSQL database with live synchronization and offline support on Android and iOS.", "homepage": "https://firebase.google.com/docs/firestore", "license": {"file": "../LICENSE"}, "authors": "The Chromium Authors", "source": {"path": "."}, "source_files": "Classes/**/*.{h,m}", "public_header_files": "Classes/Public/*.h", "private_header_files": "Classes/Private/*.h", "platforms": {"osx": "10.13"}, "dependencies": {"FlutterMacOS": [], "firebase_core": [], "Firebase/CoreOnly": ["~> 10.25.0"], "Firebase/Firestore": ["~> 10.25.0"]}, "static_framework": true, "pod_target_xcconfig": {"GCC_PREPROCESSOR_DEFINITIONS": "LIBRARY_VERSION=\\@\\\"4.17.5\\\" LIBRARY_NAME=\\@\\\"flutter-fire-fst\\\"", "DEFINES_MODULE": "YES"}}