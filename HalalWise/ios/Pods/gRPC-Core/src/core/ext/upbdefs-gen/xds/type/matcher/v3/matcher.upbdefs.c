/* This file was generated by upb_generator from the input file:
 *
 *     xds/type/matcher/v3/matcher.proto
 *
 * Do not edit -- your changes will be discarded when the file is
 * regenerated. */

#include "upb/reflection/def.h"
#include "xds/type/matcher/v3/matcher.upbdefs.h"
#include "xds/type/matcher/v3/matcher.upb_minitable.h"

extern _upb_DefPool_Init xds_annotations_v3_status_proto_upbdefinit;
extern _upb_DefPool_Init xds_core_v3_extension_proto_upbdefinit;
extern _upb_DefPool_Init xds_type_matcher_v3_string_proto_upbdefinit;
extern _upb_DefPool_Init validate_validate_proto_upbdefinit;
static const char descriptor[2330] = {'\n', '!', 'x', 'd', 's', '/', 't', 'y', 'p', 'e', '/', 'm', 'a', 't', 'c', 'h', 'e', 'r', '/', 'v', '3', '/', 'm', 'a', 't', 
'c', 'h', 'e', 'r', '.', 'p', 'r', 'o', 't', 'o', '\022', '\023', 'x', 'd', 's', '.', 't', 'y', 'p', 'e', '.', 'm', 'a', 't', 'c', 
'h', 'e', 'r', '.', 'v', '3', '\032', '\037', 'x', 'd', 's', '/', 'a', 'n', 'n', 'o', 't', 'a', 't', 'i', 'o', 'n', 's', '/', 'v', 
'3', '/', 's', 't', 'a', 't', 'u', 's', '.', 'p', 'r', 'o', 't', 'o', '\032', '\033', 'x', 'd', 's', '/', 'c', 'o', 'r', 'e', '/', 
'v', '3', '/', 'e', 'x', 't', 'e', 'n', 's', 'i', 'o', 'n', '.', 'p', 'r', 'o', 't', 'o', '\032', ' ', 'x', 'd', 's', '/', 't', 
'y', 'p', 'e', '/', 'm', 'a', 't', 'c', 'h', 'e', 'r', '/', 'v', '3', '/', 's', 't', 'r', 'i', 'n', 'g', '.', 'p', 'r', 'o', 
't', 'o', '\032', '\027', 'v', 'a', 'l', 'i', 'd', 'a', 't', 'e', '/', 'v', 'a', 'l', 'i', 'd', 'a', 't', 'e', '.', 'p', 'r', 'o', 
't', 'o', '\"', '\200', '\020', '\n', '\007', 'M', 'a', 't', 'c', 'h', 'e', 'r', '\022', 'M', '\n', '\014', 'm', 'a', 't', 'c', 'h', 'e', 'r', 
'_', 'l', 'i', 's', 't', '\030', '\001', ' ', '\001', '(', '\013', '2', '(', '.', 'x', 'd', 's', '.', 't', 'y', 'p', 'e', '.', 'm', 'a', 
't', 'c', 'h', 'e', 'r', '.', 'v', '3', '.', 'M', 'a', 't', 'c', 'h', 'e', 'r', '.', 'M', 'a', 't', 'c', 'h', 'e', 'r', 'L', 
'i', 's', 't', 'H', '\000', 'R', '\013', 'm', 'a', 't', 'c', 'h', 'e', 'r', 'L', 'i', 's', 't', '\022', 'M', '\n', '\014', 'm', 'a', 't', 
'c', 'h', 'e', 'r', '_', 't', 'r', 'e', 'e', '\030', '\002', ' ', '\001', '(', '\013', '2', '(', '.', 'x', 'd', 's', '.', 't', 'y', 'p', 
'e', '.', 'm', 'a', 't', 'c', 'h', 'e', 'r', '.', 'v', '3', '.', 'M', 'a', 't', 'c', 'h', 'e', 'r', '.', 'M', 'a', 't', 'c', 
'h', 'e', 'r', 'T', 'r', 'e', 'e', 'H', '\000', 'R', '\013', 'm', 'a', 't', 'c', 'h', 'e', 'r', 'T', 'r', 'e', 'e', '\022', 'D', '\n', 
'\013', 'o', 'n', '_', 'n', 'o', '_', 'm', 'a', 't', 'c', 'h', '\030', '\003', ' ', '\001', '(', '\013', '2', '$', '.', 'x', 'd', 's', '.', 
't', 'y', 'p', 'e', '.', 'm', 'a', 't', 'c', 'h', 'e', 'r', '.', 'v', '3', '.', 'M', 'a', 't', 'c', 'h', 'e', 'r', '.', 'O', 
'n', 'M', 'a', 't', 'c', 'h', 'R', '\t', 'o', 'n', 'N', 'o', 'M', 'a', 't', 'c', 'h', '\032', '\221', '\001', '\n', '\007', 'O', 'n', 'M', 
'a', 't', 'c', 'h', '\022', '8', '\n', '\007', 'm', 'a', 't', 'c', 'h', 'e', 'r', '\030', '\001', ' ', '\001', '(', '\013', '2', '\034', '.', 'x', 
'd', 's', '.', 't', 'y', 'p', 'e', '.', 'm', 'a', 't', 'c', 'h', 'e', 'r', '.', 'v', '3', '.', 'M', 'a', 't', 'c', 'h', 'e', 
'r', 'H', '\000', 'R', '\007', 'm', 'a', 't', 'c', 'h', 'e', 'r', '\022', ';', '\n', '\006', 'a', 'c', 't', 'i', 'o', 'n', '\030', '\002', ' ', 
'\001', '(', '\013', '2', '!', '.', 'x', 'd', 's', '.', 'c', 'o', 'r', 'e', '.', 'v', '3', '.', 'T', 'y', 'p', 'e', 'd', 'E', 'x', 
't', 'e', 'n', 's', 'i', 'o', 'n', 'C', 'o', 'n', 'f', 'i', 'g', 'H', '\000', 'R', '\006', 'a', 'c', 't', 'i', 'o', 'n', 'B', '\017', 
'\n', '\010', 'o', 'n', '_', 'm', 'a', 't', 'c', 'h', '\022', '\003', '\370', 'B', '\001', '\032', '\266', '\010', '\n', '\013', 'M', 'a', 't', 'c', 'h', 
'e', 'r', 'L', 'i', 's', 't', '\022', '[', '\n', '\010', 'm', 'a', 't', 'c', 'h', 'e', 'r', 's', '\030', '\001', ' ', '\003', '(', '\013', '2', 
'5', '.', 'x', 'd', 's', '.', 't', 'y', 'p', 'e', '.', 'm', 'a', 't', 'c', 'h', 'e', 'r', '.', 'v', '3', '.', 'M', 'a', 't', 
'c', 'h', 'e', 'r', '.', 'M', 'a', 't', 'c', 'h', 'e', 'r', 'L', 'i', 's', 't', '.', 'F', 'i', 'e', 'l', 'd', 'M', 'a', 't', 
'c', 'h', 'e', 'r', 'B', '\010', '\372', 'B', '\005', '\222', '\001', '\002', '\010', '\001', 'R', '\010', 'm', 'a', 't', 'c', 'h', 'e', 'r', 's', '\032', 
'\221', '\006', '\n', '\t', 'P', 'r', 'e', 'd', 'i', 'c', 'a', 't', 'e', '\022', 'o', '\n', '\020', 's', 'i', 'n', 'g', 'l', 'e', '_', 'p', 
'r', 'e', 'd', 'i', 'c', 'a', 't', 'e', '\030', '\001', ' ', '\001', '(', '\013', '2', 'B', '.', 'x', 'd', 's', '.', 't', 'y', 'p', 'e', 
'.', 'm', 'a', 't', 'c', 'h', 'e', 'r', '.', 'v', '3', '.', 'M', 'a', 't', 'c', 'h', 'e', 'r', '.', 'M', 'a', 't', 'c', 'h', 
'e', 'r', 'L', 'i', 's', 't', '.', 'P', 'r', 'e', 'd', 'i', 'c', 'a', 't', 'e', '.', 'S', 'i', 'n', 'g', 'l', 'e', 'P', 'r', 
'e', 'd', 'i', 'c', 'a', 't', 'e', 'H', '\000', 'R', '\017', 's', 'i', 'n', 'g', 'l', 'e', 'P', 'r', 'e', 'd', 'i', 'c', 'a', 't', 
'e', '\022', 'a', '\n', '\n', 'o', 'r', '_', 'm', 'a', 't', 'c', 'h', 'e', 'r', '\030', '\002', ' ', '\001', '(', '\013', '2', '@', '.', 'x', 
'd', 's', '.', 't', 'y', 'p', 'e', '.', 'm', 'a', 't', 'c', 'h', 'e', 'r', '.', 'v', '3', '.', 'M', 'a', 't', 'c', 'h', 'e', 
'r', '.', 'M', 'a', 't', 'c', 'h', 'e', 'r', 'L', 'i', 's', 't', '.', 'P', 'r', 'e', 'd', 'i', 'c', 'a', 't', 'e', '.', 'P', 
'r', 'e', 'd', 'i', 'c', 'a', 't', 'e', 'L', 'i', 's', 't', 'H', '\000', 'R', '\t', 'o', 'r', 'M', 'a', 't', 'c', 'h', 'e', 'r', 
'\022', 'c', '\n', '\013', 'a', 'n', 'd', '_', 'm', 'a', 't', 'c', 'h', 'e', 'r', '\030', '\003', ' ', '\001', '(', '\013', '2', '@', '.', 'x', 
'd', 's', '.', 't', 'y', 'p', 'e', '.', 'm', 'a', 't', 'c', 'h', 'e', 'r', '.', 'v', '3', '.', 'M', 'a', 't', 'c', 'h', 'e', 
'r', '.', 'M', 'a', 't', 'c', 'h', 'e', 'r', 'L', 'i', 's', 't', '.', 'P', 'r', 'e', 'd', 'i', 'c', 'a', 't', 'e', '.', 'P', 
'r', 'e', 'd', 'i', 'c', 'a', 't', 'e', 'L', 'i', 's', 't', 'H', '\000', 'R', '\n', 'a', 'n', 'd', 'M', 'a', 't', 'c', 'h', 'e', 
'r', '\022', 'U', '\n', '\013', 'n', 'o', 't', '_', 'm', 'a', 't', 'c', 'h', 'e', 'r', '\030', '\004', ' ', '\001', '(', '\013', '2', '2', '.', 
'x', 'd', 's', '.', 't', 'y', 'p', 'e', '.', 'm', 'a', 't', 'c', 'h', 'e', 'r', '.', 'v', '3', '.', 'M', 'a', 't', 'c', 'h', 
'e', 'r', '.', 'M', 'a', 't', 'c', 'h', 'e', 'r', 'L', 'i', 's', 't', '.', 'P', 'r', 'e', 'd', 'i', 'c', 'a', 't', 'e', 'H', 
'\000', 'R', '\n', 'n', 'o', 't', 'M', 'a', 't', 'c', 'h', 'e', 'r', '\032', '\363', '\001', '\n', '\017', 'S', 'i', 'n', 'g', 'l', 'e', 'P', 
'r', 'e', 'd', 'i', 'c', 'a', 't', 'e', '\022', 'A', '\n', '\005', 'i', 'n', 'p', 'u', 't', '\030', '\001', ' ', '\001', '(', '\013', '2', '!', 
'.', 'x', 'd', 's', '.', 'c', 'o', 'r', 'e', '.', 'v', '3', '.', 'T', 'y', 'p', 'e', 'd', 'E', 'x', 't', 'e', 'n', 's', 'i', 
'o', 'n', 'C', 'o', 'n', 'f', 'i', 'g', 'B', '\010', '\372', 'B', '\005', '\212', '\001', '\002', '\020', '\001', 'R', '\005', 'i', 'n', 'p', 'u', 't', 
'\022', 'E', '\n', '\013', 'v', 'a', 'l', 'u', 'e', '_', 'm', 'a', 't', 'c', 'h', '\030', '\002', ' ', '\001', '(', '\013', '2', '\"', '.', 'x', 
'd', 's', '.', 't', 'y', 'p', 'e', '.', 'm', 'a', 't', 'c', 'h', 'e', 'r', '.', 'v', '3', '.', 'S', 't', 'r', 'i', 'n', 'g', 
'M', 'a', 't', 'c', 'h', 'e', 'r', 'H', '\000', 'R', '\n', 'v', 'a', 'l', 'u', 'e', 'M', 'a', 't', 'c', 'h', '\022', 'F', '\n', '\014', 
'c', 'u', 's', 't', 'o', 'm', '_', 'm', 'a', 't', 'c', 'h', '\030', '\003', ' ', '\001', '(', '\013', '2', '!', '.', 'x', 'd', 's', '.', 
'c', 'o', 'r', 'e', '.', 'v', '3', '.', 'T', 'y', 'p', 'e', 'd', 'E', 'x', 't', 'e', 'n', 's', 'i', 'o', 'n', 'C', 'o', 'n', 
'f', 'i', 'g', 'H', '\000', 'R', '\013', 'c', 'u', 's', 't', 'o', 'm', 'M', 'a', 't', 'c', 'h', 'B', '\016', '\n', '\007', 'm', 'a', 't', 
'c', 'h', 'e', 'r', '\022', '\003', '\370', 'B', '\001', '\032', 'k', '\n', '\r', 'P', 'r', 'e', 'd', 'i', 'c', 'a', 't', 'e', 'L', 'i', 's', 
't', '\022', 'Z', '\n', '\t', 'p', 'r', 'e', 'd', 'i', 'c', 'a', 't', 'e', '\030', '\001', ' ', '\003', '(', '\013', '2', '2', '.', 'x', 'd', 
's', '.', 't', 'y', 'p', 'e', '.', 'm', 'a', 't', 'c', 'h', 'e', 'r', '.', 'v', '3', '.', 'M', 'a', 't', 'c', 'h', 'e', 'r', 
'.', 'M', 'a', 't', 'c', 'h', 'e', 'r', 'L', 'i', 's', 't', '.', 'P', 'r', 'e', 'd', 'i', 'c', 'a', 't', 'e', 'B', '\010', '\372', 
'B', '\005', '\222', '\001', '\002', '\010', '\002', 'R', '\t', 'p', 'r', 'e', 'd', 'i', 'c', 'a', 't', 'e', 'B', '\021', '\n', '\n', 'm', 'a', 't', 
'c', 'h', '_', 't', 'y', 'p', 'e', '\022', '\003', '\370', 'B', '\001', '\032', '\265', '\001', '\n', '\014', 'F', 'i', 'e', 'l', 'd', 'M', 'a', 't', 
'c', 'h', 'e', 'r', '\022', 'Z', '\n', '\t', 'p', 'r', 'e', 'd', 'i', 'c', 'a', 't', 'e', '\030', '\001', ' ', '\001', '(', '\013', '2', '2', 
'.', 'x', 'd', 's', '.', 't', 'y', 'p', 'e', '.', 'm', 'a', 't', 'c', 'h', 'e', 'r', '.', 'v', '3', '.', 'M', 'a', 't', 'c', 
'h', 'e', 'r', '.', 'M', 'a', 't', 'c', 'h', 'e', 'r', 'L', 'i', 's', 't', '.', 'P', 'r', 'e', 'd', 'i', 'c', 'a', 't', 'e', 
'B', '\010', '\372', 'B', '\005', '\212', '\001', '\002', '\020', '\001', 'R', '\t', 'p', 'r', 'e', 'd', 'i', 'c', 'a', 't', 'e', '\022', 'I', '\n', '\010', 
'o', 'n', '_', 'm', 'a', 't', 'c', 'h', '\030', '\002', ' ', '\001', '(', '\013', '2', '$', '.', 'x', 'd', 's', '.', 't', 'y', 'p', 'e', 
'.', 'm', 'a', 't', 'c', 'h', 'e', 'r', '.', 'v', '3', '.', 'M', 'a', 't', 'c', 'h', 'e', 'r', '.', 'O', 'n', 'M', 'a', 't', 
'c', 'h', 'B', '\010', '\372', 'B', '\005', '\212', '\001', '\002', '\020', '\001', 'R', '\007', 'o', 'n', 'M', 'a', 't', 'c', 'h', '\032', '\251', '\004', '\n', 
'\013', 'M', 'a', 't', 'c', 'h', 'e', 'r', 'T', 'r', 'e', 'e', '\022', 'A', '\n', '\005', 'i', 'n', 'p', 'u', 't', '\030', '\001', ' ', '\001', 
'(', '\013', '2', '!', '.', 'x', 'd', 's', '.', 'c', 'o', 'r', 'e', '.', 'v', '3', '.', 'T', 'y', 'p', 'e', 'd', 'E', 'x', 't', 
'e', 'n', 's', 'i', 'o', 'n', 'C', 'o', 'n', 'f', 'i', 'g', 'B', '\010', '\372', 'B', '\005', '\212', '\001', '\002', '\020', '\001', 'R', '\005', 'i', 
'n', 'p', 'u', 't', '\022', '[', '\n', '\017', 'e', 'x', 'a', 'c', 't', '_', 'm', 'a', 't', 'c', 'h', '_', 'm', 'a', 'p', '\030', '\002', 
' ', '\001', '(', '\013', '2', '1', '.', 'x', 'd', 's', '.', 't', 'y', 'p', 'e', '.', 'm', 'a', 't', 'c', 'h', 'e', 'r', '.', 'v', 
'3', '.', 'M', 'a', 't', 'c', 'h', 'e', 'r', '.', 'M', 'a', 't', 'c', 'h', 'e', 'r', 'T', 'r', 'e', 'e', '.', 'M', 'a', 't', 
'c', 'h', 'M', 'a', 'p', 'H', '\000', 'R', '\r', 'e', 'x', 'a', 'c', 't', 'M', 'a', 't', 'c', 'h', 'M', 'a', 'p', '\022', ']', '\n', 
'\020', 'p', 'r', 'e', 'f', 'i', 'x', '_', 'm', 'a', 't', 'c', 'h', '_', 'm', 'a', 'p', '\030', '\003', ' ', '\001', '(', '\013', '2', '1', 
'.', 'x', 'd', 's', '.', 't', 'y', 'p', 'e', '.', 'm', 'a', 't', 'c', 'h', 'e', 'r', '.', 'v', '3', '.', 'M', 'a', 't', 'c', 
'h', 'e', 'r', '.', 'M', 'a', 't', 'c', 'h', 'e', 'r', 'T', 'r', 'e', 'e', '.', 'M', 'a', 't', 'c', 'h', 'M', 'a', 'p', 'H', 
'\000', 'R', '\016', 'p', 'r', 'e', 'f', 'i', 'x', 'M', 'a', 't', 'c', 'h', 'M', 'a', 'p', '\022', 'F', '\n', '\014', 'c', 'u', 's', 't', 
'o', 'm', '_', 'm', 'a', 't', 'c', 'h', '\030', '\004', ' ', '\001', '(', '\013', '2', '!', '.', 'x', 'd', 's', '.', 'c', 'o', 'r', 'e', 
'.', 'v', '3', '.', 'T', 'y', 'p', 'e', 'd', 'E', 'x', 't', 'e', 'n', 's', 'i', 'o', 'n', 'C', 'o', 'n', 'f', 'i', 'g', 'H', 
'\000', 'R', '\013', 'c', 'u', 's', 't', 'o', 'm', 'M', 'a', 't', 'c', 'h', '\032', '\300', '\001', '\n', '\010', 'M', 'a', 't', 'c', 'h', 'M', 
'a', 'p', '\022', 'V', '\n', '\003', 'm', 'a', 'p', '\030', '\001', ' ', '\003', '(', '\013', '2', ':', '.', 'x', 'd', 's', '.', 't', 'y', 'p', 
'e', '.', 'm', 'a', 't', 'c', 'h', 'e', 'r', '.', 'v', '3', '.', 'M', 'a', 't', 'c', 'h', 'e', 'r', '.', 'M', 'a', 't', 'c', 
'h', 'e', 'r', 'T', 'r', 'e', 'e', '.', 'M', 'a', 't', 'c', 'h', 'M', 'a', 'p', '.', 'M', 'a', 'p', 'E', 'n', 't', 'r', 'y', 
'B', '\010', '\372', 'B', '\005', '\232', '\001', '\002', '\010', '\001', 'R', '\003', 'm', 'a', 'p', '\032', '\\', '\n', '\010', 'M', 'a', 'p', 'E', 'n', 't', 
'r', 'y', '\022', '\020', '\n', '\003', 'k', 'e', 'y', '\030', '\001', ' ', '\001', '(', '\t', 'R', '\003', 'k', 'e', 'y', '\022', ':', '\n', '\005', 'v', 
'a', 'l', 'u', 'e', '\030', '\002', ' ', '\001', '(', '\013', '2', '$', '.', 'x', 'd', 's', '.', 't', 'y', 'p', 'e', '.', 'm', 'a', 't', 
'c', 'h', 'e', 'r', '.', 'v', '3', '.', 'M', 'a', 't', 'c', 'h', 'e', 'r', '.', 'O', 'n', 'M', 'a', 't', 'c', 'h', 'R', '\005', 
'v', 'a', 'l', 'u', 'e', ':', '\002', '8', '\001', 'B', '\020', '\n', '\t', 't', 'r', 'e', 'e', '_', 't', 'y', 'p', 'e', '\022', '\003', '\370', 
'B', '\001', ':', '\010', '\322', '\306', '\244', '\341', '\006', '\002', '\010', '\001', 'B', '\016', '\n', '\014', 'm', 'a', 't', 'c', 'h', 'e', 'r', '_', 't', 
'y', 'p', 'e', 'B', '\\', '\n', '\036', 'c', 'o', 'm', '.', 'g', 'i', 't', 'h', 'u', 'b', '.', 'x', 'd', 's', '.', 't', 'y', 'p', 
'e', '.', 'm', 'a', 't', 'c', 'h', 'e', 'r', '.', 'v', '3', 'B', '\014', 'M', 'a', 't', 'c', 'h', 'e', 'r', 'P', 'r', 'o', 't', 
'o', 'P', '\001', 'Z', '*', 'g', 'i', 't', 'h', 'u', 'b', '.', 'c', 'o', 'm', '/', 'c', 'n', 'c', 'f', '/', 'x', 'd', 's', '/', 
'g', 'o', '/', 'x', 'd', 's', '/', 't', 'y', 'p', 'e', '/', 'm', 'a', 't', 'c', 'h', 'e', 'r', '/', 'v', '3', 'b', '\006', 'p', 
'r', 'o', 't', 'o', '3', 
};

static _upb_DefPool_Init *deps[5] = {
  &xds_annotations_v3_status_proto_upbdefinit,
  &xds_core_v3_extension_proto_upbdefinit,
  &xds_type_matcher_v3_string_proto_upbdefinit,
  &validate_validate_proto_upbdefinit,
  NULL
};

_upb_DefPool_Init xds_type_matcher_v3_matcher_proto_upbdefinit = {
  deps,
  &xds_type_matcher_v3_matcher_proto_upb_file_layout,
  "xds/type/matcher/v3/matcher.proto",
  UPB_STRINGVIEW_INIT(descriptor, 2330)
};
