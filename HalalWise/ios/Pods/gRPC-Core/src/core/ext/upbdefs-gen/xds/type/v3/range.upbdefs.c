/* This file was generated by upb_generator from the input file:
 *
 *     xds/type/v3/range.proto
 *
 * Do not edit -- your changes will be discarded when the file is
 * regenerated. */

#include "upb/reflection/def.h"
#include "xds/type/v3/range.upbdefs.h"
#include "xds/type/v3/range.upb_minitable.h"

static const char descriptor[285] = {'\n', '\027', 'x', 'd', 's', '/', 't', 'y', 'p', 'e', '/', 'v', '3', '/', 'r', 'a', 'n', 'g', 'e', '.', 'p', 'r', 'o', 't', 'o', 
'\022', '\013', 'x', 'd', 's', '.', 't', 'y', 'p', 'e', '.', 'v', '3', '\"', '4', '\n', '\n', 'I', 'n', 't', '6', '4', 'R', 'a', 'n', 
'g', 'e', '\022', '\024', '\n', '\005', 's', 't', 'a', 'r', 't', '\030', '\001', ' ', '\001', '(', '\003', 'R', '\005', 's', 't', 'a', 'r', 't', '\022', 
'\020', '\n', '\003', 'e', 'n', 'd', '\030', '\002', ' ', '\001', '(', '\003', 'R', '\003', 'e', 'n', 'd', '\"', '4', '\n', '\n', 'I', 'n', 't', '3', 
'2', 'R', 'a', 'n', 'g', 'e', '\022', '\024', '\n', '\005', 's', 't', 'a', 'r', 't', '\030', '\001', ' ', '\001', '(', '\005', 'R', '\005', 's', 't', 
'a', 'r', 't', '\022', '\020', '\n', '\003', 'e', 'n', 'd', '\030', '\002', ' ', '\001', '(', '\005', 'R', '\003', 'e', 'n', 'd', '\"', '5', '\n', '\013', 
'D', 'o', 'u', 'b', 'l', 'e', 'R', 'a', 'n', 'g', 'e', '\022', '\024', '\n', '\005', 's', 't', 'a', 'r', 't', '\030', '\001', ' ', '\001', '(', 
'\001', 'R', '\005', 's', 't', 'a', 'r', 't', '\022', '\020', '\n', '\003', 'e', 'n', 'd', '\030', '\002', ' ', '\001', '(', '\001', 'R', '\003', 'e', 'n', 
'd', 'B', 'J', '\n', '\026', 'c', 'o', 'm', '.', 'g', 'i', 't', 'h', 'u', 'b', '.', 'x', 'd', 's', '.', 't', 'y', 'p', 'e', '.', 
'v', '3', 'B', '\n', 'R', 'a', 'n', 'g', 'e', 'P', 'r', 'o', 't', 'o', 'P', '\001', 'Z', '\"', 'g', 'i', 't', 'h', 'u', 'b', '.', 
'c', 'o', 'm', '/', 'c', 'n', 'c', 'f', '/', 'x', 'd', 's', '/', 'g', 'o', '/', 'x', 'd', 's', '/', 't', 'y', 'p', 'e', '/', 
'v', '3', 'b', '\006', 'p', 'r', 'o', 't', 'o', '3', 
};

static _upb_DefPool_Init *deps[1] = {
  NULL
};

_upb_DefPool_Init xds_type_v3_range_proto_upbdefinit = {
  deps,
  &xds_type_v3_range_proto_upb_file_layout,
  "xds/type/v3/range.proto",
  UPB_STRINGVIEW_INIT(descriptor, 285)
};
