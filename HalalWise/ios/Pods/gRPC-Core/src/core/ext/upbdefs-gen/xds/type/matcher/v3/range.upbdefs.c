/* This file was generated by upb_generator from the input file:
 *
 *     xds/type/matcher/v3/range.proto
 *
 * Do not edit -- your changes will be discarded when the file is
 * regenerated. */

#include "upb/reflection/def.h"
#include "xds/type/matcher/v3/range.upbdefs.h"
#include "xds/type/matcher/v3/range.upb_minitable.h"

extern _upb_DefPool_Init xds_type_v3_range_proto_upbdefinit;
extern _upb_DefPool_Init xds_type_matcher_v3_matcher_proto_upbdefinit;
extern _upb_DefPool_Init validate_validate_proto_upbdefinit;
static const char descriptor[1007] = {'\n', '\037', 'x', 'd', 's', '/', 't', 'y', 'p', 'e', '/', 'm', 'a', 't', 'c', 'h', 'e', 'r', '/', 'v', '3', '/', 'r', 'a', 'n', 
'g', 'e', '.', 'p', 'r', 'o', 't', 'o', '\022', '\023', 'x', 'd', 's', '.', 't', 'y', 'p', 'e', '.', 'm', 'a', 't', 'c', 'h', 'e', 
'r', '.', 'v', '3', '\032', '\027', 'x', 'd', 's', '/', 't', 'y', 'p', 'e', '/', 'v', '3', '/', 'r', 'a', 'n', 'g', 'e', '.', 'p', 
'r', 'o', 't', 'o', '\032', '!', 'x', 'd', 's', '/', 't', 'y', 'p', 'e', '/', 'm', 'a', 't', 'c', 'h', 'e', 'r', '/', 'v', '3', 
'/', 'm', 'a', 't', 'c', 'h', 'e', 'r', '.', 'p', 'r', 'o', 't', 'o', '\032', '\027', 'v', 'a', 'l', 'i', 'd', 'a', 't', 'e', '/', 
'v', 'a', 'l', 'i', 'd', 'a', 't', 'e', '.', 'p', 'r', 'o', 't', 'o', '\"', '\374', '\001', '\n', '\021', 'I', 'n', 't', '6', '4', 'R', 
'a', 'n', 'g', 'e', 'M', 'a', 't', 'c', 'h', 'e', 'r', '\022', 'Z', '\n', '\016', 'r', 'a', 'n', 'g', 'e', '_', 'm', 'a', 't', 'c', 
'h', 'e', 'r', 's', '\030', '\001', ' ', '\003', '(', '\013', '2', '3', '.', 'x', 'd', 's', '.', 't', 'y', 'p', 'e', '.', 'm', 'a', 't', 
'c', 'h', 'e', 'r', '.', 'v', '3', '.', 'I', 'n', 't', '6', '4', 'R', 'a', 'n', 'g', 'e', 'M', 'a', 't', 'c', 'h', 'e', 'r', 
'.', 'R', 'a', 'n', 'g', 'e', 'M', 'a', 't', 'c', 'h', 'e', 'r', 'R', '\r', 'r', 'a', 'n', 'g', 'e', 'M', 'a', 't', 'c', 'h', 
'e', 'r', 's', '\032', '\212', '\001', '\n', '\014', 'R', 'a', 'n', 'g', 'e', 'M', 'a', 't', 'c', 'h', 'e', 'r', '\022', '9', '\n', '\006', 'r', 
'a', 'n', 'g', 'e', 's', '\030', '\001', ' ', '\003', '(', '\013', '2', '\027', '.', 'x', 'd', 's', '.', 't', 'y', 'p', 'e', '.', 'v', '3', 
'.', 'I', 'n', 't', '6', '4', 'R', 'a', 'n', 'g', 'e', 'B', '\010', '\372', 'B', '\005', '\222', '\001', '\002', '\010', '\001', 'R', '\006', 'r', 'a', 
'n', 'g', 'e', 's', '\022', '?', '\n', '\010', 'o', 'n', '_', 'm', 'a', 't', 'c', 'h', '\030', '\002', ' ', '\001', '(', '\013', '2', '$', '.', 
'x', 'd', 's', '.', 't', 'y', 'p', 'e', '.', 'm', 'a', 't', 'c', 'h', 'e', 'r', '.', 'v', '3', '.', 'M', 'a', 't', 'c', 'h', 
'e', 'r', '.', 'O', 'n', 'M', 'a', 't', 'c', 'h', 'R', '\007', 'o', 'n', 'M', 'a', 't', 'c', 'h', '\"', '\374', '\001', '\n', '\021', 'I', 
'n', 't', '3', '2', 'R', 'a', 'n', 'g', 'e', 'M', 'a', 't', 'c', 'h', 'e', 'r', '\022', 'Z', '\n', '\016', 'r', 'a', 'n', 'g', 'e', 
'_', 'm', 'a', 't', 'c', 'h', 'e', 'r', 's', '\030', '\001', ' ', '\003', '(', '\013', '2', '3', '.', 'x', 'd', 's', '.', 't', 'y', 'p', 
'e', '.', 'm', 'a', 't', 'c', 'h', 'e', 'r', '.', 'v', '3', '.', 'I', 'n', 't', '3', '2', 'R', 'a', 'n', 'g', 'e', 'M', 'a', 
't', 'c', 'h', 'e', 'r', '.', 'R', 'a', 'n', 'g', 'e', 'M', 'a', 't', 'c', 'h', 'e', 'r', 'R', '\r', 'r', 'a', 'n', 'g', 'e', 
'M', 'a', 't', 'c', 'h', 'e', 'r', 's', '\032', '\212', '\001', '\n', '\014', 'R', 'a', 'n', 'g', 'e', 'M', 'a', 't', 'c', 'h', 'e', 'r', 
'\022', '9', '\n', '\006', 'r', 'a', 'n', 'g', 'e', 's', '\030', '\001', ' ', '\003', '(', '\013', '2', '\027', '.', 'x', 'd', 's', '.', 't', 'y', 
'p', 'e', '.', 'v', '3', '.', 'I', 'n', 't', '3', '2', 'R', 'a', 'n', 'g', 'e', 'B', '\010', '\372', 'B', '\005', '\222', '\001', '\002', '\010', 
'\001', 'R', '\006', 'r', 'a', 'n', 'g', 'e', 's', '\022', '?', '\n', '\010', 'o', 'n', '_', 'm', 'a', 't', 'c', 'h', '\030', '\002', ' ', '\001', 
'(', '\013', '2', '$', '.', 'x', 'd', 's', '.', 't', 'y', 'p', 'e', '.', 'm', 'a', 't', 'c', 'h', 'e', 'r', '.', 'v', '3', '.', 
'M', 'a', 't', 'c', 'h', 'e', 'r', '.', 'O', 'n', 'M', 'a', 't', 'c', 'h', 'R', '\007', 'o', 'n', 'M', 'a', 't', 'c', 'h', '\"', 
'\377', '\001', '\n', '\022', 'D', 'o', 'u', 'b', 'l', 'e', 'R', 'a', 'n', 'g', 'e', 'M', 'a', 't', 'c', 'h', 'e', 'r', '\022', '[', '\n', 
'\016', 'r', 'a', 'n', 'g', 'e', '_', 'm', 'a', 't', 'c', 'h', 'e', 'r', 's', '\030', '\001', ' ', '\003', '(', '\013', '2', '4', '.', 'x', 
'd', 's', '.', 't', 'y', 'p', 'e', '.', 'm', 'a', 't', 'c', 'h', 'e', 'r', '.', 'v', '3', '.', 'D', 'o', 'u', 'b', 'l', 'e', 
'R', 'a', 'n', 'g', 'e', 'M', 'a', 't', 'c', 'h', 'e', 'r', '.', 'R', 'a', 'n', 'g', 'e', 'M', 'a', 't', 'c', 'h', 'e', 'r', 
'R', '\r', 'r', 'a', 'n', 'g', 'e', 'M', 'a', 't', 'c', 'h', 'e', 'r', 's', '\032', '\213', '\001', '\n', '\014', 'R', 'a', 'n', 'g', 'e', 
'M', 'a', 't', 'c', 'h', 'e', 'r', '\022', ':', '\n', '\006', 'r', 'a', 'n', 'g', 'e', 's', '\030', '\001', ' ', '\003', '(', '\013', '2', '\030', 
'.', 'x', 'd', 's', '.', 't', 'y', 'p', 'e', '.', 'v', '3', '.', 'D', 'o', 'u', 'b', 'l', 'e', 'R', 'a', 'n', 'g', 'e', 'B', 
'\010', '\372', 'B', '\005', '\222', '\001', '\002', '\010', '\001', 'R', '\006', 'r', 'a', 'n', 'g', 'e', 's', '\022', '?', '\n', '\010', 'o', 'n', '_', 'm', 
'a', 't', 'c', 'h', '\030', '\002', ' ', '\001', '(', '\013', '2', '$', '.', 'x', 'd', 's', '.', 't', 'y', 'p', 'e', '.', 'm', 'a', 't', 
'c', 'h', 'e', 'r', '.', 'v', '3', '.', 'M', 'a', 't', 'c', 'h', 'e', 'r', '.', 'O', 'n', 'M', 'a', 't', 'c', 'h', 'R', '\007', 
'o', 'n', 'M', 'a', 't', 'c', 'h', 'B', 'Z', '\n', '\036', 'c', 'o', 'm', '.', 'g', 'i', 't', 'h', 'u', 'b', '.', 'x', 'd', 's', 
'.', 't', 'y', 'p', 'e', '.', 'm', 'a', 't', 'c', 'h', 'e', 'r', '.', 'v', '3', 'B', '\n', 'R', 'a', 'n', 'g', 'e', 'P', 'r', 
'o', 't', 'o', 'P', '\001', 'Z', '*', 'g', 'i', 't', 'h', 'u', 'b', '.', 'c', 'o', 'm', '/', 'c', 'n', 'c', 'f', '/', 'x', 'd', 
's', '/', 'g', 'o', '/', 'x', 'd', 's', '/', 't', 'y', 'p', 'e', '/', 'm', 'a', 't', 'c', 'h', 'e', 'r', '/', 'v', '3', 'b', 
'\006', 'p', 'r', 'o', 't', 'o', '3', 
};

static _upb_DefPool_Init *deps[4] = {
  &xds_type_v3_range_proto_upbdefinit,
  &xds_type_matcher_v3_matcher_proto_upbdefinit,
  &validate_validate_proto_upbdefinit,
  NULL
};

_upb_DefPool_Init xds_type_matcher_v3_range_proto_upbdefinit = {
  deps,
  &xds_type_matcher_v3_range_proto_upb_file_layout,
  "xds/type/matcher/v3/range.proto",
  UPB_STRINGVIEW_INIT(descriptor, 1007)
};
