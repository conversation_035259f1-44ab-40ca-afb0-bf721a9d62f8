/* This file was generated by upb_generator from the input file:
 *
 *     xds/type/v3/typed_struct.proto
 *
 * Do not edit -- your changes will be discarded when the file is
 * regenerated. */

#include "upb/reflection/def.h"
#include "xds/type/v3/typed_struct.upbdefs.h"
#include "xds/type/v3/typed_struct.upb_minitable.h"

extern _upb_DefPool_Init google_protobuf_struct_proto_upbdefinit;
static const char descriptor[254] = {'\n', '\036', 'x', 'd', 's', '/', 't', 'y', 'p', 'e', '/', 'v', '3', '/', 't', 'y', 'p', 'e', 'd', '_', 's', 't', 'r', 'u', 'c', 
't', '.', 'p', 'r', 'o', 't', 'o', '\022', '\013', 'x', 'd', 's', '.', 't', 'y', 'p', 'e', '.', 'v', '3', '\032', '\034', 'g', 'o', 'o', 
'g', 'l', 'e', '/', 'p', 'r', 'o', 't', 'o', 'b', 'u', 'f', '/', 's', 't', 'r', 'u', 'c', 't', '.', 'p', 'r', 'o', 't', 'o', 
'\"', 'W', '\n', '\013', 'T', 'y', 'p', 'e', 'd', 'S', 't', 'r', 'u', 'c', 't', '\022', '\031', '\n', '\010', 't', 'y', 'p', 'e', '_', 'u', 
'r', 'l', '\030', '\001', ' ', '\001', '(', '\t', 'R', '\007', 't', 'y', 'p', 'e', 'U', 'r', 'l', '\022', '-', '\n', '\005', 'v', 'a', 'l', 'u', 
'e', '\030', '\002', ' ', '\001', '(', '\013', '2', '\027', '.', 'g', 'o', 'o', 'g', 'l', 'e', '.', 'p', 'r', 'o', 't', 'o', 'b', 'u', 'f', 
'.', 'S', 't', 'r', 'u', 'c', 't', 'R', '\005', 'v', 'a', 'l', 'u', 'e', 'B', 'P', '\n', '\026', 'c', 'o', 'm', '.', 'g', 'i', 't', 
'h', 'u', 'b', '.', 'x', 'd', 's', '.', 't', 'y', 'p', 'e', '.', 'v', '3', 'B', '\020', 'T', 'y', 'p', 'e', 'd', 'S', 't', 'r', 
'u', 'c', 't', 'P', 'r', 'o', 't', 'o', 'P', '\001', 'Z', '\"', 'g', 'i', 't', 'h', 'u', 'b', '.', 'c', 'o', 'm', '/', 'c', 'n', 
'c', 'f', '/', 'x', 'd', 's', '/', 'g', 'o', '/', 'x', 'd', 's', '/', 't', 'y', 'p', 'e', '/', 'v', '3', 'b', '\006', 'p', 'r', 
'o', 't', 'o', '3', 
};

static _upb_DefPool_Init *deps[2] = {
  &google_protobuf_struct_proto_upbdefinit,
  NULL
};

_upb_DefPool_Init xds_type_v3_typed_struct_proto_upbdefinit = {
  deps,
  &xds_type_v3_typed_struct_proto_upb_file_layout,
  "xds/type/v3/typed_struct.proto",
  UPB_STRINGVIEW_INIT(descriptor, 254)
};
