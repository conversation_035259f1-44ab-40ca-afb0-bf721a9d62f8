import 'app_localizations.dart';

// Urdu Localizations
class AppLocalizationsUr extends AppLocalizations {
  @override
  String get appName => 'حلال وائز';
  @override
  String get appTagline => 'ذہین حلال کھانے کی تصدیق';

  @override
  String get home => 'ہوم';
  @override
  String get scanner => 'اسکینر';
  @override
  String get history => 'تاریخ';
  @override
  String get settings => 'سیٹنگز';

  @override
  String get scanLabel => 'لیبل اسکین کریں';
  @override
  String get manualInput => 'دستی ان پٹ';
  @override
  String get voiceInput => 'آواز کا ان پٹ';
  @override
  String get scanBarcode => 'بارکوڈ اسکین کریں';
  @override
  String get takePhoto => 'تصویر لیں';
  @override
  String get selectFromGallery => 'گیلری سے منتخب کریں';
  @override
  String get comingSoon => 'جلد آرہا ہے';

  @override
  String get halalStatus => 'حلال کی صورتحال';
  @override
  String get halal => 'حلال';
  @override
  String get haram => 'حرام';
  @override
  String get questionable => 'مشکوک';
  @override
  String get uncertain => 'غیر یقینی';
  @override
  String get healthScore => 'صحت کا اسکور';
  @override
  String get ingredients => 'اجزاء';
  @override
  String get nutritionalInfo => 'غذائی معلومات';
  @override
  String get allergens => 'الرجی کا باعث';

  @override
  String get scanHistory => 'اسکین کی تاریخ';
  @override
  String get noHistoryYet => 'ابھی تک کوئی تاریخ نہیں';
  @override
  String get clearHistory => 'تاریخ صاف کریں';

  @override
  String get language => 'زبان';
  @override
  String get theme => 'تھیم';
  @override
  String get notifications => 'اطلاعات';

  @override
  String get about => 'کے بارے میں';
  @override
  String get privacyPolicy => 'رازداری کی پالیسی';
  @override
  String get termsOfService => 'خدمات کی شرائط';
  @override
  String get support => 'سپورٹ';

  @override
  String get save => 'محفوظ کریں';
  @override
  String get cancel => 'منسوخ کریں';
  @override
  String get ok => 'ٹھیک ہے';
  @override
  String get error => 'خرابی';
  @override
  String get loading => 'لوڈ ہو رہا ہے...';
  @override
  String get retry => 'دوبارہ کوشش کریں';
  @override
  String get success => 'کامیابی';

  @override
  String get scanningInProgress => 'اسکیننگ جاری ہے...';
  @override
  String get analysisComplete => 'تجزیہ مکمل';
  @override
  String get noInternetConnection => 'انٹرنیٹ کنکشن نہیں';
  @override
  String get apiKeyRequired => 'API کلید درکار';
  @override
  String get dailyLimitReached => 'یومیہ حد پہنچ گئی';
}
