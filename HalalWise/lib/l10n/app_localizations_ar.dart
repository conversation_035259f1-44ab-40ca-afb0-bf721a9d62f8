import 'app_localizations.dart';

// Arabic Localizations
class AppLocalizationsAr extends AppLocalizations {
  @override
  String get appName => 'حلال وايز';
  @override
  String get appTagline => 'التحقق الذكي من الطعام الحلال';

  @override
  String get home => 'الرئيسية';
  @override
  String get scanner => 'الماسح';
  @override
  String get history => 'التاريخ';
  @override
  String get settings => 'الإعدادات';

  @override
  String get scanLabel => 'مسح الملصق';
  @override
  String get manualInput => 'إدخال يدوي';
  @override
  String get voiceInput => 'إدخال صوتي';
  @override
  String get scanBarcode => 'مسح الباركود';
  @override
  String get takePhoto => 'التقاط صورة';
  @override
  String get selectFromGallery => 'اختيار من المعرض';
  @override
  String get comingSoon => 'قريباً';

  @override
  String get halalStatus => 'حالة الحلال';
  @override
  String get halal => 'حلال';
  @override
  String get haram => 'حرام';
  @override
  String get questionable => 'مشكوك فيه';
  @override
  String get uncertain => 'غير مؤكد';
  @override
  String get healthScore => 'نقاط الصحة';
  @override
  String get ingredients => 'المكونات';
  @override
  String get nutritionalInfo => 'المعلومات الغذائية';
  @override
  String get allergens => 'مسببات الحساسية';

  @override
  String get scanHistory => 'تاريخ المسح';
  @override
  String get noHistoryYet => 'لا يوجد تاريخ مسح بعد';
  @override
  String get clearHistory => 'مسح التاريخ';

  @override
  String get language => 'اللغة';
  @override
  String get theme => 'المظهر';
  @override
  String get notifications => 'الإشعارات';
  @override
  String get apiUsage => 'استخدام API';
  @override
  String get about => 'حول';
  @override
  String get privacyPolicy => 'سياسة الخصوصية';
  @override
  String get termsOfService => 'شروط الخدمة';
  @override
  String get support => 'الدعم';

  @override
  String get save => 'حفظ';
  @override
  String get cancel => 'إلغاء';
  @override
  String get ok => 'موافق';
  @override
  String get error => 'خطأ';
  @override
  String get loading => 'جاري التحميل...';
  @override
  String get retry => 'إعادة المحاولة';
  @override
  String get success => 'نجح';

  @override
  String get scanningInProgress => 'المسح قيد التقدم...';
  @override
  String get analysisComplete => 'اكتمل التحليل';
  @override
  String get noInternetConnection => 'لا يوجد اتصال بالإنترنت';
  @override
  String get apiKeyRequired => 'مطلوب مفتاح API';
  @override
  String get dailyLimitReached => 'تم الوصول للحد اليومي';
}
