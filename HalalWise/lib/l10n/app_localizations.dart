import 'package:flutter/material.dart';
import 'app_localizations_ar.dart';
import 'app_localizations_ur.dart';

abstract class AppLocalizations {
  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations) ?? AppLocalizationsEn();
  }

  static const List<Locale> supportedLocales = [
    Locale('en', 'US'), // English
    Locale('ar', 'SA'), // Arabic
    Locale('ur', 'PK'), // Urdu
    Locale('tr', 'TR'), // Turkish
    Locale('ms', 'MY'), // Malay
    Locale('id', 'ID'), // Indonesian
    Locale('fr', 'FR'), // French
  ];

  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = [
    AppLocalizationsDelegate(),
    DefaultMaterialLocalizations.delegate,
    DefaultWidgetsLocalizations.delegate,
  ];

  // App Name
  String get appName;
  String get appTagline;

  // Navigation
  String get home;
  String get scanner;
  String get history;
  String get settings;

  // Scanner
  String get scanLabel;
  String get manualInput;
  String get voiceInput;
  String get scanBarcode;
  String get takePhoto;
  String get selectFromGallery;
  String get comingSoon;

  // Food Analysis
  String get halalStatus;
  String get halal;
  String get haram;
  String get questionable;
  String get uncertain;
  String get healthScore;
  String get ingredients;
  String get nutritionalInfo;
  String get allergens;

  // History
  String get scanHistory;
  String get noHistoryYet;
  String get clearHistory;

  // Settings
  String get language;
  String get theme;
  String get notifications;

  String get about;
  String get privacyPolicy;
  String get termsOfService;
  String get support;

  // Common
  String get save;
  String get cancel;
  String get ok;
  String get error;
  String get loading;
  String get retry;
  String get success;

  // Messages
  String get scanningInProgress;
  String get analysisComplete;
  String get noInternetConnection;
  String get apiKeyRequired;
  String get dailyLimitReached;
}

class AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return AppLocalizations.supportedLocales
        .any((supportedLocale) => supportedLocale.languageCode == locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    switch (locale.languageCode) {
      case 'ar':
        return AppLocalizationsAr();
      case 'ur':
        return AppLocalizationsUr();
      case 'tr':
        return AppLocalizationsTr();
      case 'ms':
        return AppLocalizationsMs();
      case 'id':
        return AppLocalizationsId();
      case 'fr':
        return AppLocalizationsFr();
      default:
        return AppLocalizationsEn();
    }
  }

  @override
  bool shouldReload(AppLocalizationsDelegate old) => false;
}

// English (Default)
class AppLocalizationsEn extends AppLocalizations {
  @override
  String get appName => 'HalalWise';
  @override
  String get appTagline => 'Smart Halal Food Verification';

  @override
  String get home => 'Home';
  @override
  String get scanner => 'Scanner';
  @override
  String get history => 'History';
  @override
  String get settings => 'Settings';

  @override
  String get scanLabel => 'Scan Label';
  @override
  String get manualInput => 'Manual Input';
  @override
  String get voiceInput => 'Voice Input';
  @override
  String get scanBarcode => 'Scan Barcode';
  @override
  String get takePhoto => 'Take Photo';
  @override
  String get selectFromGallery => 'Select from Gallery';
  @override
  String get comingSoon => 'Coming Soon';

  @override
  String get halalStatus => 'Halal Status';
  @override
  String get halal => 'Halal';
  @override
  String get haram => 'Haram';
  @override
  String get questionable => 'Questionable';
  @override
  String get uncertain => 'Uncertain';
  @override
  String get healthScore => 'Health Score';
  @override
  String get ingredients => 'Ingredients';
  @override
  String get nutritionalInfo => 'Nutritional Info';
  @override
  String get allergens => 'Allergens';

  @override
  String get scanHistory => 'Scan History';
  @override
  String get noHistoryYet => 'No scan history yet';
  @override
  String get clearHistory => 'Clear History';

  @override
  String get language => 'Language';
  @override
  String get theme => 'Theme';
  @override
  String get notifications => 'Notifications';

  @override
  String get about => 'About';
  @override
  String get privacyPolicy => 'Privacy Policy';
  @override
  String get termsOfService => 'Terms of Service';
  @override
  String get support => 'Support';

  @override
  String get save => 'Save';
  @override
  String get cancel => 'Cancel';
  @override
  String get ok => 'OK';
  @override
  String get error => 'Error';
  @override
  String get loading => 'Loading...';
  @override
  String get retry => 'Retry';
  @override
  String get success => 'Success';

  @override
  String get scanningInProgress => 'Scanning in progress...';
  @override
  String get analysisComplete => 'Analysis complete';
  @override
  String get noInternetConnection => 'No internet connection';
  @override
  String get apiKeyRequired => 'API key required';
  @override
  String get dailyLimitReached => 'Daily limit reached';
}

// Turkish
class AppLocalizationsTr extends AppLocalizations {
  @override
  String get appName => 'HelalWise';
  @override
  String get appTagline => 'Akıllı Helal Gıda Doğrulama';

  @override
  String get home => 'Ana Sayfa';
  @override
  String get scanner => 'Tarayıcı';
  @override
  String get history => 'Geçmiş';
  @override
  String get settings => 'Ayarlar';

  @override
  String get scanLabel => 'Etiket Tara';
  @override
  String get manualInput => 'Manuel Giriş';
  @override
  String get voiceInput => 'Sesli Giriş';
  @override
  String get scanBarcode => 'Barkod Tara';
  @override
  String get takePhoto => 'Fotoğraf Çek';
  @override
  String get selectFromGallery => 'Galeriden Seç';
  @override
  String get comingSoon => 'Yakında';

  @override
  String get halalStatus => 'Helal Durumu';
  @override
  String get halal => 'Helal';
  @override
  String get haram => 'Haram';
  @override
  String get questionable => 'Şüpheli';
  @override
  String get uncertain => 'Belirsiz';
  @override
  String get healthScore => 'Sağlık Puanı';
  @override
  String get ingredients => 'İçerikler';
  @override
  String get nutritionalInfo => 'Beslenme Bilgisi';
  @override
  String get allergens => 'Alerjenler';

  @override
  String get scanHistory => 'Tarama Geçmişi';
  @override
  String get noHistoryYet => 'Henüz geçmiş yok';
  @override
  String get clearHistory => 'Geçmişi Temizle';

  @override
  String get language => 'Dil';
  @override
  String get theme => 'Tema';
  @override
  String get notifications => 'Bildirimler';

  @override
  String get about => 'Hakkında';
  @override
  String get privacyPolicy => 'Gizlilik Politikası';
  @override
  String get termsOfService => 'Hizmet Şartları';
  @override
  String get support => 'Destek';

  @override
  String get save => 'Kaydet';
  @override
  String get cancel => 'İptal';
  @override
  String get ok => 'Tamam';
  @override
  String get error => 'Hata';
  @override
  String get loading => 'Yükleniyor...';
  @override
  String get retry => 'Tekrar Dene';
  @override
  String get success => 'Başarılı';

  @override
  String get scanningInProgress => 'Tarama devam ediyor...';
  @override
  String get analysisComplete => 'Analiz tamamlandı';
  @override
  String get noInternetConnection => 'İnternet bağlantısı yok';
  @override
  String get apiKeyRequired => 'API anahtarı gerekli';
  @override
  String get dailyLimitReached => 'Günlük limite ulaşıldı';
}

// Malay
class AppLocalizationsMs extends AppLocalizations {
  @override
  String get appName => 'HalalWise';
  @override
  String get appTagline => 'Pengesahan Makanan Halal Pintar';

  @override
  String get home => 'Utama';
  @override
  String get scanner => 'Pengimbas';
  @override
  String get history => 'Sejarah';
  @override
  String get settings => 'Tetapan';

  @override
  String get scanLabel => 'Imbas Label';
  @override
  String get manualInput => 'Input Manual';
  @override
  String get voiceInput => 'Input Suara';
  @override
  String get scanBarcode => 'Imbas Barcode';
  @override
  String get takePhoto => 'Ambil Foto';
  @override
  String get selectFromGallery => 'Pilih dari Galeri';
  @override
  String get comingSoon => 'Akan Datang';

  @override
  String get halalStatus => 'Status Halal';
  @override
  String get halal => 'Halal';
  @override
  String get haram => 'Haram';
  @override
  String get questionable => 'Meragukan';
  @override
  String get uncertain => 'Tidak Pasti';
  @override
  String get healthScore => 'Skor Kesihatan';
  @override
  String get ingredients => 'Ramuan';
  @override
  String get nutritionalInfo => 'Maklumat Pemakanan';
  @override
  String get allergens => 'Alergen';

  @override
  String get scanHistory => 'Sejarah Imbasan';
  @override
  String get noHistoryYet => 'Tiada sejarah lagi';
  @override
  String get clearHistory => 'Padam Sejarah';

  @override
  String get language => 'Bahasa';
  @override
  String get theme => 'Tema';
  @override
  String get notifications => 'Pemberitahuan';

  @override
  String get about => 'Tentang';
  @override
  String get privacyPolicy => 'Dasar Privasi';
  @override
  String get termsOfService => 'Terma Perkhidmatan';
  @override
  String get support => 'Sokongan';

  @override
  String get save => 'Simpan';
  @override
  String get cancel => 'Batal';
  @override
  String get ok => 'OK';
  @override
  String get error => 'Ralat';
  @override
  String get loading => 'Memuatkan...';
  @override
  String get retry => 'Cuba Lagi';
  @override
  String get success => 'Berjaya';

  @override
  String get scanningInProgress => 'Imbasan sedang berjalan...';
  @override
  String get analysisComplete => 'Analisis selesai';
  @override
  String get noInternetConnection => 'Tiada sambungan internet';
  @override
  String get apiKeyRequired => 'Kunci API diperlukan';
  @override
  String get dailyLimitReached => 'Had harian dicapai';
}

// Indonesian
class AppLocalizationsId extends AppLocalizations {
  @override
  String get appName => 'HalalWise';
  @override
  String get appTagline => 'Verifikasi Makanan Halal Cerdas';

  @override
  String get home => 'Beranda';
  @override
  String get scanner => 'Pemindai';
  @override
  String get history => 'Riwayat';
  @override
  String get settings => 'Pengaturan';

  @override
  String get scanLabel => 'Pindai Label';
  @override
  String get manualInput => 'Input Manual';
  @override
  String get voiceInput => 'Input Suara';
  @override
  String get scanBarcode => 'Pindai Barcode';
  @override
  String get takePhoto => 'Ambil Foto';
  @override
  String get selectFromGallery => 'Pilih dari Galeri';
  @override
  String get comingSoon => 'Segera Hadir';

  @override
  String get halalStatus => 'Status Halal';
  @override
  String get halal => 'Halal';
  @override
  String get haram => 'Haram';
  @override
  String get questionable => 'Meragukan';
  @override
  String get uncertain => 'Tidak Pasti';
  @override
  String get healthScore => 'Skor Kesehatan';
  @override
  String get ingredients => 'Bahan';
  @override
  String get nutritionalInfo => 'Info Nutrisi';
  @override
  String get allergens => 'Alergen';

  @override
  String get scanHistory => 'Riwayat Pemindaian';
  @override
  String get noHistoryYet => 'Belum ada riwayat';
  @override
  String get clearHistory => 'Hapus Riwayat';

  @override
  String get language => 'Bahasa';
  @override
  String get theme => 'Tema';
  @override
  String get notifications => 'Notifikasi';

  @override
  String get about => 'Tentang';
  @override
  String get privacyPolicy => 'Kebijakan Privasi';
  @override
  String get termsOfService => 'Syarat Layanan';
  @override
  String get support => 'Dukungan';

  @override
  String get save => 'Simpan';
  @override
  String get cancel => 'Batal';
  @override
  String get ok => 'OK';
  @override
  String get error => 'Error';
  @override
  String get loading => 'Memuat...';
  @override
  String get retry => 'Coba Lagi';
  @override
  String get success => 'Berhasil';

  @override
  String get scanningInProgress => 'Pemindaian sedang berlangsung...';
  @override
  String get analysisComplete => 'Analisis selesai';
  @override
  String get noInternetConnection => 'Tidak ada koneksi internet';
  @override
  String get apiKeyRequired => 'Kunci API diperlukan';
  @override
  String get dailyLimitReached => 'Batas harian tercapai';
}

// French
class AppLocalizationsFr extends AppLocalizations {
  @override
  String get appName => 'HalalWise';
  @override
  String get appTagline => 'Vérification Intelligente des Aliments Halal';

  @override
  String get home => 'Accueil';
  @override
  String get scanner => 'Scanner';
  @override
  String get history => 'Historique';
  @override
  String get settings => 'Paramètres';

  @override
  String get scanLabel => 'Scanner l\'étiquette';
  @override
  String get manualInput => 'Saisie manuelle';
  @override
  String get voiceInput => 'Saisie vocale';
  @override
  String get scanBarcode => 'Scanner le code-barres';
  @override
  String get takePhoto => 'Prendre une photo';
  @override
  String get selectFromGallery => 'Sélectionner depuis la galerie';
  @override
  String get comingSoon => 'Bientôt disponible';

  @override
  String get halalStatus => 'Statut Halal';
  @override
  String get halal => 'Halal';
  @override
  String get haram => 'Haram';
  @override
  String get questionable => 'Douteux';
  @override
  String get uncertain => 'Incertain';
  @override
  String get healthScore => 'Score de santé';
  @override
  String get ingredients => 'Ingrédients';
  @override
  String get nutritionalInfo => 'Info nutritionnelle';
  @override
  String get allergens => 'Allergènes';

  @override
  String get scanHistory => 'Historique des scans';
  @override
  String get noHistoryYet => 'Aucun historique pour le moment';
  @override
  String get clearHistory => 'Effacer l\'historique';

  @override
  String get language => 'Langue';
  @override
  String get theme => 'Thème';
  @override
  String get notifications => 'Notifications';

  @override
  String get about => 'À propos';
  @override
  String get privacyPolicy => 'Politique de confidentialité';
  @override
  String get termsOfService => 'Conditions d\'utilisation';
  @override
  String get support => 'Support';

  @override
  String get save => 'Enregistrer';
  @override
  String get cancel => 'Annuler';
  @override
  String get ok => 'OK';
  @override
  String get error => 'Erreur';
  @override
  String get loading => 'Chargement...';
  @override
  String get retry => 'Réessayer';
  @override
  String get success => 'Succès';

  @override
  String get scanningInProgress => 'Scan en cours...';
  @override
  String get analysisComplete => 'Analyse terminée';
  @override
  String get noInternetConnection => 'Pas de connexion internet';
  @override
  String get apiKeyRequired => 'Clé API requise';
  @override
  String get dailyLimitReached => 'Limite quotidienne atteinte';
}
