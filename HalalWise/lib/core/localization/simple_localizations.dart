import 'package:flutter/material.dart';

class SimpleLocalizations {
  static SimpleLocalizations of(BuildContext context) {
    return SimpleLocalizations();
  }

  // App Name
  String get appName => 'HalalWise';
  String get appTagline => 'Smart Halal Food Verification';

  // Navigation
  String get home => 'Home';
  String get scanner => 'Scanner';
  String get history => 'History';
  String get settings => 'Settings';

  // Scanner
  String get scanLabel => 'Scan Label';
  String get manualInput => 'Manual Input';
  String get voiceInput => 'Voice Input';
  String get scanBarcode => 'Scan Barcode';
  String get takePhoto => 'Take Photo';
  String get selectFromGallery => 'Select from Gallery';
  String get comingSoon => 'Coming Soon';

  // Food Analysis
  String get halalStatus => 'Halal Status';
  String get halal => 'Halal';
  String get haram => 'Haram';
  String get questionable => 'Questionable';
  String get uncertain => 'Uncertain';
  String get healthScore => 'Health Score';
  String get ingredients => 'Ingredients';
  String get nutritionalInfo => 'Nutritional Info';
  String get allergens => 'Allergens';

  // History
  String get scanHistory => 'Scan History';
  String get noHistoryYet => 'No scan history yet';
  String get clearHistory => 'Clear History';

  // Settings
  String get language => 'Language';
  String get theme => 'Theme';
  String get notifications => 'Notifications';

  String get about => 'About';
  String get privacyPolicy => 'Privacy Policy';
  String get termsOfService => 'Terms of Service';
  String get support => 'Support';

  // Common
  String get save => 'Save';
  String get cancel => 'Cancel';
  String get ok => 'OK';
  String get error => 'Error';
  String get loading => 'Loading...';
  String get retry => 'Retry';
  String get success => 'Success';

  // Messages
  String get scanningInProgress => 'Scanning in progress...';
  String get analysisComplete => 'Analysis complete';
  String get noInternetConnection => 'No internet connection';
  String get apiKeyRequired => 'API key required';
  String get dailyLimitReached => 'Daily limit reached';

  // Quick Actions
  String get quickActions => 'Quick Actions';
  String get quickProductLookup => 'Quick product lookup';
  String get typeToSearch => 'Type food details';
  String get speakToSearch => 'Speak to search';
  String get recentScans => 'Recent Scans';
  String get viewAll => 'View All';

  // Label Scanner
  String get labelScanner => 'Label Scanner';
  String get selectImage => 'Select Image';
  String get extractText => 'Extract Text';
  String get analyze => 'Analyze';
  String get extractedText => 'Extracted Text';
  String get noTextFound => 'No text found in image';
  String get scanTips => 'Tips for better scanning:';
  String get tip1 => '• Ensure good lighting';
  String get tip2 => '• Keep image clear and focused';
  String get tip3 => '• Include ingredient list';
  String get tip4 => '• Avoid shadows and glare';

  // Analysis Results
  String get analysisResults => 'Analysis Results';
  String get productName => 'Product Name';
  String get brand => 'Brand';
  String get certifications => 'Certifications';
  String get concerns => 'Concerns';
  String get recommendations => 'Recommendations';
  String get confidence => 'Confidence';

  // Settings Pages
  String get languageSettings => 'Language Settings';
  String get themeSettings => 'Theme Settings';
  String get notificationSettings => 'Notification Settings';
  String get lightTheme => 'Light Theme';
  String get darkTheme => 'Dark Theme';
  String get systemTheme => 'System Default';
  String get generalNotifications => 'General Notifications';
  String get scanNotifications => 'Scan Notifications';
  String get updateNotifications => 'Update Notifications';
  String get testNotification => 'Test Notification';

  // Legal
  String get lastUpdated => 'Last Updated';
  String get contactUs => 'Contact Us';
  String get email => 'Email';
  String get website => 'Website';

  // Support
  String get helpCenter => 'Help Center';
  String get faq => 'Frequently Asked Questions';
  String get contactSupport => 'Contact Support';
  String get sendFeedback => 'Send Feedback';
  String get reportBug => 'Report a Bug';
  String get featureRequest => 'Feature Request';
}
