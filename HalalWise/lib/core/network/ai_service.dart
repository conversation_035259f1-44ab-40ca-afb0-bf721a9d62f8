import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../app_config.dart';
import 'api_client.dart';

class AIService {
  final ApiClient _apiClient;
  // API management moved to backend - these constants are deprecated

  AIService(this._apiClient);

  Future<HalalAnalysisResult> analyzeHalalStatus({
    required String productName,
    String? ingredients,
    String? brand,
    List<String>? certifications,
  }) async {
    try {
      // API usage is handled by backend subscription

      final prompt = _buildHalalAnalysisPrompt(
        productName: productName,
        ingredients: ingredients,
        brand: brand,
        certifications: certifications,
      );

      final response = await _makeGeminiRequest(prompt);
      return _parseHalalAnalysisResponse(response);
    } catch (e) {
      print('Error analyzing halal status: $e');
      return HalalAnalysisResult(
        status: HalalStatus.uncertain,
        confidence: 0.0,
        reason: 'Unable to analyze due to technical error: ${e.toString()}',
        questionableIngredients: [],
      );
    }
  }

  Future<HealthAnalysisResult> analyzeHealthScore({
    required String productName,
    String? ingredients,
    Map<String, double>? nutritionalInfo,
  }) async {
    try {
      // API usage is handled by backend subscription

      final prompt = _buildHealthAnalysisPrompt(
        productName: productName,
        ingredients: ingredients,
        nutritionalInfo: nutritionalInfo,
      );

      final response = await _makeGeminiRequest(prompt);
      return _parseHealthAnalysisResponse(response);
    } catch (e) {
      print('Error analyzing health score: $e');
      return HealthAnalysisResult(
        score: 5.0,
        category: 'Unknown',
        positives: [],
        negatives: ['Unable to analyze: ${e.toString()}'],
        allergens: [],
      );
    }
  }

  Future<String> analyzeImageText(File imageFile) async {
    try {
      // For now, we'll use a placeholder for image analysis
      // In a real implementation, you would use Google Vision API or similar
      return await _analyzeImageWithVision(imageFile);
    } catch (e) {
      print('Error analyzing image: $e');
      throw Exception('Failed to analyze image: $e');
    }
  }

  String _buildHalalAnalysisPrompt({
    required String productName,
    String? ingredients,
    String? brand,
    List<String>? certifications,
  }) {
    final buffer = StringBuffer();
    buffer.writeln('Analyze the halal status of this food product according to Islamic dietary laws:');
    buffer.writeln('Product: $productName');

    if (brand != null) {
      buffer.writeln('Brand: $brand');
    }

    if (ingredients != null) {
      buffer.writeln('Ingredients: $ingredients');
    }

    if (certifications != null && certifications.isNotEmpty) {
      buffer.writeln('Certifications: ${certifications.join(', ')}');
    }

    buffer.writeln('\nPlease provide:');
    buffer.writeln('1. Halal status (halal/haram/questionable)');
    buffer.writeln('2. Confidence level (0-1)');
    buffer.writeln('3. Detailed reason for the classification');
    buffer.writeln('4. List any questionable ingredients');
    buffer.writeln('\nConsider these factors:');
    buffer.writeln('- Pork and pork derivatives');
    buffer.writeln('- Alcohol and alcohol-based ingredients');
    buffer.writeln('- Non-zabiha meat');
    buffer.writeln('- Gelatin from unknown sources');
    buffer.writeln('- Enzymes and emulsifiers from unknown sources');
    buffer.writeln('- Halal certification presence');

    buffer.writeln('\nRespond in JSON format:');
    buffer.writeln('{');
    buffer.writeln('  "status": "halal|haram|questionable",');
    buffer.writeln('  "confidence": 0.0-1.0,');
    buffer.writeln('  "reason": "detailed explanation",');
    buffer.writeln('  "questionable_ingredients": ["ingredient1", "ingredient2"]');
    buffer.writeln('}');

    return buffer.toString();
  }

  String _buildHealthAnalysisPrompt({
    required String productName,
    String? ingredients,
    Map<String, double>? nutritionalInfo,
  }) {
    final buffer = StringBuffer();
    buffer.writeln('Analyze the health score of this food product:');
    buffer.writeln('Product: $productName');

    if (ingredients != null) {
      buffer.writeln('Ingredients: $ingredients');
    }

    if (nutritionalInfo != null) {
      buffer.writeln('Nutritional Information (per 100g):');
      nutritionalInfo.forEach((key, value) {
        buffer.writeln('- $key: ${value}g');
      });
    }

    buffer.writeln('\nPlease provide:');
    buffer.writeln('1. Health score (1-10, where 10 is healthiest)');
    buffer.writeln('2. Health category (Excellent/Good/Fair/Poor)');
    buffer.writeln('3. Positive health aspects');
    buffer.writeln('4. Negative health aspects');
    buffer.writeln('5. Common allergens present');

    buffer.writeln('\nConsider these factors:');
    buffer.writeln('- Sugar content');
    buffer.writeln('- Sodium levels');
    buffer.writeln('- Saturated fat content');
    buffer.writeln('- Fiber content');
    buffer.writeln('- Protein content');
    buffer.writeln('- Artificial additives');
    buffer.writeln('- Processing level');

    buffer.writeln('\nRespond in JSON format:');
    buffer.writeln('{');
    buffer.writeln('  "score": 1-10,');
    buffer.writeln('  "category": "health category",');
    buffer.writeln('  "positives": ["positive1", "positive2"],');
    buffer.writeln('  "negatives": ["negative1", "negative2"],');
    buffer.writeln('  "allergens": ["allergen1", "allergen2"]');
    buffer.writeln('}');

    return buffer.toString();
  }

  Future<String> _makeGeminiRequest(String prompt) async {
    // API key will be handled by backend - no client-side API key needed
    // Use backend proxy for all API calls
    return await _makeBackendRequest(prompt);
  }

  Future<String> _makeBackendRequest(String prompt) async {
    final response = await _apiClient.post(
      '${AppConfig.backendBaseUrl}/analyze',
      options: Options(
        headers: {
          'Content-Type': 'application/json',
          // No API key needed - backend handles authentication
        },
      ),
      data: {
        'prompt': prompt,
        'type': 'halal_analysis',
      },
    );

    if (response.statusCode == 200) {
      return response.data['result'];
    } else {
      throw Exception('Backend request failed: ${response.statusCode}');
    }
  }

  Future<String> _analyzeImageWithVision(File imageFile) async {
    // Placeholder for Google Vision API integration
    // In a real implementation, you would:
    // 1. Convert image to base64
    // 2. Send to Google Vision API for OCR
    // 3. Return extracted text

    throw UnimplementedError('Image analysis not implemented yet');
  }

  HalalAnalysisResult _parseHalalAnalysisResponse(String response) {
    try {
      // Extract JSON from response
      final jsonStart = response.indexOf('{');
      final jsonEnd = response.lastIndexOf('}') + 1;

      if (jsonStart == -1 || jsonEnd == 0) {
        throw Exception('No JSON found in response');
      }

      final jsonString = response.substring(jsonStart, jsonEnd);
      final data = json.decode(jsonString);

      return HalalAnalysisResult(
        status: _parseHalalStatus(data['status']),
        confidence: (data['confidence'] as num).toDouble(),
        reason: data['reason'] as String,
        questionableIngredients: List<String>.from(data['questionable_ingredients'] ?? []),
      );
    } catch (e) {
      print('Error parsing halal analysis response: $e');
      return HalalAnalysisResult(
        status: HalalStatus.uncertain,
        confidence: 0.0,
        reason: 'Unable to parse AI response',
        questionableIngredients: [],
      );
    }
  }

  HealthAnalysisResult _parseHealthAnalysisResponse(String response) {
    try {
      // Extract JSON from response
      final jsonStart = response.indexOf('{');
      final jsonEnd = response.lastIndexOf('}') + 1;

      if (jsonStart == -1 || jsonEnd == 0) {
        throw Exception('No JSON found in response');
      }

      final jsonString = response.substring(jsonStart, jsonEnd);
      final data = json.decode(jsonString);

      return HealthAnalysisResult(
        score: (data['score'] as num).toDouble(),
        category: data['category'] as String,
        positives: List<String>.from(data['positives'] ?? []),
        negatives: List<String>.from(data['negatives'] ?? []),
        allergens: List<String>.from(data['allergens'] ?? []),
      );
    } catch (e) {
      print('Error parsing health analysis response: $e');
      return HealthAnalysisResult(
        score: 5.0,
        category: 'Unknown',
        positives: [],
        negatives: ['Unable to parse AI response'],
        allergens: [],
      );
    }
  }

  HalalStatus _parseHalalStatus(String status) {
    switch (status.toLowerCase()) {
      case 'halal':
        return HalalStatus.halal;
      case 'haram':
        return HalalStatus.haram;
      case 'questionable':
        return HalalStatus.questionable;
      default:
        return HalalStatus.uncertain;
    }
  }

  // API Key Management - Removed (handled by backend)
  Future<String?> _getApiKey() async {
    // API keys are now handled by backend
    return null;
  }

  Future<void> setApiKey(String apiKey) async {
    // API keys are now handled by backend - this method is deprecated
    print('Warning: API keys should be configured on the backend, not the frontend');
  }

  Future<bool> hasApiKey() async {
    // Always return true since backend handles API keys
    return true;
  }

  // API Usage is now handled by backend subscription system
  // These methods are deprecated and will be removed
}

enum HalalStatus {
  halal,
  haram,
  questionable,
  uncertain,
}

class HalalAnalysisResult {
  final HalalStatus status;
  final double confidence;
  final String reason;
  final List<String> questionableIngredients;

  HalalAnalysisResult({
    required this.status,
    required this.confidence,
    required this.reason,
    required this.questionableIngredients,
  });
}

class HealthAnalysisResult {
  final double score;
  final String category;
  final List<String> positives;
  final List<String> negatives;
  final List<String> allergens;

  HealthAnalysisResult({
    required this.score,
    required this.category,
    required this.positives,
    required this.negatives,
    required this.allergens,
  });
}
