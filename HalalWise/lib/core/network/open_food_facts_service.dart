import 'dart:convert';
import 'package:dio/dio.dart';
import '../app_config.dart';
import 'api_client.dart';

class OpenFoodFactsService {
  final ApiClient _apiClient;
  
  OpenFoodFactsService(this._apiClient);

  Future<OpenFoodFactsProduct?> getProductByBarcode(String barcode) async {
    try {
      final response = await _apiClient.get(
        '${AppConfig.openFoodFactsBaseUrl}/product/$barcode.json',
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['status'] == 1 && data['product'] != null) {
          return OpenFoodFactsProduct.fromJson(data['product']);
        }
      }
      return null;
    } catch (e) {
      print('Error fetching product from Open Food Facts: $e');
      return null;
    }
  }

  Future<List<OpenFoodFactsProduct>> searchProducts(String query) async {
    try {
      final response = await _apiClient.get(
        '${AppConfig.openFoodFactsBaseUrl}/cgi/search.pl',
        queryParameters: {
          'search_terms': query,
          'search_simple': '1',
          'action': 'process',
          'json': '1',
          'page_size': '20',
        },
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['products'] != null) {
          return (data['products'] as List)
              .map((product) => OpenFoodFactsProduct.fromJson(product))
              .toList();
        }
      }
      return [];
    } catch (e) {
      print('Error searching products from Open Food Facts: $e');
      return [];
    }
  }
}

class OpenFoodFactsProduct {
  final String? barcode;
  final String? productName;
  final String? brands;
  final String? ingredients;
  final String? imageUrl;
  final Map<String, dynamic>? nutriments;
  final List<String>? allergens;
  final List<String>? traces;
  final String? categories;
  final String? labels;

  OpenFoodFactsProduct({
    this.barcode,
    this.productName,
    this.brands,
    this.ingredients,
    this.imageUrl,
    this.nutriments,
    this.allergens,
    this.traces,
    this.categories,
    this.labels,
  });

  factory OpenFoodFactsProduct.fromJson(Map<String, dynamic> json) {
    return OpenFoodFactsProduct(
      barcode: json['code'] as String?,
      productName: json['product_name'] as String? ?? 
                   json['product_name_en'] as String?,
      brands: json['brands'] as String?,
      ingredients: json['ingredients_text'] as String? ?? 
                   json['ingredients_text_en'] as String?,
      imageUrl: json['image_url'] as String? ?? 
                json['image_front_url'] as String?,
      nutriments: json['nutriments'] as Map<String, dynamic>?,
      allergens: _parseStringList(json['allergens_tags']),
      traces: _parseStringList(json['traces_tags']),
      categories: json['categories'] as String?,
      labels: json['labels'] as String?,
    );
  }

  static List<String>? _parseStringList(dynamic value) {
    if (value is List) {
      return value.map((e) => e.toString()).toList();
    }
    return null;
  }

  Map<String, dynamic> toJson() {
    return {
      'barcode': barcode,
      'product_name': productName,
      'brands': brands,
      'ingredients': ingredients,
      'image_url': imageUrl,
      'nutriments': nutriments,
      'allergens': allergens,
      'traces': traces,
      'categories': categories,
      'labels': labels,
    };
  }

  // Helper methods to extract nutritional information
  double? get calories => _getNutriment('energy-kcal_100g');
  double? get sugar => _getNutriment('sugars_100g');
  double? get sodium => _getNutriment('sodium_100g');
  double? get saturatedFat => _getNutriment('saturated-fat_100g');
  double? get fiber => _getNutriment('fiber_100g');
  double? get protein => _getNutriment('proteins_100g');
  double? get fat => _getNutriment('fat_100g');
  double? get carbohydrates => _getNutriment('carbohydrates_100g');

  double? _getNutriment(String key) {
    if (nutriments == null) return null;
    final value = nutriments![key];
    if (value is num) return value.toDouble();
    if (value is String) return double.tryParse(value);
    return null;
  }

  // Check if product has halal-related labels
  bool get hasHalalLabel {
    if (labels == null) return false;
    final lowerLabels = labels!.toLowerCase();
    return lowerLabels.contains('halal') || 
           lowerLabels.contains('حلال');
  }

  // Check if product has non-halal ingredients
  bool get hasNonHalalIngredients {
    if (ingredients == null) return false;
    final lowerIngredients = ingredients!.toLowerCase();
    
    for (String ingredient in AppConfig.nonHalalIngredients) {
      if (lowerIngredients.contains(ingredient.toLowerCase())) {
        return true;
      }
    }
    return false;
  }

  // Get list of questionable ingredients
  List<String> get questionableIngredients {
    if (ingredients == null) return [];
    final lowerIngredients = ingredients!.toLowerCase();
    final found = <String>[];
    
    for (String ingredient in AppConfig.questionableIngredients) {
      if (lowerIngredients.contains(ingredient.toLowerCase())) {
        found.add(ingredient);
      }
    }
    return found;
  }

  // Get allergen information
  List<String> get allergenList {
    final allAllergens = <String>[];
    
    if (allergens != null) {
      for (String allergen in allergens!) {
        // Remove 'en:' prefix if present
        final cleanAllergen = allergen.replaceFirst('en:', '');
        allAllergens.add(cleanAllergen);
      }
    }
    
    if (traces != null) {
      for (String trace in traces!) {
        // Remove 'en:' prefix if present
        final cleanTrace = trace.replaceFirst('en:', '');
        if (!allAllergens.contains(cleanTrace)) {
          allAllergens.add('may contain $cleanTrace');
        }
      }
    }
    
    return allAllergens;
  }
}
