import 'package:get_it/get_it.dart';
import '../database/database_helper.dart';
import '../../features/scanner/data/repositories/barcode_repository_impl.dart';
import '../../features/scanner/data/repositories/ocr_repository_impl.dart';
import '../../features/scanner/domain/repositories/barcode_repository.dart';
import '../../features/scanner/domain/repositories/ocr_repository.dart';
import '../../features/scanner/domain/usecases/scan_barcode_usecase.dart';
import '../../features/scanner/domain/usecases/scan_label_usecase.dart';
import '../../features/food_analysis/data/repositories/food_analysis_repository_impl.dart';
import '../../features/food_analysis/domain/repositories/food_analysis_repository.dart';
import '../../features/food_analysis/domain/usecases/analyze_food_usecase.dart';
import '../../features/food_analysis/domain/usecases/get_halal_status_usecase.dart';
import '../../features/food_analysis/domain/usecases/calculate_health_score_usecase.dart';
import '../../features/history/data/repositories/history_repository_impl.dart';
import '../../features/history/domain/repositories/history_repository.dart';
import '../../features/history/domain/usecases/get_scan_history_usecase.dart';
import '../../features/history/domain/usecases/save_scan_result_usecase.dart';
import '../network/api_client.dart';
import '../network/open_food_facts_service.dart';
import '../network/ai_service.dart';

final GetIt sl = GetIt.instance;

Future<void> setupServiceLocator() async {
  // Core services
  sl.registerLazySingleton<DatabaseHelper>(() => DatabaseHelper.instance);
  sl.registerLazySingleton<ApiClient>(() => ApiClient());
  sl.registerLazySingleton<OpenFoodFactsService>(() => OpenFoodFactsService(sl()));
  sl.registerLazySingleton<AIService>(() => AIService(sl()));

  // Repositories
  sl.registerLazySingleton<BarcodeRepository>(
    () => BarcodeRepositoryImpl(sl(), sl()),
  );
  sl.registerLazySingleton<OCRRepository>(
    () => OCRRepositoryImpl(),
  );
  sl.registerLazySingleton<FoodAnalysisRepository>(
    () => FoodAnalysisRepositoryImpl(sl(), sl()),
  );
  sl.registerLazySingleton<HistoryRepository>(
    () => HistoryRepositoryImpl(sl()),
  );

  // Use cases
  sl.registerLazySingleton(() => ScanBarcodeUseCase(sl()));
  sl.registerLazySingleton(() => ScanLabelUseCase(sl()));
  sl.registerLazySingleton(() => AnalyzeFoodUseCase(sl()));
  sl.registerLazySingleton(() => GetHalalStatusUseCase(sl()));
  sl.registerLazySingleton(() => CalculateHealthScoreUseCase(sl()));
  sl.registerLazySingleton(() => GetScanHistoryUseCase(sl()));
  sl.registerLazySingleton(() => SaveScanResultUseCase(sl()));
}
