import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../app_config.dart';
import 'sample_data.dart';

class DatabaseHelper {
  static final DatabaseHelper instance = DatabaseHelper._init();
  static Database? _database;

  DatabaseHelper._init();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDB(AppConfig.databaseName);
    return _database!;
  }

  Future<Database> _initDB(String filePath) async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, filePath);

    return await openDatabase(
      path,
      version: AppConfig.databaseVersion,
      onCreate: _createDB,
      onUpgrade: _upgradeDB,
    );
  }

  Future<void> _createDB(Database db, int version) async {
    // Products table
    await db.execute('''
      CREATE TABLE products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        barcode TEXT UNIQUE,
        name TEXT NOT NULL,
        brand TEXT,
        ingredients TEXT,
        halal_status TEXT NOT NULL,
        halal_confidence REAL,
        halal_reason TEXT,
        health_score REAL,
        calories REAL,
        sugar REAL,
        sodium REAL,
        saturated_fat REAL,
        fiber REAL,
        protein REAL,
        allergens TEXT,
        certifications TEXT,
        image_url TEXT,
        source TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    // Ingredients table
    await db.execute('''
      CREATE TABLE ingredients (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL,
        halal_status TEXT NOT NULL,
        category TEXT,
        description TEXT,
        alternatives TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    // Scan history table
    await db.execute('''
      CREATE TABLE scan_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_id INTEGER,
        scan_type TEXT NOT NULL,
        scan_data TEXT,
        result TEXT NOT NULL,
        user_feedback TEXT,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (product_id) REFERENCES products (id)
      )
    ''');

    // User preferences table
    await db.execute('''
      CREATE TABLE user_preferences (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT UNIQUE NOT NULL,
        value TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    // Certifications table
    await db.execute('''
      CREATE TABLE certifications (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL,
        authority TEXT NOT NULL,
        logo_url TEXT,
        website TEXT,
        description TEXT,
        is_trusted BOOLEAN DEFAULT 1,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    // Create indexes for better performance
    await db.execute('CREATE INDEX idx_products_barcode ON products(barcode)');
    await db.execute('CREATE INDEX idx_products_name ON products(name)');
    await db.execute('CREATE INDEX idx_ingredients_name ON ingredients(name)');
    await db.execute('CREATE INDEX idx_scan_history_created_at ON scan_history(created_at)');

    // Insert default halal certification bodies
    for (String cert in AppConfig.halalCertificationBodies) {
      await db.insert('certifications', {
        'name': cert,
        'authority': cert,
        'is_trusted': 1,
        'created_at': DateTime.now().millisecondsSinceEpoch,
        'updated_at': DateTime.now().millisecondsSinceEpoch,
      });
    }

    // Insert common non-halal ingredients
    for (String ingredient in AppConfig.nonHalalIngredients) {
      await db.insert('ingredients', {
        'name': ingredient.toLowerCase(),
        'halal_status': 'haram',
        'category': 'non-halal',
        'created_at': DateTime.now().millisecondsSinceEpoch,
        'updated_at': DateTime.now().millisecondsSinceEpoch,
      });
    }

    // Insert questionable ingredients
    for (String ingredient in AppConfig.questionableIngredients) {
      await db.insert('ingredients', {
        'name': ingredient.toLowerCase(),
        'halal_status': 'questionable',
        'category': 'requires-verification',
        'created_at': DateTime.now().millisecondsSinceEpoch,
        'updated_at': DateTime.now().millisecondsSinceEpoch,
      });
    }

    // Insert extended ingredients from sample data
    for (Map<String, dynamic> ingredient in SampleData.extendedIngredients) {
      await db.insert('ingredients', {
        'name': ingredient['name'],
        'halal_status': ingredient['halal_status'],
        'category': ingredient['category'],
        'created_at': DateTime.now().millisecondsSinceEpoch,
        'updated_at': DateTime.now().millisecondsSinceEpoch,
      }, conflictAlgorithm: ConflictAlgorithm.ignore);
    }

    // Insert sample products for testing
    for (Map<String, dynamic> product in SampleData.sampleProducts) {
      await db.insert('products', {
        ...product,
        'created_at': DateTime.now().millisecondsSinceEpoch,
        'updated_at': DateTime.now().millisecondsSinceEpoch,
      }, conflictAlgorithm: ConflictAlgorithm.ignore);
    }

    // Insert certification bodies
    for (Map<String, dynamic> cert in SampleData.certificationBodies) {
      await db.insert('certifications', {
        ...cert,
        'created_at': DateTime.now().millisecondsSinceEpoch,
        'updated_at': DateTime.now().millisecondsSinceEpoch,
      }, conflictAlgorithm: ConflictAlgorithm.ignore);
    }
  }

  Future<void> _upgradeDB(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    if (oldVersion < 2) {
      // Add new columns or tables for version 2
    }
  }

  // Product operations
  Future<int> insertProduct(Map<String, dynamic> product) async {
    final db = await instance.database;
    product['created_at'] = DateTime.now().millisecondsSinceEpoch;
    product['updated_at'] = DateTime.now().millisecondsSinceEpoch;
    return await db.insert('products', product);
  }

  Future<Map<String, dynamic>?> getProductByBarcode(String barcode) async {
    final db = await instance.database;
    final result = await db.query(
      'products',
      where: 'barcode = ?',
      whereArgs: [barcode],
      limit: 1,
    );
    return result.isNotEmpty ? result.first : null;
  }

  Future<List<Map<String, dynamic>>> searchProducts(String query) async {
    final db = await instance.database;
    return await db.query(
      'products',
      where: 'name LIKE ? OR brand LIKE ?',
      whereArgs: ['%$query%', '%$query%'],
      orderBy: 'name ASC',
      limit: 50,
    );
  }

  // Ingredient operations
  Future<Map<String, dynamic>?> getIngredient(String name) async {
    final db = await instance.database;
    final result = await db.query(
      'ingredients',
      where: 'name = ?',
      whereArgs: [name.toLowerCase()],
      limit: 1,
    );
    return result.isNotEmpty ? result.first : null;
  }

  Future<int> insertIngredient(Map<String, dynamic> ingredient) async {
    final db = await instance.database;
    ingredient['created_at'] = DateTime.now().millisecondsSinceEpoch;
    ingredient['updated_at'] = DateTime.now().millisecondsSinceEpoch;
    return await db.insert('ingredients', ingredient);
  }

  // Scan history operations
  Future<int> insertScanHistory(Map<String, dynamic> scan) async {
    final db = await instance.database;
    scan['created_at'] = DateTime.now().millisecondsSinceEpoch;
    return await db.insert('scan_history', scan);
  }

  Future<List<Map<String, dynamic>>> getScanHistory({int limit = 50}) async {
    final db = await instance.database;
    return await db.query(
      'scan_history',
      orderBy: 'created_at DESC',
      limit: limit,
    );
  }

  // User preferences operations
  Future<void> setPreference(String key, String value) async {
    final db = await instance.database;
    await db.insert(
      'user_preferences',
      {
        'key': key,
        'value': value,
        'created_at': DateTime.now().millisecondsSinceEpoch,
        'updated_at': DateTime.now().millisecondsSinceEpoch,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<String?> getPreference(String key) async {
    final db = await instance.database;
    final result = await db.query(
      'user_preferences',
      where: 'key = ?',
      whereArgs: [key],
      limit: 1,
    );
    return result.isNotEmpty ? result.first['value'] as String : null;
  }

  // Cleanup operations
  Future<void> clearOldCache() async {
    final db = await instance.database;
    final cutoffTime = DateTime.now()
        .subtract(AppConfig.cacheExpiration)
        .millisecondsSinceEpoch;

    await db.delete(
      'scan_history',
      where: 'created_at < ?',
      whereArgs: [cutoffTime],
    );
  }

  Future<void> close() async {
    final db = await instance.database;
    await db.close();
  }
}
