// Sample data for HalalWise database initialization
class SampleData {
  // Sample products for testing
  static const List<Map<String, dynamic>> sampleProducts = [
    {
      'barcode': '5449000000996',
      'name': 'Coca-Cola Classic',
      'brand': 'Coca-Cola',
      'ingredients': 'Carbonated water, sugar, carbon dioxide, phosphoric acid, natural flavors, caffeine',
      'halal_status': 'halal',
      'halal_confidence': 0.95,
      'halal_reason': 'No haram ingredients detected. Widely accepted as halal.',
      'health_score': 3.0,
      'calories': 139.0,
      'sugar': 35.0,
      'sodium': 0.01,
      'saturated_fat': 0.0,
      'fiber': 0.0,
      'protein': 0.0,
      'allergens': '',
      'certifications': '',
      'source': 'sample_data',
    },
    {
      'barcode': '7622210951557',
      'name': 'Oreo Original Cookies',
      'brand': 'Oreo',
      'ingredients': 'Wheat flour, sugar, palm oil, cocoa powder, glucose-fructose syrup, wheat starch, raising agents, salt, emulsifiers, vanilla flavoring',
      'halal_status': 'questionable',
      'halal_confidence': 0.7,
      'halal_reason': 'Contains emulsifiers which may be from animal sources. Requires verification.',
      'health_score': 4.0,
      'calories': 480.0,
      'sugar': 36.0,
      'sodium': 0.45,
      'saturated_fat': 11.0,
      'fiber': 3.0,
      'protein': 5.0,
      'allergens': 'wheat, may contain milk',
      'certifications': '',
      'source': 'sample_data',
    },
    {
      'barcode': '3017620422003',
      'name': 'Nutella Hazelnut Spread',
      'brand': 'Nutella',
      'ingredients': 'Sugar, palm oil, hazelnuts, skimmed milk powder, cocoa, lecithin, vanillin',
      'halal_status': 'questionable',
      'halal_confidence': 0.6,
      'halal_reason': 'Contains lecithin which may be from animal sources. Requires halal certification verification.',
      'health_score': 3.5,
      'calories': 539.0,
      'sugar': 56.0,
      'sodium': 0.04,
      'saturated_fat': 11.0,
      'fiber': 0.0,
      'protein': 6.0,
      'allergens': 'hazelnuts, milk, may contain other nuts',
      'certifications': '',
      'source': 'sample_data',
    },
    {
      'barcode': '8901030865507',
      'name': 'Maggi 2-Minute Noodles',
      'brand': 'Maggi',
      'ingredients': 'Wheat flour, palm oil, salt, sugar, spices, flavor enhancers, stabilizers',
      'halal_status': 'halal',
      'halal_confidence': 0.9,
      'halal_reason': 'Certified halal by multiple authorities. No haram ingredients.',
      'health_score': 5.0,
      'calories': 205.0,
      'sugar': 2.0,
      'sodium': 0.8,
      'saturated_fat': 4.0,
      'fiber': 2.0,
      'protein': 4.0,
      'allergens': 'wheat',
      'certifications': 'Halal India, JAKIM',
      'source': 'sample_data',
    },
    {
      'barcode': '4902777193731',
      'name': 'Kit Kat Chocolate Bar',
      'brand': 'Kit Kat',
      'ingredients': 'Sugar, wheat flour, cocoa butter, milk powder, cocoa mass, lactose, emulsifier, yeast, flavoring',
      'halal_status': 'questionable',
      'halal_confidence': 0.5,
      'halal_reason': 'Contains emulsifiers and flavorings that may not be halal. Regional variations exist.',
      'health_score': 3.0,
      'calories': 518.0,
      'sugar': 47.0,
      'sodium': 0.02,
      'saturated_fat': 13.0,
      'fiber': 2.0,
      'protein': 7.0,
      'allergens': 'wheat, milk, may contain nuts',
      'certifications': '',
      'source': 'sample_data',
    },
  ];

  // Extended ingredients database
  static const List<Map<String, dynamic>> extendedIngredients = [
    // Clearly Halal
    {'name': 'water', 'halal_status': 'halal', 'category': 'basic'},
    {'name': 'salt', 'halal_status': 'halal', 'category': 'basic'},
    {'name': 'sugar', 'halal_status': 'halal', 'category': 'basic'},
    {'name': 'wheat flour', 'halal_status': 'halal', 'category': 'grain'},
    {'name': 'rice', 'halal_status': 'halal', 'category': 'grain'},
    {'name': 'corn', 'halal_status': 'halal', 'category': 'grain'},
    {'name': 'olive oil', 'halal_status': 'halal', 'category': 'oil'},
    {'name': 'sunflower oil', 'halal_status': 'halal', 'category': 'oil'},
    {'name': 'coconut oil', 'halal_status': 'halal', 'category': 'oil'},
    {'name': 'tomato', 'halal_status': 'halal', 'category': 'vegetable'},
    {'name': 'onion', 'halal_status': 'halal', 'category': 'vegetable'},
    {'name': 'garlic', 'halal_status': 'halal', 'category': 'vegetable'},
    {'name': 'black pepper', 'halal_status': 'halal', 'category': 'spice'},
    {'name': 'turmeric', 'halal_status': 'halal', 'category': 'spice'},
    {'name': 'cumin', 'halal_status': 'halal', 'category': 'spice'},
    
    // Clearly Haram
    {'name': 'pork', 'halal_status': 'haram', 'category': 'meat'},
    {'name': 'ham', 'halal_status': 'haram', 'category': 'meat'},
    {'name': 'bacon', 'halal_status': 'haram', 'category': 'meat'},
    {'name': 'lard', 'halal_status': 'haram', 'category': 'fat'},
    {'name': 'alcohol', 'halal_status': 'haram', 'category': 'additive'},
    {'name': 'wine', 'halal_status': 'haram', 'category': 'additive'},
    {'name': 'beer', 'halal_status': 'haram', 'category': 'additive'},
    {'name': 'rum', 'halal_status': 'haram', 'category': 'additive'},
    {'name': 'vodka', 'halal_status': 'haram', 'category': 'additive'},
    {'name': 'whiskey', 'halal_status': 'haram', 'category': 'additive'},
    {'name': 'carmine', 'halal_status': 'haram', 'category': 'colorant'},
    {'name': 'cochineal', 'halal_status': 'haram', 'category': 'colorant'},
    
    // Questionable (requires verification)
    {'name': 'gelatin', 'halal_status': 'questionable', 'category': 'gelling-agent'},
    {'name': 'mono and diglycerides', 'halal_status': 'questionable', 'category': 'emulsifier'},
    {'name': 'glycerin', 'halal_status': 'questionable', 'category': 'humectant'},
    {'name': 'glycerol', 'halal_status': 'questionable', 'category': 'humectant'},
    {'name': 'lecithin', 'halal_status': 'questionable', 'category': 'emulsifier'},
    {'name': 'natural flavors', 'halal_status': 'questionable', 'category': 'flavoring'},
    {'name': 'artificial flavors', 'halal_status': 'questionable', 'category': 'flavoring'},
    {'name': 'enzymes', 'halal_status': 'questionable', 'category': 'processing-aid'},
    {'name': 'emulsifiers', 'halal_status': 'questionable', 'category': 'emulsifier'},
    {'name': 'stabilizers', 'halal_status': 'questionable', 'category': 'stabilizer'},
    {'name': 'whey', 'halal_status': 'questionable', 'category': 'dairy'},
    {'name': 'casein', 'halal_status': 'questionable', 'category': 'dairy'},
    {'name': 'lactose', 'halal_status': 'questionable', 'category': 'dairy'},
    {'name': 'vanilla extract', 'halal_status': 'questionable', 'category': 'flavoring'},
    {'name': 'pepsin', 'halal_status': 'questionable', 'category': 'enzyme'},
    {'name': 'rennet', 'halal_status': 'questionable', 'category': 'enzyme'},
    {'name': 'lipase', 'halal_status': 'questionable', 'category': 'enzyme'},
    {'name': 'tallow', 'halal_status': 'questionable', 'category': 'fat'},
    {'name': 'shellac', 'halal_status': 'questionable', 'category': 'coating'},
    
    // Common halal ingredients
    {'name': 'chicken', 'halal_status': 'halal', 'category': 'meat'},
    {'name': 'beef', 'halal_status': 'halal', 'category': 'meat'},
    {'name': 'lamb', 'halal_status': 'halal', 'category': 'meat'},
    {'name': 'fish', 'halal_status': 'halal', 'category': 'seafood'},
    {'name': 'salmon', 'halal_status': 'halal', 'category': 'seafood'},
    {'name': 'tuna', 'halal_status': 'halal', 'category': 'seafood'},
    {'name': 'milk', 'halal_status': 'halal', 'category': 'dairy'},
    {'name': 'cheese', 'halal_status': 'halal', 'category': 'dairy'},
    {'name': 'butter', 'halal_status': 'halal', 'category': 'dairy'},
    {'name': 'yogurt', 'halal_status': 'halal', 'category': 'dairy'},
    {'name': 'eggs', 'halal_status': 'halal', 'category': 'protein'},
    {'name': 'honey', 'halal_status': 'halal', 'category': 'sweetener'},
    {'name': 'dates', 'halal_status': 'halal', 'category': 'fruit'},
    {'name': 'almonds', 'halal_status': 'halal', 'category': 'nuts'},
    {'name': 'walnuts', 'halal_status': 'halal', 'category': 'nuts'},
    {'name': 'pistachios', 'halal_status': 'halal', 'category': 'nuts'},
    {'name': 'cashews', 'halal_status': 'halal', 'category': 'nuts'},
  ];

  // Halal certification authorities
  static const List<Map<String, dynamic>> certificationBodies = [
    {
      'name': 'IFANCA',
      'authority': 'Islamic Food and Nutrition Council of America',
      'description': 'Leading halal certification body in North America',
      'is_trusted': true,
    },
    {
      'name': 'HMC',
      'authority': 'Halal Monitoring Committee',
      'description': 'UK-based halal certification organization',
      'is_trusted': true,
    },
    {
      'name': 'JAKIM',
      'authority': 'Department of Islamic Development Malaysia',
      'description': 'Malaysian government halal certification',
      'is_trusted': true,
    },
    {
      'name': 'MUI',
      'authority': 'Indonesian Ulema Council',
      'description': 'Indonesia\'s leading Islamic authority',
      'is_trusted': true,
    },
    {
      'name': 'ISWA',
      'authority': 'Islamic Society of Washington Area',
      'description': 'Regional halal certification in Washington DC area',
      'is_trusted': true,
    },
    {
      'name': 'HFCE',
      'authority': 'Halal Food Council of Europe',
      'description': 'European halal certification body',
      'is_trusted': true,
    },
    {
      'name': 'WHFC',
      'authority': 'World Halal Food Council',
      'description': 'International halal certification organization',
      'is_trusted': true,
    },
    {
      'name': 'SANHA',
      'authority': 'South African National Halaal Authority',
      'description': 'Leading halal authority in South Africa',
      'is_trusted': true,
    },
  ];
}
