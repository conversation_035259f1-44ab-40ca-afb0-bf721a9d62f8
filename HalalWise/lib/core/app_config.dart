class AppConfig {
  static const String appName = 'HalalWise';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'AI-powered halal and health food verification';

  // API Configuration
  static const String openFoodFactsBaseUrl = 'https://world.openfoodfacts.org/api/v0';
  static const String geminiApiBaseUrl = 'https://generativelanguage.googleapis.com/v1beta';
  static const String googleVisionApiBaseUrl = 'https://vision.googleapis.com/v1';

  // Backend Configuration (Required - API keys handled by backend)
  static const String backendBaseUrl = 'https://your-backend-url.com/api/v1';
  static const bool useBackendProxy = true; // API keys are handled by backend

  // Database Configuration
  static const String databaseName = 'halalwise.db';
  static const int databaseVersion = 1;

  // Cache Configuration
  static const Duration cacheExpiration = Duration(days: 7);
  static const int maxCacheSize = 1000; // Maximum number of cached items

  // AI Configuration
  static const int maxTokensPerRequest = 1000;
  static const double confidenceThreshold = 0.8;
  static const String geminiModel = 'gemini-1.5-flash'; // or 'gemini-1.5-pro' for better accuracy

  // API Usage is now handled by backend subscription system

  // Scanning Configuration
  static const Duration scanTimeout = Duration(seconds: 10);
  static const int maxImageSize = 2048; // pixels

  // Supported Languages
  static const List<String> supportedLanguages = [
    'en', // English
    'ar', // Arabic
    'ms', // Malay
    'ur', // Urdu
    'tr', // Turkish
    'id', // Indonesian
  ];

  // Halal Certification Bodies
  static const List<String> halalCertificationBodies = [
    'IFANCA',
    'HMC',
    'JAKIM',
    'MUI',
    'ISWA',
    'HFCE',
    'WHFC',
    'SANHA',
  ];

  // Common Non-Halal Ingredients
  static const List<String> nonHalalIngredients = [
    'pork',
    'ham',
    'bacon',
    'lard',
    'gelatin',
    'alcohol',
    'wine',
    'beer',
    'rum',
    'vodka',
    'whiskey',
    'vanilla extract',
    'carmine',
    'cochineal',
    'shellac',
    'pepsin',
    'rennet',
    'lipase',
    'tallow',
  ];

  // Questionable Ingredients (require verification)
  static const List<String> questionableIngredients = [
    'mono and diglycerides',
    'glycerin',
    'glycerol',
    'lecithin',
    'natural flavors',
    'artificial flavors',
    'enzymes',
    'emulsifiers',
    'stabilizers',
    'whey',
    'casein',
    'lactose',
  ];

  // Health Score Weights
  static const Map<String, double> healthScoreWeights = {
    'calories': 0.2,
    'sugar': 0.25,
    'sodium': 0.2,
    'saturatedFat': 0.15,
    'fiber': 0.1,
    'protein': 0.1,
  };

  // Allergens
  static const List<String> commonAllergens = [
    'milk',
    'eggs',
    'fish',
    'shellfish',
    'tree nuts',
    'peanuts',
    'wheat',
    'soybeans',
    'sesame',
  ];
}
