import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
// import 'package:firebase_core/firebase_core.dart';
// import 'package:hive_flutter/hive_flutter.dart';

import 'core/app_config.dart';
import 'core/theme/app_theme.dart';
import 'core/localization/app_localizations.dart';
import 'core/database/database_helper.dart';
import 'core/services/service_locator.dart';
import 'features/home/<USER>/pages/home_page.dart';
import 'features/auth/presentation/providers/auth_provider.dart';
import 'features/scanner/presentation/providers/scanner_provider.dart';
import 'features/food_analysis/presentation/providers/food_analysis_provider.dart';
import 'features/history/presentation/providers/history_provider.dart';
import 'features/settings/presentation/providers/settings_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialize local database
    await DatabaseHelper.instance.database;

    // Setup service locator
    await setupServiceLocator();
  } catch (e) {
    print('Initialization error: $e');
    // Continue anyway for basic functionality
  }

  runApp(const HalalWiseApp());
}

class HalalWiseApp extends StatelessWidget {
  const HalalWiseApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => ScannerProvider()),
        ChangeNotifierProvider(create: (_) => FoodAnalysisProvider()),
        ChangeNotifierProvider(create: (_) => HistoryProvider()),
        ChangeNotifierProvider(create: (_) => SettingsProvider()),
      ],
      child: Consumer<SettingsProvider>(
        builder: (context, settingsProvider, child) {
          return MaterialApp(
            title: AppConfig.appName,
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: settingsProvider.themeMode,
            locale: settingsProvider.locale,
            supportedLocales: AppLocalizations.supportedLocales,
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            home: const HomePage(),
            builder: (context, child) {
              return Directionality(
                textDirection: settingsProvider.isRTL
                    ? TextDirection.rtl
                    : TextDirection.ltr,
                child: child!,
              );
            },
          );
        },
      ),
    );
  }
}
