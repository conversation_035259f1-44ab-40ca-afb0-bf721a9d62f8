import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'core/theme/app_theme.dart';
import 'features/home/<USER>/pages/home_page.dart';
import 'features/auth/presentation/providers/auth_provider.dart';
import 'features/food_analysis/presentation/providers/food_analysis_provider.dart';
import 'features/history/presentation/providers/history_provider.dart';
import 'features/settings/presentation/providers/settings_provider.dart';

void main() {
  runApp(const HalalWiseApp());
}

class HalalWiseApp extends StatelessWidget {
  const HalalWiseApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => FoodAnalysisProvider()),
        ChangeNotifierProvider(create: (_) => HistoryProvider()),
        ChangeNotifierProvider(create: (_) => SettingsProvider()),
      ],
      child: Consumer<SettingsProvider>(
        builder: (context, settingsProvider, child) {
          return MaterialApp(
            title: 'HalalWise',
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: settingsProvider.themeMode,
            home: const HomePage(),
            routes: {
              '/home': (context) => const HomePage(),
            },
          );
        },
      ),
    );
  }
}
