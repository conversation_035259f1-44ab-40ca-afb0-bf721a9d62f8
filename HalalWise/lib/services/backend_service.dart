import 'dart:convert';
import 'package:http/http.dart' as http;
import '../core/app_config.dart';

class BackendService {
  static const String baseUrl = AppConfig.backendBaseUrl;
  static String? _authToken;

  // Set authentication token
  static void setAuthToken(String token) {
    _authToken = token;
  }

  // Get headers with authentication
  static Map<String, String> _getHeaders() {
    final headers = {
      'Content-Type': 'application/json',
    };
    
    if (_authToken != null) {
      headers['Authorization'] = 'Bearer $_authToken';
    }
    
    return headers;
  }

  // Authentication Methods
  static Future<Map<String, dynamic>> register({
    required String email,
    required String password,
    required String name,
  }) async {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/register'),
      headers: _getHeaders(),
      body: jsonEncode({
        'email': email,
        'password': password,
        'name': name,
      }),
    );

    if (response.statusCode == 201) {
      final data = jsonDecode(response.body);
      setAuthToken(data['token']);
      return data;
    } else {
      throw Exception('Registration failed: ${response.body}');
    }
  }

  static Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/login'),
      headers: _getHeaders(),
      body: jsonEncode({
        'email': email,
        'password': password,
      }),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      setAuthToken(data['token']);
      return data;
    } else {
      throw Exception('Login failed: ${response.body}');
    }
  }

  // Product Methods
  static Future<Map<String, dynamic>> getProductByBarcode(String barcode) async {
    final response = await http.get(
      Uri.parse('$baseUrl/products/barcode/$barcode'),
      headers: _getHeaders(),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else if (response.statusCode == 404) {
      throw Exception('Product not found');
    } else {
      throw Exception('Failed to get product: ${response.body}');
    }
  }

  static Future<Map<String, dynamic>> searchProducts({
    required String query,
    String? halalStatus,
    String? category,
    String? brand,
    int limit = 20,
  }) async {
    final queryParams = {
      'q': query,
      'limit': limit.toString(),
    };
    
    if (halalStatus != null) queryParams['halalStatus'] = halalStatus;
    if (category != null) queryParams['category'] = category;
    if (brand != null) queryParams['brand'] = brand;

    final uri = Uri.parse('$baseUrl/products/search').replace(
      queryParameters: queryParams,
    );

    final response = await http.get(uri, headers: _getHeaders());

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Search failed: ${response.body}');
    }
  }

  static Future<List<Map<String, dynamic>>> getPopularProducts({
    int limit = 20,
    String? category,
    String? halalStatus,
  }) async {
    final queryParams = {
      'limit': limit.toString(),
    };
    
    if (category != null) queryParams['category'] = category;
    if (halalStatus != null) queryParams['halalStatus'] = halalStatus;

    final uri = Uri.parse('$baseUrl/products/popular').replace(
      queryParameters: queryParams,
    );

    final response = await http.get(uri, headers: _getHeaders());

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return List<Map<String, dynamic>>.from(data['products']);
    } else {
      throw Exception('Failed to get popular products: ${response.body}');
    }
  }

  // Brand Methods
  static Future<List<Map<String, dynamic>>> getBrands({
    int page = 1,
    int limit = 20,
    String sort = 'trustScore',
  }) async {
    final queryParams = {
      'page': page.toString(),
      'limit': limit.toString(),
      'sort': sort,
      'order': 'desc',
    };

    final uri = Uri.parse('$baseUrl/brands').replace(
      queryParameters: queryParams,
    );

    final response = await http.get(uri, headers: _getHeaders());

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return List<Map<String, dynamic>>.from(data['brands']);
    } else {
      throw Exception('Failed to get brands: ${response.body}');
    }
  }

  static Future<List<Map<String, dynamic>>> getTopHalalBrands({
    int limit = 10,
  }) async {
    final uri = Uri.parse('$baseUrl/brands/top-halal').replace(
      queryParameters: {'limit': limit.toString()},
    );

    final response = await http.get(uri, headers: _getHeaders());

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return List<Map<String, dynamic>>.from(data['brands']);
    } else {
      throw Exception('Failed to get top halal brands: ${response.body}');
    }
  }

  // AI Analysis Method (Database-first)
  static Future<Map<String, dynamic>> analyzeProduct({
    String? prompt,
    String? barcode,
    String? productName,
    String? brand,
    String type = 'halalAnalysis',
  }) async {
    final body = <String, dynamic>{
      'type': type,
    };

    if (prompt != null) body['prompt'] = prompt;
    if (barcode != null) body['barcode'] = barcode;
    if (productName != null) body['productName'] = productName;
    if (brand != null) body['brand'] = brand;

    final response = await http.post(
      Uri.parse('$baseUrl/analyze'),
      headers: _getHeaders(),
      body: jsonEncode(body),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Analysis failed: ${response.body}');
    }
  }

  // Subscription Methods
  static Future<List<Map<String, dynamic>>> getSubscriptionPlans() async {
    final response = await http.get(
      Uri.parse('$baseUrl/subscription/plans'),
      headers: _getHeaders(),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return List<Map<String, dynamic>>.from(data['plans']);
    } else {
      throw Exception('Failed to get subscription plans: ${response.body}');
    }
  }

  static Future<Map<String, dynamic>> getCurrentSubscription() async {
    final response = await http.get(
      Uri.parse('$baseUrl/subscription/current'),
      headers: _getHeaders(),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to get current subscription: ${response.body}');
    }
  }

  static Future<Map<String, dynamic>> getUsageStatistics() async {
    final response = await http.get(
      Uri.parse('$baseUrl/usage'),
      headers: _getHeaders(),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to get usage statistics: ${response.body}');
    }
  }

  // Apple In-App Purchase Methods
  static Future<List<Map<String, dynamic>>> getAppleIAPProducts() async {
    final response = await http.get(
      Uri.parse('$baseUrl/apple-iap/products'),
      headers: _getHeaders(),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return List<Map<String, dynamic>>.from(data['products'].values);
    } else {
      throw Exception('Failed to get Apple IAP products: ${response.body}');
    }
  }

  static Future<Map<String, dynamic>> verifyAppleReceipt({
    required String receiptData,
    bool isProduction = false,
  }) async {
    final response = await http.post(
      Uri.parse('$baseUrl/apple-iap/verify-receipt'),
      headers: _getHeaders(),
      body: jsonEncode({
        'receiptData': receiptData,
        'isProduction': isProduction,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Receipt verification failed: ${response.body}');
    }
  }

  static Future<Map<String, dynamic>> restoreApplePurchases({
    required String receiptData,
    bool isProduction = false,
  }) async {
    final response = await http.post(
      Uri.parse('$baseUrl/apple-iap/restore-purchases'),
      headers: _getHeaders(),
      body: jsonEncode({
        'receiptData': receiptData,
        'isProduction': isProduction,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Restore purchases failed: ${response.body}');
    }
  }

  // Rating and Review Methods
  static Future<void> rateProduct({
    required String productId,
    required int rating,
    String? review,
    String? halalConfirmation,
  }) async {
    final body = {
      'rating': rating,
    };
    
    if (review != null) body['review'] = review;
    if (halalConfirmation != null) body['halalConfirmation'] = halalConfirmation;

    final response = await http.post(
      Uri.parse('$baseUrl/products/$productId/rating'),
      headers: _getHeaders(),
      body: jsonEncode(body),
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to rate product: ${response.body}');
    }
  }

  static Future<void> reviewBrand({
    required String brandId,
    required int rating,
    String? review,
  }) async {
    final body = {
      'rating': rating,
    };
    
    if (review != null) body['review'] = review;

    final response = await http.post(
      Uri.parse('$baseUrl/brands/$brandId/review'),
      headers: _getHeaders(),
      body: jsonEncode(body),
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to review brand: ${response.body}');
    }
  }
}
