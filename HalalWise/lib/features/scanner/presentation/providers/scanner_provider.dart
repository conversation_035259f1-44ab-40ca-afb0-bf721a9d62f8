import 'dart:io';
import 'package:flutter/foundation.dart';
import '../../domain/entities/scan_result.dart';
import '../../domain/usecases/scan_barcode_usecase.dart';
import '../../domain/usecases/scan_label_usecase.dart';
import '../../../../core/services/service_locator.dart';

class ScannerProvider extends ChangeNotifier {
  final ScanBarcodeUseCase _scanBarcodeUseCase = sl<ScanBarcodeUseCase>();
  final ScanLabelUseCase _scanLabelUseCase = sl<ScanLabelUseCase>();

  bool _isLoading = false;
  String? _error;
  ScanResult? _lastResult;

  bool get isLoading => _isLoading;
  String? get error => _error;
  ScanResult? get lastResult => _lastResult;

  Future<ScanResult?> scanBarcode(String barcode) async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _scanBarcodeUseCase.execute(barcode);
      _lastResult = result;
      _setLoading(false);
      return result;
    } catch (e) {
      _setError('Failed to scan barcode: ${e.toString()}');
      _setLoading(false);
      return null;
    }
  }

  Future<ScanResult?> scanLabel(File imageFile) async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _scanLabelUseCase.execute(imageFile);
      _lastResult = result;
      _setLoading(false);
      return result;
    } catch (e) {
      _setError('Failed to scan label: ${e.toString()}');
      _setLoading(false);
      return null;
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void clearLastResult() {
    _lastResult = null;
    notifyListeners();
  }
}
