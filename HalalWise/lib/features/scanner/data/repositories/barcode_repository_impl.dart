import 'package:uuid/uuid.dart';
import '../../domain/repositories/barcode_repository.dart';
import '../../../food_analysis/domain/entities/food_item.dart';
import '../../../../core/database/database_helper.dart';
import '../../../../core/network/open_food_facts_service.dart';
import '../../../../core/network/ai_service.dart';

class BarcodeRepositoryImpl implements BarcodeRepository {
  final DatabaseHelper _databaseHelper;
  final OpenFoodFactsService _openFoodFactsService;
  final Uuid _uuid = const Uuid();

  BarcodeRepositoryImpl(this._databaseHelper, this._openFoodFactsService);

  @override
  Future<FoodItem?> getFoodItemByBarcode(String barcode) async {
    try {
      final productData = await _databaseHelper.getProductByBarcode(barcode);
      if (productData != null) {
        return _mapDatabaseToFoodItem(productData);
      }
      return null;
    } catch (e) {
      print('Error getting food item by barcode: $e');
      return null;
    }
  }

  @override
  Future<FoodItem?> fetchProductFromExternalAPI(String barcode) async {
    try {
      // Try Open Food Facts first
      final openFoodProduct = await _openFoodFactsService.getProductByBarcode(barcode);
      
      if (openFoodProduct != null) {
        return await _convertOpenFoodFactsToFoodItem(openFoodProduct);
      }
      
      return null;
    } catch (e) {
      print('Error fetching product from external API: $e');
      return null;
    }
  }

  @override
  Future<void> saveFoodItem(FoodItem foodItem) async {
    try {
      final productData = _mapFoodItemToDatabase(foodItem);
      await _databaseHelper.insertProduct(productData);
    } catch (e) {
      print('Error saving food item: $e');
      rethrow;
    }
  }

  @override
  Future<List<FoodItem>> searchFoodItems(String query) async {
    try {
      final results = await _databaseHelper.searchProducts(query);
      return results.map((data) => _mapDatabaseToFoodItem(data)).toList();
    } catch (e) {
      print('Error searching food items: $e');
      return [];
    }
  }

  Future<FoodItem> _convertOpenFoodFactsToFoodItem(OpenFoodFactsProduct product) async {
    // Analyze halal status using AI if needed
    HalalStatus halalStatus = HalalStatus.uncertain;
    double halalConfidence = 0.0;
    String halalReason = 'Analysis pending';

    // Quick check for obvious halal/haram indicators
    if (product.hasHalalLabel) {
      halalStatus = HalalStatus.halal;
      halalConfidence = 0.9;
      halalReason = 'Product has halal certification label';
    } else if (product.hasNonHalalIngredients) {
      halalStatus = HalalStatus.haram;
      halalConfidence = 0.95;
      halalReason = 'Contains non-halal ingredients';
    } else if (product.questionableIngredients.isNotEmpty) {
      halalStatus = HalalStatus.questionable;
      halalConfidence = 0.7;
      halalReason = 'Contains questionable ingredients: ${product.questionableIngredients.join(', ')}';
    }

    // Calculate basic health score
    double healthScore = _calculateBasicHealthScore(product);

    return FoodItem(
      id: _uuid.v4(),
      name: product.productName ?? 'Unknown Product',
      brand: product.brands,
      barcode: product.barcode,
      ingredients: product.ingredients?.split(',').map((e) => e.trim()).toList() ?? [],
      halalStatus: halalStatus,
      halalConfidence: halalConfidence,
      halalReason: halalReason,
      healthScore: healthScore,
      allergens: product.allergenList,
      certifications: product.hasHalalLabel ? ['Halal Certified'] : [],
      nutritionalInfo: NutritionalInfo(
        calories: product.calories,
        protein: product.protein,
        carbohydrates: product.carbohydrates,
        sugar: product.sugar,
        fat: product.fat,
        saturatedFat: product.saturatedFat,
        fiber: product.fiber,
        sodium: product.sodium,
      ),
      imageUrl: product.imageUrl,
      source: 'Open Food Facts',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  double _calculateBasicHealthScore(OpenFoodFactsProduct product) {
    double score = 5.0; // Start with neutral score
    
    // Adjust based on nutritional content
    if (product.sugar != null) {
      if (product.sugar! > 20) score -= 2;
      else if (product.sugar! > 10) score -= 1;
      else if (product.sugar! < 5) score += 1;
    }
    
    if (product.sodium != null) {
      if (product.sodium! > 1.5) score -= 2; // High sodium
      else if (product.sodium! > 0.6) score -= 1;
      else if (product.sodium! < 0.3) score += 1;
    }
    
    if (product.saturatedFat != null) {
      if (product.saturatedFat! > 5) score -= 1;
      else if (product.saturatedFat! < 1.5) score += 0.5;
    }
    
    if (product.fiber != null && product.fiber! > 3) {
      score += 1; // Good fiber content
    }
    
    if (product.protein != null && product.protein! > 10) {
      score += 0.5; // Good protein content
    }
    
    // Ensure score is between 1 and 10
    return score.clamp(1.0, 10.0);
  }

  FoodItem _mapDatabaseToFoodItem(Map<String, dynamic> data) {
    return FoodItem(
      id: data['id'].toString(),
      name: data['name'] as String,
      brand: data['brand'] as String?,
      barcode: data['barcode'] as String?,
      ingredients: (data['ingredients'] as String?)?.split(',').map((e) => e.trim()).toList() ?? [],
      halalStatus: _parseHalalStatus(data['halal_status'] as String),
      halalConfidence: (data['halal_confidence'] as num?)?.toDouble() ?? 0.0,
      halalReason: data['halal_reason'] as String? ?? '',
      healthScore: (data['health_score'] as num?)?.toDouble() ?? 5.0,
      allergens: (data['allergens'] as String?)?.split(',').map((e) => e.trim()).toList() ?? [],
      certifications: (data['certifications'] as String?)?.split(',').map((e) => e.trim()).toList() ?? [],
      nutritionalInfo: NutritionalInfo(
        calories: (data['calories'] as num?)?.toDouble(),
        protein: (data['protein'] as num?)?.toDouble(),
        sugar: (data['sugar'] as num?)?.toDouble(),
        sodium: (data['sodium'] as num?)?.toDouble(),
        saturatedFat: (data['saturated_fat'] as num?)?.toDouble(),
        fiber: (data['fiber'] as num?)?.toDouble(),
      ),
      imageUrl: data['image_url'] as String?,
      source: data['source'] as String? ?? 'database',
      createdAt: DateTime.fromMillisecondsSinceEpoch(data['created_at'] as int),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(data['updated_at'] as int),
    );
  }

  Map<String, dynamic> _mapFoodItemToDatabase(FoodItem foodItem) {
    return {
      'barcode': foodItem.barcode,
      'name': foodItem.name,
      'brand': foodItem.brand,
      'ingredients': foodItem.ingredients.join(', '),
      'halal_status': foodItem.halalStatus.toString().split('.').last,
      'halal_confidence': foodItem.halalConfidence,
      'halal_reason': foodItem.halalReason,
      'health_score': foodItem.healthScore,
      'calories': foodItem.nutritionalInfo?.calories,
      'sugar': foodItem.nutritionalInfo?.sugar,
      'sodium': foodItem.nutritionalInfo?.sodium,
      'saturated_fat': foodItem.nutritionalInfo?.saturatedFat,
      'fiber': foodItem.nutritionalInfo?.fiber,
      'protein': foodItem.nutritionalInfo?.protein,
      'allergens': foodItem.allergens.join(', '),
      'certifications': foodItem.certifications.join(', '),
      'image_url': foodItem.imageUrl,
      'source': foodItem.source,
    };
  }

  HalalStatus _parseHalalStatus(String status) {
    switch (status.toLowerCase()) {
      case 'halal':
        return HalalStatus.halal;
      case 'haram':
        return HalalStatus.haram;
      case 'questionable':
        return HalalStatus.questionable;
      default:
        return HalalStatus.uncertain;
    }
  }
}
