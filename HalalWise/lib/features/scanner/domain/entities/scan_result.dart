import '../../../food_analysis/domain/entities/food_item.dart';

class ScanResult {
  final String id;
  final ScanType scanType;
  final String scanData;
  final FoodItem foodItem;
  final DateTime timestamp;
  final String? userFeedback;

  ScanResult({
    required this.id,
    required this.scanType,
    required this.scanData,
    required this.foodItem,
    required this.timestamp,
    this.userFeedback,
  });

  ScanResult copyWith({
    String? id,
    ScanType? scanType,
    String? scanData,
    FoodItem? foodItem,
    DateTime? timestamp,
    String? userFeedback,
  }) {
    return ScanResult(
      id: id ?? this.id,
      scanType: scanType ?? this.scanType,
      scanData: scanData ?? this.scanData,
      foodItem: foodItem ?? this.foodItem,
      timestamp: timestamp ?? this.timestamp,
      userFeedback: userFeedback ?? this.userFeedback,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'scan_type': scanType.toString(),
      'scan_data': scanData,
      'food_item': foodItem.toJson(),
      'timestamp': timestamp.millisecondsSinceEpoch,
      'user_feedback': userFeedback,
    };
  }

  factory ScanResult.fromJson(Map<String, dynamic> json) {
    return ScanResult(
      id: json['id'] as String,
      scanType: ScanType.values.firstWhere(
        (e) => e.toString() == json['scan_type'],
        orElse: () => ScanType.manual,
      ),
      scanData: json['scan_data'] as String,
      foodItem: FoodItem.fromJson(json['food_item'] as Map<String, dynamic>),
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp'] as int),
      userFeedback: json['user_feedback'] as String?,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ScanResult &&
        other.id == id &&
        other.scanType == scanType &&
        other.scanData == scanData &&
        other.foodItem == foodItem &&
        other.timestamp == timestamp &&
        other.userFeedback == userFeedback;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        scanType.hashCode ^
        scanData.hashCode ^
        foodItem.hashCode ^
        timestamp.hashCode ^
        userFeedback.hashCode;
  }

  @override
  String toString() {
    return 'ScanResult(id: $id, scanType: $scanType, scanData: $scanData, foodItem: $foodItem, timestamp: $timestamp, userFeedback: $userFeedback)';
  }
}

enum ScanType {
  barcode,
  label,
  manual,
  voice,
}

extension ScanTypeExtension on ScanType {
  String get displayName {
    switch (this) {
      case ScanType.barcode:
        return 'Barcode Scan';
      case ScanType.label:
        return 'Label Scan';
      case ScanType.manual:
        return 'Manual Input';
      case ScanType.voice:
        return 'Voice Input';
    }
  }

  String get icon {
    switch (this) {
      case ScanType.barcode:
        return '📱';
      case ScanType.label:
        return '📷';
      case ScanType.manual:
        return '✏️';
      case ScanType.voice:
        return '🎤';
    }
  }
}
