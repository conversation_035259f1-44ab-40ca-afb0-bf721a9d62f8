import 'package:uuid/uuid.dart';
import '../entities/scan_result.dart';
import '../repositories/barcode_repository.dart';
import '../../../food_analysis/domain/entities/food_item.dart';
import '../../../../core/network/ai_service.dart';

class ScanBarcodeUseCase {
  final BarcodeRepository _repository;
  final Uuid _uuid = const Uuid();

  ScanBarcodeUseCase(this._repository);

  Future<ScanResult> execute(String barcode) async {
    try {
      // First, check local database
      FoodItem? foodItem = await _repository.getFoodItemByBarcode(barcode);
      
      if (foodItem != null) {
        // Found in local database
        return ScanResult(
          id: _uuid.v4(),
          scanType: ScanType.barcode,
          scanData: barcode,
          foodItem: foodItem,
          timestamp: DateTime.now(),
        );
      }

      // If not found locally, try external APIs
      foodItem = await _repository.fetchProductFromExternalAPI(barcode);
      
      if (foodItem != null) {
        // Save to local database for future use
        await _repository.saveFoodItem(foodItem);
        
        return ScanResult(
          id: _uuid.v4(),
          scanType: ScanType.barcode,
          scanData: barcode,
          foodItem: foodItem,
          timestamp: DateTime.now(),
        );
      }

      // If still not found, return error
      throw Exception('Product not found for barcode: $barcode');
      
    } catch (e) {
      throw Exception('Failed to scan barcode: ${e.toString()}');
    }
  }
}
