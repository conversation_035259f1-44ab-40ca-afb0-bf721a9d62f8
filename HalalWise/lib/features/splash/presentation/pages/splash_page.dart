import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../../../core/theme/app_theme.dart';
import '../../../home/<USER>/pages/home_page.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _textController;
  late AnimationController _fadeController;

  late Animation<double> _logoScaleAnimation;
  late Animation<double> _logoRotationAnimation;
  late Animation<double> _textFadeAnimation;
  late Animation<Offset> _textSlideAnimation;
  late Animation<double> _backgroundFadeAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startAnimationSequence();
  }

  void _setupAnimations() {
    // Logo animations
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _logoScaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    _logoRotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.easeInOut,
    ));

    // Text animations
    _textController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _textFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeIn,
    ));

    _textSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeOutCubic,
    ));

    // Background fade
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _backgroundFadeAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
  }

  void _startAnimationSequence() async {
    // Start logo animation
    await _logoController.forward();

    // Wait a bit, then start text animation
    await Future.delayed(const Duration(milliseconds: 300));
    await _textController.forward();

    // Wait for user to see the splash, then fade out and navigate
    await Future.delayed(const Duration(milliseconds: 1500));
    await _fadeController.forward();

    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const HomePage()),
      );
    }
  }

  @override
  void dispose() {
    _logoController.dispose();
    _textController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AnimatedBuilder(
        animation: _backgroundFadeAnimation,
        builder: (context, child) {
          return Opacity(
            opacity: _backgroundFadeAnimation.value,
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppTheme.primaryGreen,
                    AppTheme.lightGreen,
                    AppTheme.accentGold.withOpacity(0.3),
                  ],
                ),
              ),
              child: SafeArea(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Logo
                      AnimatedBuilder(
                        animation: _logoController,
                        builder: (context, child) {
                          return Transform.scale(
                            scale: _logoScaleAnimation.value,
                            child: Transform.rotate(
                              angle: _logoRotationAnimation.value * 0.1,
                              child: _buildLogo(),
                            ),
                          );
                        },
                      ),

                      const SizedBox(height: 40),

                      // App Name and Tagline
                      AnimatedBuilder(
                        animation: _textController,
                        builder: (context, child) {
                          return FadeTransition(
                            opacity: _textFadeAnimation,
                            child: SlideTransition(
                              position: _textSlideAnimation,
                              child: Column(
                                children: [
                                  // App Name
                                  const Text(
                                    'HalalWise',
                                    style: TextStyle(
                                      fontSize: 36,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                      letterSpacing: 1.2,
                                    ),
                                  ),

                                  const SizedBox(height: 8),

                                  // Arabic Name
                                  const Text(
                                    'حلال وايز',
                                    style: TextStyle(
                                      fontSize: 24,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.white,
                                      letterSpacing: 1.0,
                                    ),
                                  ),

                                  const SizedBox(height: 16),

                                  // Tagline
                                  Text(
                                    'Smart Halal Food Verification',
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: Colors.white.withOpacity(0.9),
                                      letterSpacing: 0.5,
                                    ),
                                  ),

                                  const SizedBox(height: 8),

                                  // Arabic Tagline
                                  Text(
                                    'التحقق الذكي من الطعام الحلال',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.white.withOpacity(0.8),
                                      letterSpacing: 0.5,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),

                      const SizedBox(height: 60),

                      // Loading indicator
                      AnimatedBuilder(
                        animation: _textController,
                        builder: (context, child) {
                          return FadeTransition(
                            opacity: _textFadeAnimation,
                            child: Column(
                              children: [
                                SizedBox(
                                  width: 30,
                                  height: 30,
                                  child: CircularProgressIndicator(
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white.withOpacity(0.8),
                                    ),
                                    strokeWidth: 3,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'Initializing...',
                                  style: TextStyle(
                                    color: Colors.white.withOpacity(0.7),
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildLogo() {
    return Container(
      width: 120,
      height: 120,
      child: CustomPaint(
        painter: HalalWiseLogoPainter(),
      ),
    );
  }
}

class HalalWiseLogoPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.4;

    // Create gradient paint for the crescent
    final crescentPaint = Paint()
      ..shader = LinearGradient(
        colors: [
          AppTheme.accentGold,
          AppTheme.accentGold.withOpacity(0.8),
        ],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ).createShader(Rect.fromCircle(center: center, radius: radius))
      ..style = PaintingStyle.fill;

    // Create the crescent moon shape
    final crescentPath = Path();

    // Outer arc (main crescent)
    crescentPath.addArc(
      Rect.fromCircle(center: center, radius: radius),
      -math.pi / 2,
      math.pi,
    );

    // Inner arc (to create crescent shape)
    final innerCenter = Offset(center.dx + radius * 0.3, center.dy);
    crescentPath.addArc(
      Rect.fromCircle(center: innerCenter, radius: radius * 0.7),
      math.pi / 2,
      math.pi,
    );

    crescentPath.fillType = PathFillType.evenOdd;
    canvas.drawPath(crescentPath, crescentPaint);

    // Add geometric pattern inside the crescent
    _drawGeometricPattern(canvas, center, radius * 0.6);

    // Add Arabic calligraphy-inspired text "حلال"
    _drawArabicText(canvas, center, radius);
  }

  void _drawGeometricPattern(Canvas canvas, Offset center, double radius) {
    final patternPaint = Paint()
      ..color = Colors.white.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;

    // Draw interconnected lines creating an Islamic geometric pattern
    final points = <Offset>[];
    const numPoints = 8;

    for (int i = 0; i < numPoints; i++) {
      final angle = (i * 2 * math.pi) / numPoints;
      final x = center.dx + radius * 0.5 * math.cos(angle);
      final y = center.dy + radius * 0.5 * math.sin(angle);
      points.add(Offset(x, y));
    }

    // Connect points to create star pattern
    for (int i = 0; i < numPoints; i++) {
      for (int j = i + 2; j < numPoints; j++) {
        if (j - i != numPoints / 2) {
          canvas.drawLine(points[i], points[j], patternPaint);
        }
      }
    }

    // Draw center circle
    canvas.drawCircle(center, radius * 0.15, patternPaint);
  }

  void _drawArabicText(Canvas canvas, Offset center, double radius) {
    // Draw "حلال" in a stylized way using simple shapes
    final textPaint = Paint()
      ..color = Colors.white.withOpacity(0.9)
      ..style = PaintingStyle.fill;

    // Simple representation of Arabic "حلال" using geometric shapes
    final textPath = Path();

    // First letter approximation
    textPath.addRRect(RRect.fromRectAndRadius(
      Rect.fromCenter(
        center: Offset(center.dx - radius * 0.3, center.dy + radius * 0.8),
        width: radius * 0.2,
        height: radius * 0.15,
      ),
      const Radius.circular(2),
    ));

    // Second letter approximation
    textPath.addRRect(RRect.fromRectAndRadius(
      Rect.fromCenter(
        center: Offset(center.dx - radius * 0.1, center.dy + radius * 0.8),
        width: radius * 0.15,
        height: radius * 0.12,
      ),
      const Radius.circular(2),
    ));

    // Third letter approximation
    textPath.addRRect(RRect.fromRectAndRadius(
      Rect.fromCenter(
        center: Offset(center.dx + radius * 0.1, center.dy + radius * 0.8),
        width: radius * 0.15,
        height: radius * 0.12,
      ),
      const Radius.circular(2),
    ));

    // Fourth letter approximation
    textPath.addRRect(RRect.fromRectAndRadius(
      Rect.fromCenter(
        center: Offset(center.dx + radius * 0.3, center.dy + radius * 0.8),
        width: radius * 0.2,
        height: radius * 0.15,
      ),
      const Radius.circular(2),
    ));

    canvas.drawPath(textPath, textPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
