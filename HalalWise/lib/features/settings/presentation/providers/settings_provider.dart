import 'package:flutter/material.dart';

class SettingsProvider extends ChangeNotifier {
  ThemeMode _themeMode = ThemeMode.system;
  Locale _locale = const Locale('en');
  bool _isRTL = false;

  ThemeMode get themeMode => _themeMode;
  Locale get locale => _locale;
  bool get isRTL => _isRTL;

  void setThemeMode(ThemeMode mode) {
    _themeMode = mode;
    notifyListeners();
  }

  void setLocale(Locale locale) {
    _locale = locale;
    _isRTL = locale.languageCode == 'ar' || 
             locale.languageCode == 'ur' || 
             locale.languageCode == 'fa';
    notifyListeners();
  }

  void toggleRTL() {
    _isRTL = !_isRTL;
    notifyListeners();
  }
}
