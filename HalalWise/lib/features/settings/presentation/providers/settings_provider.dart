import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SettingsProvider extends ChangeNotifier {
  ThemeMode _themeMode = ThemeMode.system;
  Locale _locale = const Locale('en', 'US');
  bool _isRTL = false;

  // Notification settings
  bool _notificationsEnabled = true;
  bool _notificationSoundEnabled = true;
  bool _notificationVibrationEnabled = true;
  bool _scanResultNotificationsEnabled = true;
  bool _haramAlertNotificationsEnabled = true;
  bool _questionableIngredientNotificationsEnabled = true;
  bool _updateNotificationsEnabled = true;
  bool _databaseUpdateNotificationsEnabled = true;

  // Getters
  ThemeMode get themeMode => _themeMode;
  Locale get locale => _locale;
  Locale? get currentLocale => _locale;
  bool get isRTL => _isRTL;

  // Notification getters
  bool get notificationsEnabled => _notificationsEnabled;
  bool get notificationSoundEnabled => _notificationSoundEnabled;
  bool get notificationVibrationEnabled => _notificationVibrationEnabled;
  bool get scanResultNotificationsEnabled => _scanResultNotificationsEnabled;
  bool get haramAlertNotificationsEnabled => _haramAlertNotificationsEnabled;
  bool get questionableIngredientNotificationsEnabled => _questionableIngredientNotificationsEnabled;
  bool get updateNotificationsEnabled => _updateNotificationsEnabled;
  bool get databaseUpdateNotificationsEnabled => _databaseUpdateNotificationsEnabled;

  // Theme methods
  void setThemeMode(ThemeMode mode) {
    _themeMode = mode;
    _saveThemeMode(mode);
    notifyListeners();
  }

  // Locale methods
  Future<void> setLocale(Locale locale) async {
    _locale = locale;
    _isRTL = ['ar', 'ur', 'fa', 'he'].contains(locale.languageCode);
    await _saveLocale(_locale);
    notifyListeners();
  }

  void toggleRTL() {
    _isRTL = !_isRTL;
    notifyListeners();
  }

  // Notification methods
  void setNotificationsEnabled(bool enabled) {
    _notificationsEnabled = enabled;
    _saveNotificationSetting('notifications_enabled', enabled);
    notifyListeners();
  }

  void setNotificationSoundEnabled(bool enabled) {
    _notificationSoundEnabled = enabled;
    _saveNotificationSetting('notification_sound_enabled', enabled);
    notifyListeners();
  }

  void setNotificationVibrationEnabled(bool enabled) {
    _notificationVibrationEnabled = enabled;
    _saveNotificationSetting('notification_vibration_enabled', enabled);
    notifyListeners();
  }

  void setScanResultNotificationsEnabled(bool enabled) {
    _scanResultNotificationsEnabled = enabled;
    _saveNotificationSetting('scan_result_notifications_enabled', enabled);
    notifyListeners();
  }

  void setHaramAlertNotificationsEnabled(bool enabled) {
    _haramAlertNotificationsEnabled = enabled;
    _saveNotificationSetting('haram_alert_notifications_enabled', enabled);
    notifyListeners();
  }

  void setQuestionableIngredientNotificationsEnabled(bool enabled) {
    _questionableIngredientNotificationsEnabled = enabled;
    _saveNotificationSetting('questionable_ingredient_notifications_enabled', enabled);
    notifyListeners();
  }

  void setUpdateNotificationsEnabled(bool enabled) {
    _updateNotificationsEnabled = enabled;
    _saveNotificationSetting('update_notifications_enabled', enabled);
    notifyListeners();
  }

  void setDatabaseUpdateNotificationsEnabled(bool enabled) {
    _databaseUpdateNotificationsEnabled = enabled;
    _saveNotificationSetting('database_update_notifications_enabled', enabled);
    notifyListeners();
  }

  // Initialization method
  Future<void> loadSettings() async {
    final prefs = await SharedPreferences.getInstance();

    // Load theme
    final themeIndex = prefs.getInt('theme_mode') ?? ThemeMode.system.index;
    _themeMode = ThemeMode.values[themeIndex];

    // Load locale
    final languageCode = prefs.getString('language_code') ?? 'en';
    final countryCode = prefs.getString('country_code') ?? 'US';
    _locale = Locale(languageCode, countryCode);
    _isRTL = ['ar', 'ur', 'fa', 'he'].contains(languageCode);

    // Load notification settings
    _notificationsEnabled = prefs.getBool('notifications_enabled') ?? true;
    _notificationSoundEnabled = prefs.getBool('notification_sound_enabled') ?? true;
    _notificationVibrationEnabled = prefs.getBool('notification_vibration_enabled') ?? true;
    _scanResultNotificationsEnabled = prefs.getBool('scan_result_notifications_enabled') ?? true;
    _haramAlertNotificationsEnabled = prefs.getBool('haram_alert_notifications_enabled') ?? true;
    _questionableIngredientNotificationsEnabled = prefs.getBool('questionable_ingredient_notifications_enabled') ?? true;
    _updateNotificationsEnabled = prefs.getBool('update_notifications_enabled') ?? true;
    _databaseUpdateNotificationsEnabled = prefs.getBool('database_update_notifications_enabled') ?? true;

    notifyListeners();
  }

  // Private persistence methods
  Future<void> _saveThemeMode(ThemeMode mode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('theme_mode', mode.index);
  }

  Future<void> _saveLocale(Locale locale) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('language_code', locale.languageCode);
    if (locale.countryCode != null) {
      await prefs.setString('country_code', locale.countryCode!);
    }
  }

  Future<void> _saveNotificationSetting(String key, bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(key, value);
  }
}
