import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../l10n/app_localizations.dart';

class PrivacyPolicyPage extends StatelessWidget {
  const PrivacyPolicyPage({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.privacyPolicy),
        backgroundColor: AppTheme.primaryGreen,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.primaryGreen.withOpacity(0.1),
              Colors.white,
            ],
          ),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeaderCard(context),
              const SizedBox(height: 24),
              _buildContentCard(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderCard(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              AppTheme.primaryGreen.withOpacity(0.1),
              AppTheme.primaryGreen.withOpacity(0.05),
            ],
          ),
        ),
        child: Column(
          children: [
            Icon(
              Icons.privacy_tip,
              size: 48,
              color: AppTheme.primaryGreen,
            ),
            const SizedBox(height: 12),
            Text(
              'Privacy Policy',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryGreen,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Last updated: December 2024',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentCard(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSection(
              context,
              title: '1. Information We Collect',
              content: '''
HalalWise is committed to protecting your privacy. We collect minimal information necessary to provide our halal food verification service:

• Product scan data (barcodes, ingredient lists)
• Usage analytics (anonymized)
• Device information for app optimization
• Crash reports for bug fixes

We do NOT collect:
• Personal identification information
• Location data
• Contact information
• Payment information
''',
            ),
            
            _buildSection(
              context,
              title: '2. How We Use Your Information',
              content: '''
Your data is used exclusively to:

• Provide halal verification services
• Improve our ingredient database
• Enhance app performance and reliability
• Generate anonymized usage statistics

We never sell, rent, or share your personal data with third parties for marketing purposes.
''',
            ),
            
            _buildSection(
              context,
              title: '3. Data Storage and Security',
              content: '''
• All scan history is stored locally on your device
• Cloud backup is optional and encrypted
• We use industry-standard security measures
• Data transmission is encrypted using TLS
• Regular security audits are conducted
''',
            ),
            
            _buildSection(
              context,
              title: '4. Third-Party Services',
              content: '''
HalalWise integrates with:

• Google Gemini AI for ingredient analysis
• Open Food Facts for product information
• Firebase for optional cloud features

These services have their own privacy policies. We ensure they meet our privacy standards.
''',
            ),
            
            _buildSection(
              context,
              title: '5. Your Rights',
              content: '''
You have the right to:

• Access your data
• Delete your data
• Export your scan history
• Opt-out of analytics
• Request data correction

Contact <NAME_EMAIL> for any privacy-related requests.
''',
            ),
            
            _buildSection(
              context,
              title: '6. Children\'s Privacy',
              content: '''
HalalWise is safe for all ages. We do not knowingly collect personal information from children under 13. If you believe we have collected such information, please contact us immediately.
''',
            ),
            
            _buildSection(
              context,
              title: '7. Changes to This Policy',
              content: '''
We may update this privacy policy to reflect changes in our practices or legal requirements. We will notify users of significant changes through the app.
''',
            ),
            
            _buildSection(
              context,
              title: '8. Contact Us',
              content: '''
If you have questions about this privacy policy or our data practices, please contact us:

Email: <EMAIL>
Website: www.halalwise.app
Address: [Your Company Address]

We are committed to addressing your privacy concerns promptly and transparently.
''',
            ),
            
            const SizedBox(height: 24),
            
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.successGreen.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppTheme.successGreen.withOpacity(0.3)),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.verified_user,
                    color: AppTheme.successGreen,
                    size: 32,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Privacy Commitment',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.successGreen,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'HalalWise is built with privacy by design. Your trust is essential to our mission of providing reliable halal verification.',
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey[700],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(BuildContext context, {
    required String title,
    required String content,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryGreen,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            content,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              height: 1.6,
              color: Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }
}
