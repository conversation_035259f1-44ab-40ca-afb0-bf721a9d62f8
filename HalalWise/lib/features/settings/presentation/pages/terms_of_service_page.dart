import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../l10n/app_localizations.dart';

class TermsOfServicePage extends StatelessWidget {
  const TermsOfServicePage({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.termsOfService),
        backgroundColor: AppTheme.primaryGreen,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.primaryGreen.withOpacity(0.1),
              Colors.white,
            ],
          ),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeaderCard(context),
              const SizedBox(height: 24),
              _buildContentCard(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderCard(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              AppTheme.primaryGreen.withOpacity(0.1),
              AppTheme.primaryGreen.withOpacity(0.05),
            ],
          ),
        ),
        child: Column(
          children: [
            Icon(
              Icons.description,
              size: 48,
              color: AppTheme.primaryGreen,
            ),
            const SizedBox(height: 12),
            Text(
              'Terms of Service',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryGreen,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Last updated: December 2024',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentCard(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSection(
              context,
              title: '1. Acceptance of Terms',
              content: '''
By downloading, installing, or using HalalWise, you agree to be bound by these Terms of Service. If you do not agree to these terms, please do not use our application.

These terms constitute a legally binding agreement between you and HalalWise.
''',
            ),
            
            _buildSection(
              context,
              title: '2. Description of Service',
              content: '''
HalalWise is a mobile application that provides:

• Halal food verification through barcode scanning
• Ingredient analysis using AI technology
• Local database of halal/haram ingredients
• Health and nutritional information
• Scan history and personal preferences

Our service is provided "as is" and we strive for accuracy but cannot guarantee 100% precision.
''',
            ),
            
            _buildSection(
              context,
              title: '3. User Responsibilities',
              content: '''
As a user, you agree to:

• Use the app for lawful purposes only
• Not attempt to reverse engineer or hack the application
• Not share false or misleading information
• Respect intellectual property rights
• Use the service in accordance with Islamic principles

You are responsible for your own dietary decisions based on the information provided.
''',
            ),
            
            _buildSection(
              context,
              title: '4. Accuracy and Limitations',
              content: '''
Important Disclaimers:

• HalalWise provides information for guidance only
• We cannot guarantee 100% accuracy of halal status
• Users should verify with certified halal authorities when in doubt
• The app is not a substitute for official halal certification
• Regional halal standards may vary

Always consult with religious authorities for definitive rulings.
''',
            ),
            
            _buildSection(
              context,
              title: '5. Intellectual Property',
              content: '''
HalalWise and its content are protected by intellectual property laws:

• The app, design, and code are owned by HalalWise
• User-generated content remains your property
• You grant us license to use your feedback for improvements
• Third-party content is used under appropriate licenses

Unauthorized copying or distribution is prohibited.
''',
            ),
            
            _buildSection(
              context,
              title: '6. Privacy and Data',
              content: '''
Your privacy is important to us:

• We collect minimal data necessary for service provision
• Scan history is stored locally on your device
• We do not sell personal information to third parties
• Please review our Privacy Policy for detailed information

You can delete your data at any time through the app settings.
''',
            ),
            
            _buildSection(
              context,
              title: '7. Limitation of Liability',
              content: '''
HalalWise shall not be liable for:

• Dietary decisions made based on app information
• Damages resulting from app use or inability to use
• Third-party content or services
• Technical issues or service interruptions
• Loss of data or information

Use the app at your own discretion and risk.
''',
            ),
            
            _buildSection(
              context,
              title: '8. Termination',
              content: '''
We may terminate or suspend your access to HalalWise:

• For violation of these terms
• For illegal or harmful activities
• At our sole discretion with or without notice

You may stop using the app at any time by uninstalling it.
''',
            ),
            
            _buildSection(
              context,
              title: '9. Changes to Terms',
              content: '''
We reserve the right to modify these terms at any time:

• Changes will be posted in the app
• Continued use constitutes acceptance of new terms
• Significant changes will be highlighted to users

Please review terms periodically for updates.
''',
            ),
            
            _buildSection(
              context,
              title: '10. Contact Information',
              content: '''
For questions about these Terms of Service:

Email: <EMAIL>
Website: www.halalwise.app
Address: [Your Company Address]

We welcome your feedback and questions about our terms.
''',
            ),
            
            const SizedBox(height: 24),
            
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.accentGold.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppTheme.accentGold.withOpacity(0.3)),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.balance,
                    color: AppTheme.accentGold,
                    size: 32,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Islamic Compliance',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.accentGold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'HalalWise is developed with Islamic principles in mind. We strive to provide accurate information while respecting diverse scholarly opinions on halal matters.',
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey[700],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(BuildContext context, {
    required String title,
    required String content,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryGreen,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            content,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              height: 1.6,
              color: Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }
}
