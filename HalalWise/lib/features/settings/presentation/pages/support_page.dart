import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../l10n/app_localizations.dart';

class SupportPage extends StatelessWidget {
  const SupportPage({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.support),
        backgroundColor: AppTheme.primaryGreen,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.primaryGreen.withOpacity(0.1),
              Colors.white,
            ],
          ),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeaderCard(context),
              const SizedBox(height: 24),
              _buildQuickHelpSection(context),
              const SizedBox(height: 20),
              _buildContactSection(context),
              const SizedBox(height: 20),
              _buildFAQSection(context),
              const SizedBox(height: 20),
              _buildFeedbackSection(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderCard(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              AppTheme.primaryGreen.withOpacity(0.1),
              AppTheme.primaryGreen.withOpacity(0.05),
            ],
          ),
        ),
        child: Column(
          children: [
            Icon(
              Icons.support_agent,
              size: 48,
              color: AppTheme.primaryGreen,
            ),
            const SizedBox(height: 12),
            Text(
              'How can we help you?',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryGreen,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'We\'re here to assist you with any questions or issues',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickHelpSection(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.flash_on, color: AppTheme.primaryGreen, size: 24),
                const SizedBox(width: 12),
                Text(
                  'Quick Help',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildQuickHelpItem(
              context,
              icon: Icons.qr_code_scanner,
              title: 'Scanning Issues',
              subtitle: 'Camera not working or barcode not recognized',
              onTap: () => _showScanningHelp(context),
            ),
            _buildQuickHelpItem(
              context,
              icon: Icons.help_outline,
              title: 'Understanding Results',
              subtitle: 'How to interpret halal verification results',
              onTap: () => _showResultsHelp(context),
            ),
            _buildQuickHelpItem(
              context,
              icon: Icons.language,
              title: 'Language & Settings',
              subtitle: 'Change language, theme, and preferences',
              onTap: () => _showSettingsHelp(context),
            ),
            _buildQuickHelpItem(
              context,
              icon: Icons.bug_report,
              title: 'Report a Bug',
              subtitle: 'Found an issue? Let us know',
              onTap: () => _showBugReport(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickHelpItem(BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppTheme.primaryGreen.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: AppTheme.primaryGreen, size: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey[400]),
          ],
        ),
      ),
    );
  }

  Widget _buildContactSection(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.contact_support, color: AppTheme.primaryGreen, size: 24),
                const SizedBox(width: 12),
                Text(
                  'Contact Us',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildContactItem(
              context,
              icon: Icons.email,
              title: 'Email Support',
              subtitle: '<EMAIL>',
              onTap: () => _copyToClipboard(context, '<EMAIL>'),
            ),
            _buildContactItem(
              context,
              icon: Icons.web,
              title: 'Website',
              subtitle: 'www.halalwise.app',
              onTap: () => _copyToClipboard(context, 'www.halalwise.app'),
            ),
            _buildContactItem(
              context,
              icon: Icons.schedule,
              title: 'Response Time',
              subtitle: 'Usually within 24 hours',
              onTap: null,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactItem(BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            Icon(icon, color: AppTheme.primaryGreen, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
            if (onTap != null)
              Icon(Icons.copy, size: 16, color: Colors.grey[400]),
          ],
        ),
      ),
    );
  }

  Widget _buildFAQSection(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.quiz, color: AppTheme.primaryGreen, size: 24),
                const SizedBox(width: 12),
                Text(
                  'Frequently Asked Questions',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildFAQItem(
              context,
              question: 'How accurate is the halal verification?',
              answer: 'Our AI analyzes ingredients against a comprehensive database of halal/haram substances. While highly accurate, we recommend consulting with religious authorities for definitive rulings.',
            ),
            _buildFAQItem(
              context,
              question: 'Can I use the app offline?',
              answer: 'Yes! HalalWise works offline using a local database. Online features like AI analysis require internet connection.',
            ),
            _buildFAQItem(
              context,
              question: 'How do I report incorrect information?',
              answer: 'Use the feedback feature in the app or contact our support team. We review all reports and update our database accordingly.',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFAQItem(BuildContext context, {
    required String question,
    required String answer,
  }) {
    return ExpansionTile(
      title: Text(
        question,
        style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 14),
      ),
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          child: Text(
            answer,
            style: TextStyle(color: Colors.grey[700], height: 1.4),
          ),
        ),
      ],
    );
  }

  Widget _buildFeedbackSection(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.feedback, color: AppTheme.primaryGreen, size: 24),
                const SizedBox(width: 12),
                Text(
                  'Send Feedback',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Help us improve HalalWise by sharing your thoughts and suggestions.',
              style: TextStyle(color: Colors.grey[700]),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _showFeedbackDialog(context),
                icon: const Icon(Icons.send),
                label: const Text('Send Feedback'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryGreen,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _copyToClipboard(BuildContext context, String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Copied to clipboard: $text'),
        backgroundColor: AppTheme.successGreen,
      ),
    );
  }

  void _showScanningHelp(BuildContext context) {
    _showHelpDialog(context, 'Scanning Help', '''
• Ensure good lighting when scanning
• Hold the camera steady over the barcode
• Make sure the barcode is clean and undamaged
• Try different angles if scanning fails
• Check camera permissions in device settings
''');
  }

  void _showResultsHelp(BuildContext context) {
    _showHelpDialog(context, 'Understanding Results', '''
🟢 HALAL: Product is permissible according to Islamic law
🔴 HARAM: Product contains forbidden ingredients
🟡 QUESTIONABLE: Contains ingredients with uncertain status
⚪ UNCERTAIN: Insufficient information for determination

Confidence levels indicate our certainty in the analysis.
''');
  }

  void _showSettingsHelp(BuildContext context) {
    _showHelpDialog(context, 'Settings Help', '''
• Language: Change app language and enable RTL support
• Theme: Switch between light, dark, or system theme
• Notifications: Customize alert preferences
• Privacy: Manage your data and privacy settings
''');
  }

  void _showBugReport(BuildContext context) {
    _showHelpDialog(context, 'Report a Bug', '''
When reporting bugs, please include:

• Device model and OS version
• App version
• Steps to reproduce the issue
• Screenshots if applicable
• Error messages (if any)

Send reports to: <EMAIL>
''');
  }

  void _showHelpDialog(BuildContext context, String title, String content) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  void _showFeedbackDialog(BuildContext context) {
    final TextEditingController controller = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Send Feedback'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Your feedback helps us improve HalalWise:'),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              maxLines: 4,
              decoration: const InputDecoration(
                hintText: 'Share your thoughts, suggestions, or report issues...',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Thank you for your feedback!'),
                  backgroundColor: AppTheme.successGreen,
                ),
              );
            },
            child: const Text('Send'),
          ),
        ],
      ),
    );
  }
}
