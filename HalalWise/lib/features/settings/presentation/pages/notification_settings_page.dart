import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../l10n/app_localizations.dart';
import '../providers/settings_provider.dart';

class NotificationSettingsPage extends StatefulWidget {
  const NotificationSettingsPage({super.key});

  @override
  State<NotificationSettingsPage> createState() => _NotificationSettingsPageState();
}

class _NotificationSettingsPageState extends State<NotificationSettingsPage> {
  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final settingsProvider = Provider.of<SettingsProvider>(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.notifications),
        backgroundColor: AppTheme.primaryGreen,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.primaryGreen.withOpacity(0.1),
              Colors.white,
            ],
          ),
        ),
        child: Column(
          children: [
            _buildHeaderCard(context, l10n),
            Expanded(
              child: _buildNotificationSettings(context, settingsProvider),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderCard(BuildContext context, AppLocalizations l10n) {
    return Container(
      margin: const EdgeInsets.all(20),
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              colors: [
                AppTheme.primaryGreen.withOpacity(0.1),
                AppTheme.primaryGreen.withOpacity(0.05),
              ],
            ),
          ),
          child: Column(
            children: [
              Icon(
                Icons.notifications_active,
                size: 48,
                color: AppTheme.primaryGreen,
              ),
              const SizedBox(height: 12),
              Text(
                'Notification Settings',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryGreen,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Customize your notification preferences to stay informed',
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationSettings(BuildContext context, SettingsProvider settingsProvider) {
    return ListView(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      children: [
        // General Notifications
        _buildSectionCard(
          context,
          title: 'General Notifications',
          icon: Icons.notifications,
          children: [
            _buildSwitchTile(
              context,
              title: 'Enable Notifications',
              subtitle: 'Receive app notifications',
              value: settingsProvider.notificationsEnabled,
              onChanged: (value) {
                settingsProvider.setNotificationsEnabled(value);
                _showSnackBar(context, 'Notifications ${value ? 'enabled' : 'disabled'}');
              },
            ),
            _buildSwitchTile(
              context,
              title: 'Sound',
              subtitle: 'Play sound for notifications',
              value: settingsProvider.notificationSoundEnabled,
              onChanged: settingsProvider.notificationsEnabled
                  ? (value) {
                      settingsProvider.setNotificationSoundEnabled(value);
                      _showSnackBar(context, 'Notification sound ${value ? 'enabled' : 'disabled'}');
                    }
                  : null,
            ),
            _buildSwitchTile(
              context,
              title: 'Vibration',
              subtitle: 'Vibrate for notifications',
              value: settingsProvider.notificationVibrationEnabled,
              onChanged: settingsProvider.notificationsEnabled
                  ? (value) {
                      settingsProvider.setNotificationVibrationEnabled(value);
                      _showSnackBar(context, 'Notification vibration ${value ? 'enabled' : 'disabled'}');
                    }
                  : null,
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // Scan Notifications
        _buildSectionCard(
          context,
          title: 'Scan Notifications',
          icon: Icons.qr_code_scanner,
          children: [
            _buildSwitchTile(
              context,
              title: 'Scan Results',
              subtitle: 'Notify when scan analysis is complete',
              value: settingsProvider.scanResultNotificationsEnabled,
              onChanged: settingsProvider.notificationsEnabled
                  ? (value) {
                      settingsProvider.setScanResultNotificationsEnabled(value);
                      _showSnackBar(context, 'Scan result notifications ${value ? 'enabled' : 'disabled'}');
                    }
                  : null,
            ),
            _buildSwitchTile(
              context,
              title: 'Haram Alerts',
              subtitle: 'High priority alerts for haram products',
              value: settingsProvider.haramAlertNotificationsEnabled,
              onChanged: settingsProvider.notificationsEnabled
                  ? (value) {
                      settingsProvider.setHaramAlertNotificationsEnabled(value);
                      _showSnackBar(context, 'Haram alert notifications ${value ? 'enabled' : 'disabled'}');
                    }
                  : null,
            ),
            _buildSwitchTile(
              context,
              title: 'Questionable Ingredients',
              subtitle: 'Notify about questionable ingredients',
              value: settingsProvider.questionableIngredientNotificationsEnabled,
              onChanged: settingsProvider.notificationsEnabled
                  ? (value) {
                      settingsProvider.setQuestionableIngredientNotificationsEnabled(value);
                      _showSnackBar(context, 'Questionable ingredient notifications ${value ? 'enabled' : 'disabled'}');
                    }
                  : null,
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // App Updates
        _buildSectionCard(
          context,
          title: 'App Updates',
          icon: Icons.system_update,
          children: [
            _buildSwitchTile(
              context,
              title: 'Update Notifications',
              subtitle: 'Notify about app updates',
              value: settingsProvider.updateNotificationsEnabled,
              onChanged: settingsProvider.notificationsEnabled
                  ? (value) {
                      settingsProvider.setUpdateNotificationsEnabled(value);
                      _showSnackBar(context, 'Update notifications ${value ? 'enabled' : 'disabled'}');
                    }
                  : null,
            ),
            _buildSwitchTile(
              context,
              title: 'Database Updates',
              subtitle: 'Notify about ingredient database updates',
              value: settingsProvider.databaseUpdateNotificationsEnabled,
              onChanged: settingsProvider.notificationsEnabled
                  ? (value) {
                      settingsProvider.setDatabaseUpdateNotificationsEnabled(value);
                      _showSnackBar(context, 'Database update notifications ${value ? 'enabled' : 'disabled'}');
                    }
                  : null,
            ),
          ],
        ),
        
        const SizedBox(height: 24),
        
        // Test Notification Button
        if (settingsProvider.notificationsEnabled) ...[
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Icon(
                    Icons.test_tube,
                    color: AppTheme.primaryGreen,
                    size: 32,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Test Notifications',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Send a test notification to verify your settings',
                    style: Theme.of(context).textTheme.bodySmall,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: () => _sendTestNotification(context),
                    icon: const Icon(Icons.send),
                    label: const Text('Send Test Notification'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryGreen,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
        
        const SizedBox(height: 20),
      ],
    );
  }

  Widget _buildSectionCard(BuildContext context, {
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: AppTheme.primaryGreen, size: 24),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildSwitchTile(BuildContext context, {
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool>? onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: onChanged == null ? Colors.grey : null,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: onChanged == null ? Colors.grey : Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppTheme.primaryGreen,
          ),
        ],
      ),
    );
  }

  void _sendTestNotification(BuildContext context) {
    // Simulate sending a test notification
    _showSnackBar(context, '🔔 Test notification sent! Check your notification panel.');
  }

  void _showSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.successGreen,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
