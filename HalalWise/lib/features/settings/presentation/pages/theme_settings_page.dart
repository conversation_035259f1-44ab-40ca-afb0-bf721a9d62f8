import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../l10n/app_localizations.dart';
import '../providers/settings_provider.dart';

class ThemeSettingsPage extends StatefulWidget {
  const ThemeSettingsPage({super.key});

  @override
  State<ThemeSettingsPage> createState() => _ThemeSettingsPageState();
}

class _ThemeSettingsPageState extends State<ThemeSettingsPage> {
  final List<ThemeOption> _themeOptions = [
    ThemeOption(
      mode: ThemeMode.system,
      title: 'System Default',
      subtitle: 'Follow device settings',
      icon: Icons.brightness_auto,
      description: 'Automatically switch between light and dark theme based on your device settings',
    ),
    ThemeOption(
      mode: ThemeMode.light,
      title: 'Light Theme',
      subtitle: 'Bright and clean',
      icon: Icons.brightness_7,
      description: 'Classic light theme with bright colors and clear visibility',
    ),
    ThemeOption(
      mode: ThemeMode.dark,
      title: 'Dark Theme',
      subtitle: 'Easy on the eyes',
      icon: Icons.brightness_3,
      description: 'Dark theme that reduces eye strain and saves battery on OLED displays',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final settingsProvider = Provider.of<SettingsProvider>(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.theme),
        backgroundColor: AppTheme.primaryGreen,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.primaryGreen.withOpacity(0.1),
              Colors.white,
            ],
          ),
        ),
        child: Column(
          children: [
            _buildHeaderCard(context, l10n),
            Expanded(
              child: _buildThemeList(context, settingsProvider),
            ),
            _buildPreviewCard(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderCard(BuildContext context, AppLocalizations l10n) {
    return Container(
      margin: const EdgeInsets.all(20),
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              colors: [
                AppTheme.primaryGreen.withOpacity(0.1),
                AppTheme.primaryGreen.withOpacity(0.05),
              ],
            ),
          ),
          child: Column(
            children: [
              Icon(
                Icons.palette,
                size: 48,
                color: AppTheme.primaryGreen,
              ),
              const SizedBox(height: 12),
              Text(
                'Choose Your Theme',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryGreen,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Select your preferred theme for the best visual experience',
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildThemeList(BuildContext context, SettingsProvider settingsProvider) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemCount: _themeOptions.length,
      itemBuilder: (context, index) {
        final themeOption = _themeOptions[index];
        final isSelected = settingsProvider.themeMode == themeOption.mode;
        
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          elevation: isSelected ? 6 : 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              gradient: isSelected
                  ? LinearGradient(
                      colors: [
                        AppTheme.primaryGreen.withOpacity(0.1),
                        AppTheme.primaryGreen.withOpacity(0.05),
                      ],
                    )
                  : null,
              border: isSelected
                  ? Border.all(color: AppTheme.primaryGreen, width: 2)
                  : null,
            ),
            child: InkWell(
              onTap: () => _selectTheme(context, settingsProvider, themeOption),
              borderRadius: BorderRadius.circular(12),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: isSelected 
                                ? AppTheme.primaryGreen.withOpacity(0.2)
                                : Colors.grey[100],
                          ),
                          child: Icon(
                            themeOption.icon,
                            color: isSelected ? AppTheme.primaryGreen : Colors.grey[600],
                            size: 28,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                themeOption.title,
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: isSelected ? FontWeight.bold : FontWeight.w600,
                                  color: isSelected ? AppTheme.primaryGreen : null,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                themeOption.subtitle,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: isSelected ? AppTheme.primaryGreen : Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (isSelected)
                          Icon(
                            Icons.check_circle,
                            color: AppTheme.primaryGreen,
                            size: 28,
                          )
                        else
                          Icon(
                            Icons.radio_button_unchecked,
                            color: Colors.grey[400],
                            size: 28,
                          ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      themeOption.description,
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.grey[600],
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPreviewCard(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(20),
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.preview,
                    color: AppTheme.primaryGreen,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Theme Preview',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: AppTheme.primaryGreen,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                'Changes will be applied immediately. You can always change your theme preference later in settings.',
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _selectTheme(BuildContext context, SettingsProvider settingsProvider, ThemeOption themeOption) {
    if (settingsProvider.themeMode == themeOption.mode) {
      return; // Already selected
    }

    settingsProvider.setThemeMode(themeOption.mode);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Theme changed to ${themeOption.title}'),
        backgroundColor: AppTheme.successGreen,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}

class ThemeOption {
  final ThemeMode mode;
  final String title;
  final String subtitle;
  final IconData icon;
  final String description;

  ThemeOption({
    required this.mode,
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.description,
  });
}
