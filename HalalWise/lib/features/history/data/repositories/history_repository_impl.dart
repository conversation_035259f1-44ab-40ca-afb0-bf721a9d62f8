import '../../domain/repositories/history_repository.dart';
import '../../../scanner/domain/entities/scan_result.dart';
import '../../../../core/database/database_helper.dart';

class HistoryRepositoryImpl implements HistoryRepository {
  final DatabaseHelper _databaseHelper;

  HistoryRepositoryImpl(this._databaseHelper);

  @override
  Future<List<ScanResult>> getScanHistory({int limit = 50}) async {
    try {
      final historyData = await _databaseHelper.getScanHistory(limit: limit);
      return historyData.map((data) => _mapDatabaseToScanResult(data)).toList();
    } catch (e) {
      print('Error getting scan history: $e');
      return [];
    }
  }

  @override
  Future<void> saveScanResult(ScanResult scanResult) async {
    try {
      final scanData = {
        'scan_type': scanResult.scanType.toString(),
        'scan_data': scanResult.scanData,
        'result': scanResult.toJson().toString(),
        'user_feedback': scanResult.userFeedback,
      };
      
      await _databaseHelper.insertScanHistory(scanData);
    } catch (e) {
      print('Error saving scan result: $e');
      rethrow;
    }
  }

  @override
  Future<void> deleteScanResult(String id) async {
    // Placeholder implementation
    // In a real implementation, you would delete from database
  }

  @override
  Future<void> clearHistory() async {
    // Placeholder implementation
    // In a real implementation, you would clear the history table
  }

  ScanResult _mapDatabaseToScanResult(Map<String, dynamic> data) {
    // Placeholder implementation
    // In a real implementation, you would properly map the database data
    throw UnimplementedError('Database to ScanResult mapping not implemented');
  }
}
