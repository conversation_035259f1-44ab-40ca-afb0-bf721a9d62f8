import 'package:flutter/foundation.dart';

class HistoryProvider extends ChangeNotifier {
  List<dynamic> _scanHistory = [];
  bool _isLoading = false;

  List<dynamic> get scanHistory => _scanHistory;
  bool get isLoading => _isLoading;

  Future<void> loadHistory() async {
    _isLoading = true;
    notifyListeners();

    try {
      // Placeholder for loading history logic
      await Future.delayed(const Duration(seconds: 1));
      _scanHistory = []; // Empty for now
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      notifyListeners();
    }
  }

  void clearHistory() {
    _scanHistory.clear();
    notifyListeners();
  }
}
