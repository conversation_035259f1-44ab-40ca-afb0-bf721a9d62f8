import 'package:flutter/foundation.dart';

class AuthProvider extends ChangeNotifier {
  bool _isAuthenticated = false;
  String? _userId;
  String? _userEmail;

  bool get isAuthenticated => _isAuthenticated;
  String? get userId => _userId;
  String? get userEmail => _userEmail;

  Future<void> signIn(String email, String password) async {
    // Placeholder for authentication logic
    await Future.delayed(const Duration(seconds: 1));
    _isAuthenticated = true;
    _userId = 'user_123';
    _userEmail = email;
    notifyListeners();
  }

  Future<void> signOut() async {
    _isAuthenticated = false;
    _userId = null;
    _userEmail = null;
    notifyListeners();
  }
}
