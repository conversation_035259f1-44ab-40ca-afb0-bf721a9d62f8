import '../../../../core/network/ai_service.dart';

class FoodItem {
  final String id;
  final String name;
  final String? brand;
  final String? barcode;
  final List<String> ingredients;
  final HalalStatus halalStatus;
  final double halalConfidence;
  final String halalReason;
  final double healthScore;
  final List<String> allergens;
  final List<String> certifications;
  final NutritionalInfo? nutritionalInfo;
  final String? imageUrl;
  final String source;
  final DateTime createdAt;
  final DateTime updatedAt;

  FoodItem({
    required this.id,
    required this.name,
    this.brand,
    this.barcode,
    required this.ingredients,
    required this.halalStatus,
    required this.halalConfidence,
    required this.halalReason,
    required this.healthScore,
    required this.allergens,
    required this.certifications,
    this.nutritionalInfo,
    this.imageUrl,
    required this.source,
    required this.createdAt,
    required this.updatedAt,
  });

  FoodItem copyWith({
    String? id,
    String? name,
    String? brand,
    String? barcode,
    List<String>? ingredients,
    HalalStatus? halalStatus,
    double? halalConfidence,
    String? halalReason,
    double? healthScore,
    List<String>? allergens,
    List<String>? certifications,
    NutritionalInfo? nutritionalInfo,
    String? imageUrl,
    String? source,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return FoodItem(
      id: id ?? this.id,
      name: name ?? this.name,
      brand: brand ?? this.brand,
      barcode: barcode ?? this.barcode,
      ingredients: ingredients ?? this.ingredients,
      halalStatus: halalStatus ?? this.halalStatus,
      halalConfidence: halalConfidence ?? this.halalConfidence,
      halalReason: halalReason ?? this.halalReason,
      healthScore: healthScore ?? this.healthScore,
      allergens: allergens ?? this.allergens,
      certifications: certifications ?? this.certifications,
      nutritionalInfo: nutritionalInfo ?? this.nutritionalInfo,
      imageUrl: imageUrl ?? this.imageUrl,
      source: source ?? this.source,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'brand': brand,
      'barcode': barcode,
      'ingredients': ingredients,
      'halal_status': halalStatus.toString(),
      'halal_confidence': halalConfidence,
      'halal_reason': halalReason,
      'health_score': healthScore,
      'allergens': allergens,
      'certifications': certifications,
      'nutritional_info': nutritionalInfo?.toJson(),
      'image_url': imageUrl,
      'source': source,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory FoodItem.fromJson(Map<String, dynamic> json) {
    return FoodItem(
      id: json['id'] as String,
      name: json['name'] as String,
      brand: json['brand'] as String?,
      barcode: json['barcode'] as String?,
      ingredients: List<String>.from(json['ingredients'] ?? []),
      halalStatus: HalalStatus.values.firstWhere(
        (e) => e.toString() == json['halal_status'],
        orElse: () => HalalStatus.uncertain,
      ),
      halalConfidence: (json['halal_confidence'] as num?)?.toDouble() ?? 0.0,
      halalReason: json['halal_reason'] as String? ?? '',
      healthScore: (json['health_score'] as num?)?.toDouble() ?? 5.0,
      allergens: List<String>.from(json['allergens'] ?? []),
      certifications: List<String>.from(json['certifications'] ?? []),
      nutritionalInfo: json['nutritional_info'] != null
          ? NutritionalInfo.fromJson(json['nutritional_info'])
          : null,
      imageUrl: json['image_url'] as String?,
      source: json['source'] as String? ?? 'unknown',
      createdAt: DateTime.fromMillisecondsSinceEpoch(
        json['created_at'] as int? ?? DateTime.now().millisecondsSinceEpoch,
      ),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(
        json['updated_at'] as int? ?? DateTime.now().millisecondsSinceEpoch,
      ),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FoodItem &&
        other.id == id &&
        other.name == name &&
        other.brand == brand &&
        other.barcode == barcode;
  }

  @override
  int get hashCode {
    return id.hashCode ^ name.hashCode ^ brand.hashCode ^ barcode.hashCode;
  }

  @override
  String toString() {
    return 'FoodItem(id: $id, name: $name, brand: $brand, halalStatus: $halalStatus, healthScore: $healthScore)';
  }

  // Helper getters
  String get displayName {
    if (brand != null && brand!.isNotEmpty) {
      return '$brand $name';
    }
    return name;
  }

  String get halalStatusText {
    switch (halalStatus) {
      case HalalStatus.halal:
        return 'Halal ✅';
      case HalalStatus.haram:
        return 'Haram ❌';
      case HalalStatus.questionable:
        return 'Questionable ⚠️';
      case HalalStatus.uncertain:
        return 'Uncertain ❓';
    }
  }

  String get healthScoreText {
    if (healthScore >= 8) return 'Excellent';
    if (healthScore >= 6) return 'Good';
    if (healthScore >= 4) return 'Fair';
    return 'Poor';
  }

  bool get hasHalalCertification {
    return certifications.any((cert) => 
        cert.toLowerCase().contains('halal') ||
        cert.toLowerCase().contains('حلال'));
  }

  List<String> get questionableIngredients {
    // This would be populated by the AI analysis
    return ingredients.where((ingredient) {
      final lower = ingredient.toLowerCase();
      return lower.contains('gelatin') ||
             lower.contains('mono and diglycerides') ||
             lower.contains('natural flavors') ||
             lower.contains('enzymes');
    }).toList();
  }
}

class NutritionalInfo {
  final double? calories;
  final double? protein;
  final double? carbohydrates;
  final double? sugar;
  final double? fat;
  final double? saturatedFat;
  final double? fiber;
  final double? sodium;
  final double? cholesterol;

  NutritionalInfo({
    this.calories,
    this.protein,
    this.carbohydrates,
    this.sugar,
    this.fat,
    this.saturatedFat,
    this.fiber,
    this.sodium,
    this.cholesterol,
  });

  Map<String, dynamic> toJson() {
    return {
      'calories': calories,
      'protein': protein,
      'carbohydrates': carbohydrates,
      'sugar': sugar,
      'fat': fat,
      'saturated_fat': saturatedFat,
      'fiber': fiber,
      'sodium': sodium,
      'cholesterol': cholesterol,
    };
  }

  factory NutritionalInfo.fromJson(Map<String, dynamic> json) {
    return NutritionalInfo(
      calories: (json['calories'] as num?)?.toDouble(),
      protein: (json['protein'] as num?)?.toDouble(),
      carbohydrates: (json['carbohydrates'] as num?)?.toDouble(),
      sugar: (json['sugar'] as num?)?.toDouble(),
      fat: (json['fat'] as num?)?.toDouble(),
      saturatedFat: (json['saturated_fat'] as num?)?.toDouble(),
      fiber: (json['fiber'] as num?)?.toDouble(),
      sodium: (json['sodium'] as num?)?.toDouble(),
      cholesterol: (json['cholesterol'] as num?)?.toDouble(),
    );
  }

  @override
  String toString() {
    return 'NutritionalInfo(calories: $calories, protein: $protein, carbohydrates: $carbohydrates, sugar: $sugar, fat: $fat, saturatedFat: $saturatedFat, fiber: $fiber, sodium: $sodium, cholesterol: $cholesterol)';
  }
}
