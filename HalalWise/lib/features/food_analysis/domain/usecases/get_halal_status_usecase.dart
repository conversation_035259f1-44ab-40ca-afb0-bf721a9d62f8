import '../../../../core/network/ai_service.dart';
import '../repositories/food_analysis_repository.dart';

class GetHalalStatusUseCase {
  final FoodAnalysisRepository _repository;

  GetHalalStatusUseCase(this._repository);

  Future<HalalAnalysisResult> execute({
    required String productName,
    String? ingredients,
    String? brand,
    List<String>? certifications,
  }) async {
    return await _repository.getHalalStatus(
      productName: productName,
      ingredients: ingredients,
      brand: brand,
      certifications: certifications,
    );
  }
}
