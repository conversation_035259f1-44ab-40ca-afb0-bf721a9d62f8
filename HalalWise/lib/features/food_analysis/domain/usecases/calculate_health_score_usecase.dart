import '../repositories/food_analysis_repository.dart';

class CalculateHealthScoreUseCase {
  final FoodAnalysisRepository _repository;

  CalculateHealthScoreUseCase(this._repository);

  Future<double> execute({
    required String productName,
    String? ingredients,
    Map<String, double>? nutritionalInfo,
  }) async {
    return await _repository.calculateHealthScore(
      productName: productName,
      ingredients: ingredients,
      nutritionalInfo: nutritionalInfo,
    );
  }
}
