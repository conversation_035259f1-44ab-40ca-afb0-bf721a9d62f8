import '../entities/food_item.dart';
import '../repositories/food_analysis_repository.dart';

class AnalyzeFoodUseCase {
  final FoodAnalysisRepository _repository;

  AnalyzeFoodUseCase(this._repository);

  Future<FoodItem> execute({
    required String name,
    String? brand,
    List<String>? ingredients,
    Map<String, double>? nutritionalInfo,
  }) async {
    return await _repository.analyzeFood(
      name: name,
      brand: brand,
      ingredients: ingredients,
      nutritionalInfo: nutritionalInfo,
    );
  }
}
