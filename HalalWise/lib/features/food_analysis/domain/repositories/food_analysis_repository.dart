import '../entities/food_item.dart';
import '../../../../core/network/ai_service.dart';

abstract class FoodAnalysisRepository {
  Future<FoodItem> analyzeFood({
    required String name,
    String? brand,
    List<String>? ingredients,
    Map<String, double>? nutritionalInfo,
  });
  
  Future<HalalAnalysisResult> getHalalStatus({
    required String productName,
    String? ingredients,
    String? brand,
    List<String>? certifications,
  });
  
  Future<double> calculateHealthScore({
    required String productName,
    String? ingredients,
    Map<String, double>? nutritionalInfo,
  });
}
