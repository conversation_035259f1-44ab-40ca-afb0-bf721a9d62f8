import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/network/ai_service.dart';
import '../../../scanner/domain/entities/scan_result.dart';
import '../../domain/entities/food_item.dart';

class FoodResultPage extends StatefulWidget {
  final ScanResult result;

  const FoodResultPage({
    super.key,
    required this.result,
  });

  @override
  State<FoodResultPage> createState() => _FoodResultPageState();
}

class _FoodResultPageState extends State<FoodResultPage>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Food Analysis'),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _shareResult,
          ),
          IconButton(
            icon: const Icon(Icons.bookmark_border),
            onPressed: _saveToFavorites,
          ),
        ],
      ),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: _buildBody(),
            ),
          );
        },
      ),
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildProductHeader(),
          const SizedBox(height: 24),
          _buildHalalStatusCard(),
          const SizedBox(height: 16),
          _buildHealthScoreCard(),
          const SizedBox(height: 16),
          _buildIngredientsCard(),
          const SizedBox(height: 16),
          _buildNutritionalInfoCard(),
          const SizedBox(height: 16),
          _buildAllergensCard(),
          const SizedBox(height: 16),
          _buildCertificationsCard(),
          const SizedBox(height: 24),
          _buildFeedbackSection(),
        ],
      ),
    );
  }

  Widget _buildProductHeader() {
    final foodItem = widget.result.foodItem;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            // Product image
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: Colors.grey[200],
              ),
              child: foodItem.imageUrl != null
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: CachedNetworkImage(
                        imageUrl: foodItem.imageUrl!,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => const Center(
                          child: CircularProgressIndicator(),
                        ),
                        errorWidget: (context, url, error) => const Icon(
                          Icons.image_not_supported,
                          size: 40,
                          color: Colors.grey,
                        ),
                      ),
                    )
                  : const Icon(
                      Icons.fastfood,
                      size: 40,
                      color: Colors.grey,
                    ),
            ),
            const SizedBox(width: 16),
            // Product info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    foodItem.displayName,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (foodItem.barcode != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      'Barcode: ${foodItem.barcode}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Icons.source,
                        size: 16,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Source: ${foodItem.source}',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHalalStatusCard() {
    final foodItem = widget.result.foodItem;
    Color statusColor;
    IconData statusIcon;
    
    switch (foodItem.halalStatus) {
      case HalalStatus.halal:
        statusColor = AppTheme.successGreen;
        statusIcon = Icons.check_circle;
        break;
      case HalalStatus.haram:
        statusColor = AppTheme.errorRed;
        statusIcon = Icons.cancel;
        break;
      case HalalStatus.questionable:
        statusColor = AppTheme.warningOrange;
        statusIcon = Icons.warning;
        break;
      case HalalStatus.uncertain:
        statusColor = Colors.grey;
        statusIcon = Icons.help;
        break;
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  statusIcon,
                  color: statusColor,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  'Halal Status',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: statusColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: statusColor.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Text(
                    foodItem.halalStatusText,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: statusColor,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '${(foodItem.halalConfidence * 100).toInt()}% confident',
                    style: TextStyle(
                      fontSize: 14,
                      color: statusColor,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Reason:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              foodItem.halalReason,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHealthScoreCard() {
    final foodItem = widget.result.foodItem;
    final score = foodItem.healthScore;
    Color scoreColor;
    
    if (score >= 8) {
      scoreColor = AppTheme.successGreen;
    } else if (score >= 6) {
      scoreColor = AppTheme.lightGreen;
    } else if (score >= 4) {
      scoreColor = AppTheme.warningOrange;
    } else {
      scoreColor = AppTheme.errorRed;
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.health_and_safety,
                  color: scoreColor,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  'Health Score',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: scoreColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(30),
                    border: Border.all(color: scoreColor, width: 3),
                  ),
                  child: Center(
                    child: Text(
                      score.toStringAsFixed(1),
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: scoreColor,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        foodItem.healthScoreText,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: scoreColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      LinearProgressIndicator(
                        value: score / 10,
                        backgroundColor: Colors.grey[300],
                        valueColor: AlwaysStoppedAnimation<Color>(scoreColor),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIngredientsCard() {
    final foodItem = widget.result.foodItem;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.list,
                  color: AppTheme.primaryGreen,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  'Ingredients',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (foodItem.ingredients.isNotEmpty) ...[
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: foodItem.ingredients.map((ingredient) {
                  final isQuestionable = foodItem.questionableIngredients.contains(ingredient);
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: isQuestionable 
                          ? AppTheme.warningOrange.withOpacity(0.1)
                          : Colors.grey[100],
                      borderRadius: BorderRadius.circular(16),
                      border: isQuestionable 
                          ? Border.all(color: AppTheme.warningOrange.withOpacity(0.3))
                          : null,
                    ),
                    child: Text(
                      ingredient,
                      style: TextStyle(
                        fontSize: 12,
                        color: isQuestionable ? AppTheme.warningOrange : null,
                      ),
                    ),
                  );
                }).toList(),
              ),
            ] else ...[
              Text(
                'No ingredient information available',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildNutritionalInfoCard() {
    final nutritionalInfo = widget.result.foodItem.nutritionalInfo;
    
    if (nutritionalInfo == null) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.analytics,
                  color: AppTheme.primaryGreen,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  'Nutritional Information',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildNutrientRow('Calories', nutritionalInfo.calories, 'kcal'),
            _buildNutrientRow('Protein', nutritionalInfo.protein, 'g'),
            _buildNutrientRow('Carbohydrates', nutritionalInfo.carbohydrates, 'g'),
            _buildNutrientRow('Sugar', nutritionalInfo.sugar, 'g'),
            _buildNutrientRow('Fat', nutritionalInfo.fat, 'g'),
            _buildNutrientRow('Saturated Fat', nutritionalInfo.saturatedFat, 'g'),
            _buildNutrientRow('Fiber', nutritionalInfo.fiber, 'g'),
            _buildNutrientRow('Sodium', nutritionalInfo.sodium, 'g'),
          ],
        ),
      ),
    );
  }

  Widget _buildNutrientRow(String name, double? value, String unit) {
    if (value == null) return const SizedBox.shrink();
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(name),
          Text(
            '${value.toStringAsFixed(1)} $unit',
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  Widget _buildAllergensCard() {
    final allergens = widget.result.foodItem.allergens;
    
    if (allergens.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.warning_amber,
                  color: AppTheme.warningOrange,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  'Allergens',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: allergens.map((allergen) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppTheme.warningOrange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: AppTheme.warningOrange.withOpacity(0.3)),
                  ),
                  child: Text(
                    allergen,
                    style: TextStyle(
                      fontSize: 12,
                      color: AppTheme.warningOrange,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCertificationsCard() {
    final certifications = widget.result.foodItem.certifications;
    
    if (certifications.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.verified,
                  color: AppTheme.successGreen,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  'Certifications',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...certifications.map((cert) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: AppTheme.successGreen,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(cert),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildFeedbackSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Was this analysis helpful?',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _submitFeedback(true),
                    icon: const Icon(Icons.thumb_up),
                    label: const Text('Yes'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _submitFeedback(false),
                    icon: const Icon(Icons.thumb_down),
                    label: const Text('No'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _shareResult() {
    // Implement sharing functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Sharing functionality coming soon!')),
    );
  }

  void _saveToFavorites() {
    // Implement save to favorites functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Saved to favorites!')),
    );
  }

  void _submitFeedback(bool isPositive) {
    // Implement feedback submission
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          isPositive 
              ? 'Thank you for your positive feedback!' 
              : 'Thank you for your feedback. We\'ll work to improve.',
        ),
      ),
    );
  }
}
