import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../l10n/app_localizations.dart';
import '../providers/food_analysis_provider.dart';
import 'analysis_result_page.dart';

class ManualInputPage extends StatefulWidget {
  const ManualInputPage({super.key});

  @override
  State<ManualInputPage> createState() => _ManualInputPageState();
}

class _ManualInputPageState extends State<ManualInputPage> {
  final _formKey = GlobalKey<FormState>();
  final _productNameController = TextEditingController();
  final _brandController = TextEditingController();
  final _ingredientsController = TextEditingController();
  final _certificationsController = TextEditingController();

  bool _isAnalyzing = false;

  @override
  void dispose() {
    _productNameController.dispose();
    _brandController.dispose();
    _ingredientsController.dispose();
    _certificationsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.manualInput),
        backgroundColor: AppTheme.warningOrange,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.warningOrange.withOpacity(0.1),
              Colors.white,
            ],
          ),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeaderCard(context, l10n),
                const SizedBox(height: 24),
                _buildInputFields(context, l10n),
                const SizedBox(height: 32),
                _buildAnalyzeButton(context, l10n),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderCard(BuildContext context, AppLocalizations l10n) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              AppTheme.warningOrange.withOpacity(0.1),
              AppTheme.warningOrange.withOpacity(0.05),
            ],
          ),
        ),
        child: Column(
          children: [
            Icon(
              Icons.edit_note,
              size: 48,
              color: AppTheme.warningOrange,
            ),
            const SizedBox(height: 12),
            Text(
              'Enter Food Details',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.warningOrange,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Manually enter product information for halal analysis',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInputFields(BuildContext context, AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Product Information',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),

        // Product Name (Required)
        _buildTextField(
          controller: _productNameController,
          label: 'Product Name *',
          hint: 'e.g., Chicken Nuggets',
          icon: Icons.fastfood,
          required: true,
        ),
        const SizedBox(height: 16),

        // Brand (Optional)
        _buildTextField(
          controller: _brandController,
          label: 'Brand',
          hint: 'e.g., McDonald\'s',
          icon: Icons.business,
        ),
        const SizedBox(height: 16),

        // Ingredients (Important)
        _buildTextField(
          controller: _ingredientsController,
          label: 'Ingredients',
          hint: 'List all ingredients separated by commas',
          icon: Icons.list,
          maxLines: 4,
        ),
        const SizedBox(height: 16),

        // Certifications (Optional)
        _buildTextField(
          controller: _certificationsController,
          label: 'Halal Certifications',
          hint: 'e.g., JAKIM, MUI, HFA',
          icon: Icons.verified,
        ),
        const SizedBox(height: 16),

        // Info Card
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppTheme.primaryGreen.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppTheme.primaryGreen.withOpacity(0.3),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                color: AppTheme.primaryGreen,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'The more details you provide, the more accurate the halal analysis will be.',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppTheme.primaryGreen,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    int maxLines = 1,
    bool required = false,
  }) {
    return TextFormField(
      controller: controller,
      maxLines: maxLines,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, color: AppTheme.warningOrange),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppTheme.warningOrange, width: 2),
        ),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      validator: required ? (value) {
        if (value == null || value.trim().isEmpty) {
          return 'This field is required';
        }
        return null;
      } : null,
    );
  }

  Widget _buildAnalyzeButton(BuildContext context, AppLocalizations l10n) {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton.icon(
        onPressed: _isAnalyzing ? null : _analyzeFood,
        icon: _isAnalyzing
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Icon(Icons.analytics),
        label: Text(
          _isAnalyzing ? 'Analyzing...' : 'Analyze Food',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.warningOrange,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 4,
        ),
      ),
    );
  }

  Future<void> _analyzeFood() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_productNameController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter at least the product name'),
          backgroundColor: AppTheme.errorRed,
        ),
      );
      return;
    }

    setState(() {
      _isAnalyzing = true;
    });

    try {
      final provider = Provider.of<FoodAnalysisProvider>(context, listen: false);

      // Simulate analysis (replace with actual implementation)
      await Future.delayed(const Duration(seconds: 2));

      // Navigate to results page
      if (mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => AnalysisResultPage(
              productName: _productNameController.text.trim(),
              brand: _brandController.text.trim().isNotEmpty
                  ? _brandController.text.trim()
                  : null,
              ingredients: _ingredientsController.text.trim().isNotEmpty
                  ? _ingredientsController.text.trim()
                  : null,
              certifications: _certificationsController.text.trim().isNotEmpty
                  ? _certificationsController.text.split(',').map((e) => e.trim()).toList()
                  : null,
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error analyzing food: $e'),
            backgroundColor: AppTheme.errorRed,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isAnalyzing = false;
        });
      }
    }
  }
}