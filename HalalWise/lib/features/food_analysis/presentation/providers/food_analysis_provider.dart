import 'package:flutter/foundation.dart';

class FoodAnalysisProvider extends ChangeNotifier {
  bool _isAnalyzing = false;
  String? _error;

  bool get isAnalyzing => _isAnalyzing;
  String? get error => _error;

  Future<void> analyzeFood(String foodName) async {
    _isAnalyzing = true;
    _error = null;
    notifyListeners();

    try {
      // Placeholder for food analysis logic
      await Future.delayed(const Duration(seconds: 2));
      _isAnalyzing = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isAnalyzing = false;
      notifyListeners();
    }
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}
