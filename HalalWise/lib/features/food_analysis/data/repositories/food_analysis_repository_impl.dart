import 'package:uuid/uuid.dart';
import '../../domain/repositories/food_analysis_repository.dart';
import '../../domain/entities/food_item.dart';
import '../../../../core/network/ai_service.dart';
import '../../../../core/database/database_helper.dart';

class FoodAnalysisRepositoryImpl implements FoodAnalysisRepository {
  final AIService _aiService;
  final DatabaseHelper _databaseHelper;
  final Uuid _uuid = const Uuid();

  FoodAnalysisRepositoryImpl(this._aiService, this._databaseHelper);

  @override
  Future<FoodItem> analyzeFood({
    required String name,
    String? brand,
    List<String>? ingredients,
    Map<String, double>? nutritionalInfo,
  }) async {
    try {
      // Get halal analysis
      final halalResult = await _aiService.analyzeHalalStatus(
        productName: name,
        brand: brand,
        ingredients: ingredients?.join(', '),
      );

      // Get health analysis
      final healthResult = await _aiService.analyzeHealthScore(
        productName: name,
        ingredients: ingredients?.join(', '),
        nutritionalInfo: nutritionalInfo,
      );

      final foodItem = FoodItem(
        id: _uuid.v4(),
        name: name,
        brand: brand,
        ingredients: ingredients ?? [],
        halalStatus: halalResult.status,
        halalConfidence: halalResult.confidence,
        halalReason: halalResult.reason,
        healthScore: healthResult.score,
        allergens: healthResult.allergens,
        certifications: [],
        nutritionalInfo: nutritionalInfo != null 
            ? NutritionalInfo(
                calories: nutritionalInfo['calories'],
                protein: nutritionalInfo['protein'],
                carbohydrates: nutritionalInfo['carbohydrates'],
                sugar: nutritionalInfo['sugar'],
                fat: nutritionalInfo['fat'],
                saturatedFat: nutritionalInfo['saturatedFat'],
                fiber: nutritionalInfo['fiber'],
                sodium: nutritionalInfo['sodium'],
              )
            : null,
        source: 'ai_analysis',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Save to database for future reference
      await _saveFoodItemToDatabase(foodItem);

      return foodItem;
    } catch (e) {
      throw Exception('Failed to analyze food: ${e.toString()}');
    }
  }

  @override
  Future<HalalAnalysisResult> getHalalStatus({
    required String productName,
    String? ingredients,
    String? brand,
    List<String>? certifications,
  }) async {
    return await _aiService.analyzeHalalStatus(
      productName: productName,
      ingredients: ingredients,
      brand: brand,
      certifications: certifications,
    );
  }

  @override
  Future<double> calculateHealthScore({
    required String productName,
    String? ingredients,
    Map<String, double>? nutritionalInfo,
  }) async {
    final result = await _aiService.analyzeHealthScore(
      productName: productName,
      ingredients: ingredients,
      nutritionalInfo: nutritionalInfo,
    );
    return result.score;
  }

  Future<void> _saveFoodItemToDatabase(FoodItem foodItem) async {
    try {
      final productData = {
        'name': foodItem.name,
        'brand': foodItem.brand,
        'ingredients': foodItem.ingredients.join(', '),
        'halal_status': foodItem.halalStatus.toString().split('.').last,
        'halal_confidence': foodItem.halalConfidence,
        'halal_reason': foodItem.halalReason,
        'health_score': foodItem.healthScore,
        'calories': foodItem.nutritionalInfo?.calories,
        'sugar': foodItem.nutritionalInfo?.sugar,
        'sodium': foodItem.nutritionalInfo?.sodium,
        'saturated_fat': foodItem.nutritionalInfo?.saturatedFat,
        'fiber': foodItem.nutritionalInfo?.fiber,
        'protein': foodItem.nutritionalInfo?.protein,
        'allergens': foodItem.allergens.join(', '),
        'certifications': foodItem.certifications.join(', '),
        'source': foodItem.source,
      };
      
      await _databaseHelper.insertProduct(productData);
    } catch (e) {
      print('Error saving food item to database: $e');
      // Don't throw error as this is not critical
    }
  }
}
