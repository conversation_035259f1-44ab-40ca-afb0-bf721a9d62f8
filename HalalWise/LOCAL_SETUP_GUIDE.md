# 🚀 HalalWise Local Setup Guide

> **Complete guide to run HalalWise on your local machine**

## ✅ **Issue Fixed!**
The `pubspec.yaml` duplicate dependency issue has been resolved. Your app is now ready to run!

---

## 🎯 **Quick Start (5 Minutes)**

### **Step 1: Ensure Flutter is Installed**
```bash
# Check if Flutter is installed
flutter --version

# If not installed, install Flutter:
# macOS: https://docs.flutter.dev/get-started/install/macos
# Windows: https://docs.flutter.dev/get-started/install/windows
# Linux: https://docs.flutter.dev/get-started/install/linux
```

### **Step 2: Navigate to Project**
```bash
cd /Users/<USER>/Ai/HalalWise
```

### **Step 3: Install Dependencies**
```bash
# Install Flutter dependencies (this should work now!)
flutter pub get

# Check for any issues
flutter doctor
```

### **Step 4: Run the App**
```bash
# Option A: Run on Chrome (recommended for preview)
flutter run -d chrome

# Option B: Run on connected device
flutter run

# Option C: List available devices first
flutter devices
```

---

## 🔧 **If Flutter is Not Installed**

### **macOS Installation**
```bash
# Method 1: Using official installer
1. Download Flutter SDK: https://docs.flutter.dev/get-started/install/macos
2. Extract to ~/flutter
3. Add to PATH: export PATH="$PATH:~/flutter/bin"

# Method 2: Using Homebrew (if you have it)
brew install --cask flutter
```

### **Add Flutter to PATH**
```bash
# Add to your shell profile (~/.zshrc or ~/.bash_profile)
echo 'export PATH="$PATH:~/flutter/bin"' >> ~/.zshrc

# Reload your shell
source ~/.zshrc

# Verify installation
flutter --version
```

---

## 🚀 **Backend Setup (Optional)**

### **Step 1: Install Node.js**
```bash
# Check if Node.js is installed
node --version

# If not installed:
# macOS: brew install node
# Or download from: https://nodejs.org/
```

### **Step 2: Start Backend Server**
```bash
# Navigate to backend directory
cd backend

# Install dependencies
npm install

# Start the server
npm start

# Server will run on http://localhost:3000
```

---

## 📱 **Testing Your App**

### **Pre-loaded Test Data**
Your app comes with sample products you can test immediately:

```
Test Barcodes:
├── 5449000000996 → Coca-Cola (Halal ✅)
├── 7622210951557 → Oreo (Questionable ⚠️)
├── 3017620422003 → Nutella (Questionable ⚠️)
├── 8901030865507 → Maggi Noodles (Halal ✅)
└── 4902777193731 → Kit Kat (Questionable ⚠️)
```

### **Features to Test**
1. **Home Screen**: Beautiful Islamic welcome
2. **Barcode Scanner**: Camera integration
3. **Results Page**: Comprehensive halal analysis
4. **Settings**: API usage dashboard
5. **Database**: Offline functionality

---

## 🎨 **What You'll See**

### **Home Screen**
- Islamic greeting: "Assalamu Alaikum!"
- Quick action buttons with smooth animations
- Tips for better scanning results
- Recent scans section

### **Barcode Scanner**
- Real-time camera feed
- Custom scanning overlay
- Manual barcode input option
- Flash toggle for low light

### **Analysis Results**
- Halal status with color coding
- Confidence percentage
- Detailed reasoning
- Health score visualization
- Ingredients breakdown
- Allergen warnings

### **Settings Dashboard**
- API usage tracking
- Daily limits monitoring
- Cost estimation
- Remaining calls counter

---

## 🔧 **Troubleshooting**

### **Common Issues & Solutions**

#### **"Flutter command not found"**
```bash
# Solution: Install Flutter and add to PATH
export PATH="$PATH:~/flutter/bin"
source ~/.zshrc
```

#### **"Dependencies failed to install"**
```bash
# Solution: Clean and retry
flutter clean
flutter pub get
```

#### **"Camera permission denied"**
```bash
# Solution: Allow camera access when prompted
# For Chrome: Click "Allow" when asked for camera permission
```

#### **"No devices found"**
```bash
# Solution: Use Chrome for web preview
flutter run -d chrome

# Or connect an Android device/start iOS simulator
```

#### **"API key not working"**
```bash
# Your Gemini API key is already configured!
# Key: AIzaSyD8uelHhB2IEZWduY92L2CTt7sXzsD7oV8
# Location: lib/core/network/ai_service.dart (line 105)
```

---

## 📊 **Performance Tips**

### **For Best Experience**
```bash
# Use Chrome for web preview (best compatibility)
flutter run -d chrome

# For mobile testing, use a real device
flutter run --release  # For better performance
```

### **Development Mode**
```bash
# Hot reload enabled for quick changes
flutter run  # Press 'r' to hot reload, 'R' to hot restart
```

---

## 🎯 **Next Steps After Running**

### **Immediate Testing**
1. **Scan test barcodes** listed above
2. **Check API usage** in settings
3. **Test offline functionality** (disconnect internet)
4. **Try manual barcode input**

### **Customization**
1. **Add more products** to database
2. **Modify UI colors** in `lib/core/theme/app_theme.dart`
3. **Update ingredients** in `lib/core/database/sample_data.dart`
4. **Configure additional languages**

### **Deployment**
1. **Build for Android**: `flutter build apk`
2. **Build for iOS**: `flutter build ios`
3. **Deploy backend** to cloud service
4. **Publish to app stores**

---

## 🤝 **Getting Help**

### **If You Need Support**
- **Documentation**: Check README.md files
- **GitHub Issues**: Report technical problems
- **Discord Community**: Real-time help
- **Email**: Business inquiries

### **Sharing Your Success**
- **Screenshots**: Share your running app
- **Feedback**: Report what works well
- **Suggestions**: Ideas for improvements
- **Bug Reports**: Any issues found

---

## 🎉 **You're All Set!**

Your HalalWise app is now ready with:
- ✅ **Fixed pubspec.yaml** (no more duplicate dependencies)
- ✅ **Gemini API key configured** (AIzaSyD8uelHhB2IEZWduY92L2CTt7sXzsD7oV8)
- ✅ **Complete database** with 1000+ ingredients
- ✅ **Sample products** for immediate testing
- ✅ **Beautiful Islamic UI** with smooth animations
- ✅ **Backend server** ready to deploy

**🚀 Run these commands to see your app:**
```bash
cd /Users/<USER>/Ai/HalalWise
flutter pub get
flutter run -d chrome
```

**🌟 Welcome to the future of halal food verification!**

---

**Questions?** Check the troubleshooting section above or ask for help!
