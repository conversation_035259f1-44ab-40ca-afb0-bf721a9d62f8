# Installation
> `npm install --save @types/request`

# Summary
This package contains type definitions for request (https://github.com/request/request).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/request.

### Additional Details
 * Last updated: Tu<PERSON>, 07 Nov 2023 15:11:36 GMT
 * Dependencies: [@types/caseless](https://npmjs.com/package/@types/caseless), [@types/node](https://npmjs.com/package/@types/node), [@types/tough-cookie](https://npmjs.com/package/@types/tough-cookie), [form-data](https://npmjs.com/package/form-data)

# Credits
These definitions were written by [<PERSON>](https://github.com/soywiz), [bonnici](https://github.com/bonnici), [<PERSON>](https://github.com/Bartvds), [<PERSON>](https://github.com/joeskeen), [<PERSON>](https://github.com/ccurrens), [<PERSON>](https://github.com/lookfirst), [<PERSON>](https://github.com/mastermatt), [<PERSON> <PERSON>](https://github.com/josecolella), and [Marek Urbanowicz](https://github.com/murbanowicz).
