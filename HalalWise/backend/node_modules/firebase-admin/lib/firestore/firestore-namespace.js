/*! firebase-admin v12.7.0 */
"use strict";
/*!
 * Copyright 2021 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.firestore = void 0;
const _firestore = require("@google-cloud/firestore");
/* eslint-disable @typescript-eslint/no-namespace */
var firestore;
(function (firestore) {
    /* eslint-disable @typescript-eslint/no-unused-vars */
    // See https://github.com/typescript-eslint/typescript-eslint/issues/363
    firestore.v1beta1 = _firestore.v1beta1;
    firestore.v1 = _firestore.v1;
    firestore.AggregateField = _firestore.AggregateField;
    firestore.AggregateQuery = _firestore.AggregateQuery;
    firestore.AggregateQuerySnapshot = _firestore.AggregateQuerySnapshot;
    firestore.BulkWriter = _firestore.BulkWriter;
    firestore.BundleBuilder = _firestore.BundleBuilder;
    firestore.CollectionGroup = _firestore.CollectionGroup;
    firestore.CollectionReference = _firestore.CollectionReference;
    firestore.DocumentReference = _firestore.DocumentReference;
    firestore.DocumentSnapshot = _firestore.DocumentSnapshot;
    firestore.FieldPath = _firestore.FieldPath;
    firestore.FieldValue = _firestore.FieldValue;
    firestore.Filter = _firestore.Filter;
    firestore.Firestore = _firestore.Firestore;
    firestore.GeoPoint = _firestore.GeoPoint;
    firestore.GrpcStatus = _firestore.GrpcStatus;
    firestore.Query = _firestore.Query;
    firestore.QueryDocumentSnapshot = _firestore.QueryDocumentSnapshot;
    firestore.QueryPartition = _firestore.QueryPartition;
    firestore.QuerySnapshot = _firestore.QuerySnapshot;
    firestore.Timestamp = _firestore.Timestamp;
    firestore.Transaction = _firestore.Transaction;
    firestore.WriteBatch = _firestore.WriteBatch;
    firestore.WriteResult = _firestore.WriteResult;
    firestore.setLogFunction = _firestore.setLogFunction;
})(firestore || (exports.firestore = firestore = {}));
