const https = require('https');
const fs = require('fs');
const path = require('path');

// Create self-signed certificate for development
const selfsigned = require('selfsigned');

// Generate self-signed certificate
const attrs = [{ name: 'commonName', value: 'localhost' }];
const pems = selfsigned.generate(attrs, { days: 365 });

// Write certificate files
fs.writeFileSync(path.join(__dirname, 'server.crt'), pems.cert);
fs.writeFileSync(path.join(__dirname, 'server.key'), pems.private);

console.log('✅ HTTPS certificates created!');
console.log('📁 Files created: server.crt, server.key');
console.log('🚀 Now run: node https-server.js');
