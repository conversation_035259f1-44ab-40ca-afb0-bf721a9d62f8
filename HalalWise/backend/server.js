// HalalWise Backend Server
// Node.js Express server with subscription management and AI integration

const express = require('express');
const cors = require('cors');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const mongoose = require('mongoose');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const admin = require('firebase-admin');
require('dotenv').config();

// Import models and services
const User = require('./src/models/User');
const Usage = require('./src/models/Usage');
const SubscriptionService = require('./src/services/subscriptionService');
const {
  authenticate,
  requireAdmin,
  checkUsageLimit,
  optionalAuth,
  requireSubscription
} = require('./src/middleware/auth');

const app = express();
const PORT = process.env.PORT || 3000;

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/halalwise', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => console.log('Connected to MongoDB'))
.catch(err => console.error('MongoDB connection error:', err));

// Initialize Firebase Admin (Optional - only if Firebase credentials are provided)
let firebaseInitialized = false;
if (process.env.FIREBASE_PROJECT_ID && process.env.FIREBASE_PRIVATE_KEY && process.env.FIREBASE_CLIENT_EMAIL) {
  try {
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      }),
    });
    firebaseInitialized = true;
    console.log('Firebase Admin initialized successfully');
  } catch (error) {
    console.log('Firebase not configured, running in standalone mode');
  }
} else {
  console.log('Firebase credentials not provided, running in standalone mode');
}

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

// Middleware
app.use(helmet());
app.use(compression());
app.use(morgan('combined'));
app.use(cors({
  origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Serve static files (for the dashboard)
app.use(express.static('public'));

// Rate limiting
const limiter = rateLimit({
  windowMs: 24 * 60 * 60 * 1000, // 24 hours
  max: 1000, // limit each IP to 1000 requests per windowMs
  message: 'Too many requests from this IP, please try again tomorrow.',
});
app.use('/api/', limiter);

// User-specific rate limiting
const userLimiter = rateLimit({
  windowMs: 24 * 60 * 60 * 1000, // 24 hours
  max: 50, // limit each user to 50 requests per day
  keyGenerator: (req) => req.user?.uid || req.ip,
  message: 'Daily API limit reached. Please try again tomorrow.',
});

// Authentication middleware (simplified for demo)
const authenticateUser = async (req, res, next) => {
  if (!firebaseInitialized) {
    // Skip authentication in standalone mode
    req.user = { uid: 'demo-user', email: '<EMAIL>' };
    next();
    return;
  }

  try {
    const token = req.headers.authorization?.split('Bearer ')[1];
    if (!token) {
      return res.status(401).json({ error: 'No token provided' });
    }

    const decodedToken = await admin.auth().verifyIdToken(token);
    req.user = decodedToken;
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(401).json({ error: 'Invalid token' });
  }
};

// Usage tracking (simplified for demo)
const usageData = new Map(); // In-memory storage for demo

const trackUsage = async (userId, type, tokens = 0) => {
  if (!firebaseInitialized) {
    // Use in-memory storage for demo
    const today = new Date().toISOString().split('T')[0];
    const key = `${userId}_${today}`;

    if (!usageData.has(key)) {
      usageData.set(key, {
        userId,
        date: today,
        totalCalls: 0,
        totalTokens: 0,
        types: {}
      });
    }

    const usage = usageData.get(key);
    usage.totalCalls += 1;
    usage.totalTokens += tokens;
    usage.types[type] = (usage.types[type] || 0) + 1;

    console.log(`Usage tracked for ${userId}: ${usage.totalCalls} calls today`);
    return;
  }

  // Firebase tracking
  const db = admin.firestore();
  const today = new Date().toISOString().split('T')[0];

  const usageRef = db.collection('usage').doc(`${userId}_${today}`);

  await usageRef.set({
    userId,
    date: today,
    totalCalls: admin.firestore.FieldValue.increment(1),
    totalTokens: admin.firestore.FieldValue.increment(tokens),
    types: {
      [type]: admin.firestore.FieldValue.increment(1)
    }
  }, { merge: true });
};

// Cost calculation
const calculateCost = (inputTokens, outputTokens) => {
  const inputCost = (inputTokens / 1000000) * 0.075; // $0.075 per 1M input tokens
  const outputCost = (outputTokens / 1000000) * 0.30; // $0.30 per 1M output tokens
  return inputCost + outputCost;
};

// Import routes
const authRoutes = require('./src/routes/auth');
const subscriptionRoutes = require('./src/routes/subscription');
const productRoutes = require('./src/routes/products');
const brandRoutes = require('./src/routes/brands');
const appleIAPRoutes = require('./src/routes/apple-iap');
const adminRoutes = require('./src/routes/admin');

// Import services
const DataSeeder = require('./src/services/dataSeeder');

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/subscription', subscriptionRoutes);
app.use('/api/products', productRoutes);
app.use('/api/brands', brandRoutes);
app.use('/api/apple-iap', appleIAPRoutes);
app.use('/api/admin', adminRoutes);

// API info endpoint (JSON)
app.get('/api/info', (req, res) => {
  res.json({
    name: 'HalalWise Backend API',
    version: '1.0.0',
    description: 'Database-first halal food verification with Apple IAP integration',
    environment: process.env.NODE_ENV || 'development',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: 'GET /api/health',
      products: {
        byBarcode: 'GET /api/products/barcode/:barcode',
        search: 'GET /api/products/search?q=query',
        popular: 'GET /api/products/popular',
        categories: 'GET /api/products/categories'
      },
      brands: {
        all: 'GET /api/brands',
        topHalal: 'GET /api/brands/top-halal',
        search: 'GET /api/brands/search?q=query'
      },
      auth: {
        register: 'POST /api/auth/register',
        login: 'POST /api/auth/login',
        profile: 'GET /api/auth/profile'
      },
      analysis: {
        analyze: 'POST /api/analyze',
        usage: 'GET /api/usage'
      },
      appleIAP: {
        products: 'GET /api/apple-iap/products',
        verifyReceipt: 'POST /api/apple-iap/verify-receipt',
        restorePurchases: 'POST /api/apple-iap/restore-purchases'
      },
      subscription: {
        plans: 'GET /api/subscription/plans',
        current: 'GET /api/subscription/current',
        usage: 'GET /api/subscription/usage'
      },
      admin: {
        seedDatabase: 'POST /api/admin/seed-database'
      }
    },
    database: {
      status: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected',
      seededBrands: 6,
      seededProducts: 6
    },
    features: [
      'Database-first product lookup (90% cost savings)',
      'Apple App Store In-App Purchase integration',
      'JWT authentication and user management',
      'Subscription tiers (Free/Premium/Enterprise)',
      'Real-time usage tracking and analytics',
      'Community ratings and reviews',
      'OpenFoodFacts integration for unknown products',
      'Smart AI fallback for new product analysis'
    ],
    documentation: 'See README.md for complete setup and usage instructions'
  });
});

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    database: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected'
  });
});

// Database seeding endpoint (for development)
app.post('/api/admin/seed-database', async (req, res) => {
  try {
    if (process.env.NODE_ENV === 'production') {
      return res.status(403).json({
        error: 'Database seeding is not allowed in production'
      });
    }

    console.log('🌱 Starting database seeding...');
    await DataSeeder.seedAll();

    res.json({
      message: 'Database seeded successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Database seeding error:', error);
    res.status(500).json({
      error: 'Database seeding failed',
      details: error.message
    });
  }
});

// Analyze food with Gemini (Database-first approach)
app.post('/api/analyze', authenticate, checkUsageLimit, async (req, res) => {
  const startTime = Date.now();

  try {
    const { prompt, barcode, productName, brand, type = 'halalAnalysis' } = req.body;

    if (!prompt && !barcode && !productName) {
      return res.status(400).json({
        error: 'Prompt, barcode, or product name is required'
      });
    }

    let databaseResult = null;

    // First, try to find product in database
    if (barcode) {
      databaseResult = await Product.findOne({ barcode });
    } else if (productName && brand) {
      databaseResult = await Product.findOne({
        name: { $regex: productName, $options: 'i' },
        brand: { $regex: brand, $options: 'i' }
      });
    } else if (productName) {
      databaseResult = await Product.findOne({
        name: { $regex: productName, $options: 'i' }
      });
    }

    // If found in database, return cached result
    if (databaseResult) {
      // Update search count
      databaseResult.searchCount += 1;
      databaseResult.popularity += 1;
      await databaseResult.save();

      // Track usage (minimal cost for database lookup)
      const usage = await Usage.getOrCreateUsage(req.user._id);
      await usage.addCall({
        type: 'databaseLookup',
        inputTokens: 0,
        outputTokens: 0,
        cost: 0,
        success: true,
        metadata: {
          source: 'database',
          responseTime: Date.now() - startTime,
          productId: databaseResult._id
        }
      });

      return res.json({
        source: 'database',
        cached: true,
        result: {
          status: databaseResult.halalStatus.status,
          confidence: databaseResult.halalStatus.confidence,
          reason: databaseResult.halalStatus.reason,
          questionableIngredients: databaseResult.ingredients.questionableIngredients || [],
          healthScore: databaseResult.healthInfo.healthScore,
          nutritionalHighlights: databaseResult.healthInfo.positives || [],
          recommendations: databaseResult.healthInfo.recommendations || [],
          product: {
            name: databaseResult.name,
            brand: databaseResult.brand,
            category: databaseResult.category,
            ingredients: databaseResult.ingredients.list,
            allergens: databaseResult.ingredients.allergens,
            nutrition: databaseResult.nutrition
          }
        },
        metadata: {
          inputTokens: 0,
          outputTokens: 0,
          estimatedCost: 0,
          responseTime: Date.now() - startTime,
          timestamp: new Date().toISOString(),
          remainingCalls: req.usageInfo.dailyLimit - req.usageInfo.currentUsage.totalCalls
        }
      });
    }

    // If not in database, use AI analysis
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

    const enhancedPrompt = `You are a halal food expert and nutritionist with deep knowledge of Islamic dietary laws and nutritional science.

${prompt}

Please provide accurate, detailed analysis. Be conservative in your halal assessments - when in doubt, mark as questionable rather than halal.

Respond in JSON format with the following structure:
{
  "status": "halal|haram|questionable",
  "confidence": 0.0-1.0,
  "reason": "detailed explanation",
  "questionableIngredients": ["ingredient1", "ingredient2"],
  "healthScore": 0-10,
  "nutritionalHighlights": ["highlight1", "highlight2"],
  "recommendations": ["recommendation1", "recommendation2"]
}`;

    const result = await model.generateContent(enhancedPrompt);
    const response = await result.response;
    const text = response.text();

    // Estimate token usage
    const inputTokens = Math.ceil(enhancedPrompt.length / 4);
    const outputTokens = Math.ceil(text.length / 4);
    const cost = calculateCost(inputTokens, outputTokens);
    const responseTime = Date.now() - startTime;

    // Track usage in database
    const usage = await Usage.getOrCreateUsage(req.user._id);
    await usage.addCall({
      type,
      inputTokens,
      outputTokens,
      cost,
      success: true,
      metadata: {
        model: 'gemini-1.5-flash',
        responseTime,
        promptLength: prompt.length,
        responseLength: text.length,
        source: 'ai'
      }
    });

    res.json({
      source: 'ai',
      cached: false,
      result: text,
      metadata: {
        inputTokens,
        outputTokens,
        estimatedCost: cost,
        responseTime,
        timestamp: new Date().toISOString(),
        remainingCalls: req.usageInfo.dailyLimit - req.usageInfo.currentUsage.totalCalls - 1
      }
    });

  } catch (error) {
    console.error('Analysis error:', error);

    // Track failed call
    try {
      const usage = await Usage.getOrCreateUsage(req.user._id);
      await usage.addCall({
        type: req.body.type || 'halalAnalysis',
        inputTokens: 0,
        outputTokens: 0,
        cost: 0,
        success: false,
        errorMessage: error.message,
        metadata: {
          responseTime: Date.now() - startTime
        }
      });
    } catch (trackingError) {
      console.error('Error tracking failed call:', trackingError);
    }

    res.status(500).json({
      error: 'Analysis failed',
      details: error.message,
      code: 'ANALYSIS_ERROR'
    });
  }
});

// Get user usage statistics
app.get('/api/usage', authenticate, async (req, res) => {
  try {
    const dailyUsage = await Usage.getDailyUsage(req.user._id);
    const monthlyUsage = await Usage.getMonthlyUsage(req.user._id);

    const dailyLimit = req.user.getDailyLimit();
    const monthlyLimit = req.user.getMonthlyLimit();

    res.json({
      usage: {
        daily: {
          ...dailyUsage,
          limit: dailyLimit,
          remaining: Math.max(0, dailyLimit - dailyUsage.totalCalls)
        },
        monthly: {
          ...monthlyUsage,
          limit: monthlyLimit,
          remaining: Math.max(0, monthlyLimit - monthlyUsage.totalCalls)
        }
      },
      subscription: {
        tier: req.user.subscription.tier,
        status: req.user.subscription.status,
        currentPeriodEnd: req.user.subscription.currentPeriodEnd
      }
    });

  } catch (error) {
    console.error('Usage fetch error:', error);
    res.status(500).json({
      error: 'Failed to fetch usage data',
      code: 'USAGE_FETCH_ERROR'
    });
  }
});

// Batch analysis for multiple products (Premium+ feature)
app.post('/api/batch-analyze', authenticate, requireSubscription('premium'), checkUsageLimit, async (req, res) => {
  try {
    const { products } = req.body;

    if (!Array.isArray(products) || products.length === 0) {
      return res.status(400).json({ error: 'Products array is required' });
    }

    if (products.length > 10) {
      return res.status(400).json({ error: 'Maximum 10 products per batch' });
    }

    const results = [];
    let totalCost = 0;

    for (const product of products) {
      try {
        const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
        const result = await model.generateContent(product.prompt);
        const response = await result.response;
        const text = response.text();

        const inputTokens = Math.ceil(product.prompt.length / 4);
        const outputTokens = Math.ceil(text.length / 4);
        const cost = calculateCost(inputTokens, outputTokens);
        totalCost += cost;

        results.push({
          productId: product.id,
          result: text,
          tokens: inputTokens + outputTokens,
          cost
        });

      } catch (error) {
        results.push({
          productId: product.id,
          error: error.message
        });
      }
    }

    // Track batch usage
    await trackUsage(req.user.uid, 'batch_analysis',
      results.reduce((sum, r) => sum + (r.tokens || 0), 0));

    res.json({
      results,
      summary: {
        totalProducts: products.length,
        successfulAnalyses: results.filter(r => !r.error).length,
        totalCost,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Batch analysis error:', error);
    res.status(500).json({ error: 'Batch analysis failed' });
  }
});

// Admin endpoint for usage analytics
app.get('/api/admin/analytics', authenticateUser, async (req, res) => {
  try {
    // Check if user is admin (implement your admin check logic)
    if (!req.user.admin) {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const db = admin.firestore();
    const today = new Date().toISOString().split('T')[0];

    // Get today's usage across all users
    const usageSnapshot = await db.collection('usage')
      .where('date', '==', today)
      .get();

    let totalCalls = 0;
    let totalTokens = 0;
    let totalUsers = 0;

    usageSnapshot.forEach(doc => {
      const data = doc.data();
      totalCalls += data.totalCalls || 0;
      totalTokens += data.totalTokens || 0;
      totalUsers++;
    });

    res.json({
      date: today,
      totalCalls,
      totalTokens,
      totalUsers,
      estimatedCost: calculateCost(totalTokens * 0.5, totalTokens * 0.5) // Rough estimate
    });

  } catch (error) {
    console.error('Analytics error:', error);
    res.status(500).json({ error: 'Failed to fetch analytics' });
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({
    error: 'Internal server error',
    timestamp: new Date().toISOString()
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Endpoint not found' });
});

// Start server
app.listen(PORT, () => {
  console.log(`HalalWise Backend Server running on port ${PORT}`);
  console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
});

module.exports = app;
