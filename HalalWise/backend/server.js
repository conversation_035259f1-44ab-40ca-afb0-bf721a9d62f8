// HalalWise Backend Server
// Node.js Express server for API management and cost control

const express = require('express');
const cors = require('cors');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const admin = require('firebase-admin');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Initialize Firebase Admin
admin.initializeApp({
  credential: admin.credential.cert({
    projectId: process.env.FIREBASE_PROJECT_ID,
    privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
  }),
});

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json({ limit: '10mb' }));

// Rate limiting
const limiter = rateLimit({
  windowMs: 24 * 60 * 60 * 1000, // 24 hours
  max: 1000, // limit each IP to 1000 requests per windowMs
  message: 'Too many requests from this IP, please try again tomorrow.',
});
app.use('/api/', limiter);

// User-specific rate limiting
const userLimiter = rateLimit({
  windowMs: 24 * 60 * 60 * 1000, // 24 hours
  max: 50, // limit each user to 50 requests per day
  keyGenerator: (req) => req.user?.uid || req.ip,
  message: 'Daily API limit reached. Please try again tomorrow.',
});

// Authentication middleware
const authenticateUser = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.split('Bearer ')[1];
    if (!token) {
      return res.status(401).json({ error: 'No token provided' });
    }

    const decodedToken = await admin.auth().verifyIdToken(token);
    req.user = decodedToken;
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(401).json({ error: 'Invalid token' });
  }
};

// Usage tracking
const trackUsage = async (userId, type, tokens = 0) => {
  const db = admin.firestore();
  const today = new Date().toISOString().split('T')[0];
  
  const usageRef = db.collection('usage').doc(`${userId}_${today}`);
  
  await usageRef.set({
    userId,
    date: today,
    totalCalls: admin.firestore.FieldValue.increment(1),
    totalTokens: admin.firestore.FieldValue.increment(tokens),
    types: {
      [type]: admin.firestore.FieldValue.increment(1)
    }
  }, { merge: true });
};

// Cost calculation
const calculateCost = (inputTokens, outputTokens) => {
  const inputCost = (inputTokens / 1000000) * 0.075; // $0.075 per 1M input tokens
  const outputCost = (outputTokens / 1000000) * 0.30; // $0.30 per 1M output tokens
  return inputCost + outputCost;
};

// Routes

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'healthy', timestamp: new Date().toISOString() });
});

// Analyze food with Gemini
app.post('/api/analyze', authenticateUser, userLimiter, async (req, res) => {
  try {
    const { prompt, type = 'halal_analysis' } = req.body;
    
    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    // Get Gemini model
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

    // Enhanced prompt with system instructions
    const enhancedPrompt = `You are a halal food expert and nutritionist with deep knowledge of Islamic dietary laws and nutritional science.

${prompt}

Please provide accurate, detailed analysis. Be conservative in your halal assessments - when in doubt, mark as questionable rather than halal.`;

    // Generate content
    const result = await model.generateContent(enhancedPrompt);
    const response = await result.response;
    const text = response.text();

    // Estimate token usage (approximate)
    const inputTokens = Math.ceil(enhancedPrompt.length / 4);
    const outputTokens = Math.ceil(text.length / 4);
    const cost = calculateCost(inputTokens, outputTokens);

    // Track usage
    await trackUsage(req.user.uid, type, inputTokens + outputTokens);

    res.json({
      result: text,
      metadata: {
        inputTokens,
        outputTokens,
        estimatedCost: cost,
        timestamp: new Date().toISOString(),
      }
    });

  } catch (error) {
    console.error('Analysis error:', error);
    res.status(500).json({ 
      error: 'Analysis failed', 
      details: error.message 
    });
  }
});

// Get user usage statistics
app.get('/api/usage', authenticateUser, async (req, res) => {
  try {
    const db = admin.firestore();
    const today = new Date().toISOString().split('T')[0];
    const usageRef = db.collection('usage').doc(`${req.user.uid}_${today}`);
    
    const doc = await usageRef.get();
    const usage = doc.exists ? doc.data() : {
      totalCalls: 0,
      totalTokens: 0,
      types: {}
    };

    res.json({
      today: usage,
      limits: {
        dailyCalls: 50,
        remainingCalls: Math.max(0, 50 - (usage.totalCalls || 0))
      }
    });

  } catch (error) {
    console.error('Usage fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch usage data' });
  }
});

// Batch analysis for multiple products
app.post('/api/batch-analyze', authenticateUser, async (req, res) => {
  try {
    const { products } = req.body;
    
    if (!Array.isArray(products) || products.length === 0) {
      return res.status(400).json({ error: 'Products array is required' });
    }

    if (products.length > 10) {
      return res.status(400).json({ error: 'Maximum 10 products per batch' });
    }

    const results = [];
    let totalCost = 0;

    for (const product of products) {
      try {
        const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
        const result = await model.generateContent(product.prompt);
        const response = await result.response;
        const text = response.text();

        const inputTokens = Math.ceil(product.prompt.length / 4);
        const outputTokens = Math.ceil(text.length / 4);
        const cost = calculateCost(inputTokens, outputTokens);
        totalCost += cost;

        results.push({
          productId: product.id,
          result: text,
          tokens: inputTokens + outputTokens,
          cost
        });

      } catch (error) {
        results.push({
          productId: product.id,
          error: error.message
        });
      }
    }

    // Track batch usage
    await trackUsage(req.user.uid, 'batch_analysis', 
      results.reduce((sum, r) => sum + (r.tokens || 0), 0));

    res.json({
      results,
      summary: {
        totalProducts: products.length,
        successfulAnalyses: results.filter(r => !r.error).length,
        totalCost,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Batch analysis error:', error);
    res.status(500).json({ error: 'Batch analysis failed' });
  }
});

// Admin endpoint for usage analytics
app.get('/api/admin/analytics', authenticateUser, async (req, res) => {
  try {
    // Check if user is admin (implement your admin check logic)
    if (!req.user.admin) {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const db = admin.firestore();
    const today = new Date().toISOString().split('T')[0];
    
    // Get today's usage across all users
    const usageSnapshot = await db.collection('usage')
      .where('date', '==', today)
      .get();

    let totalCalls = 0;
    let totalTokens = 0;
    let totalUsers = 0;

    usageSnapshot.forEach(doc => {
      const data = doc.data();
      totalCalls += data.totalCalls || 0;
      totalTokens += data.totalTokens || 0;
      totalUsers++;
    });

    res.json({
      date: today,
      totalCalls,
      totalTokens,
      totalUsers,
      estimatedCost: calculateCost(totalTokens * 0.5, totalTokens * 0.5) // Rough estimate
    });

  } catch (error) {
    console.error('Analytics error:', error);
    res.status(500).json({ error: 'Failed to fetch analytics' });
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({ 
    error: 'Internal server error',
    timestamp: new Date().toISOString()
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Endpoint not found' });
});

// Start server
app.listen(PORT, () => {
  console.log(`HalalWise Backend Server running on port ${PORT}`);
  console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
});

module.exports = app;
