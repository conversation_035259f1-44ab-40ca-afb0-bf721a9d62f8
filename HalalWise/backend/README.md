# HalalWise Backend API Server 🚀

> **Node.js Express server for API management, cost control, and Google Gemini AI integration**

## 🎯 Purpose

This backend server provides:
- **API Cost Control**: Rate limiting and usage tracking
- **Secure AI Integration**: Centralized Gemini API management
- **User Management**: Authentication and authorization
- **Analytics**: Usage statistics and cost monitoring
- **Scalability**: Handle thousands of concurrent users

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Flutter App   │───▶│  Backend API    │───▶│  Gemini AI API  │
│                 │    │                 │    │                 │
│ • Barcode Scan  │    │ • Rate Limiting │    │ • Text Analysis │
│ • User Auth     │    │ • Cost Control  │    │ • JSON Response │
│ • Usage Display │    │ • Analytics     │    │ • Safety Filter │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │  Firebase DB    │
                       │                 │
                       │ • User Data     │
                       │ • Usage Stats   │
                       │ • Analytics     │
                       └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites
```bash
# Required software
- Node.js 18+ 
- npm or yarn
- Firebase project
- Google Gemini API key
```

### Installation
```bash
# Clone and setup
cd HalalWise/backend
npm install

# Configure environment
cp .env.example .env
# Edit .env with your API keys

# Start development server
npm run dev
```

### Environment Setup
```bash
# Required environment variables
GEMINI_API_KEY=your_gemini_api_key_here
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----..."
FIREBASE_CLIENT_EMAIL=<EMAIL>
```

## 📡 API Endpoints

### Authentication
```bash
# All endpoints require Firebase Auth token
Authorization: Bearer <firebase_id_token>
```

### Core Endpoints

#### 1. Analyze Food Product
```bash
POST /api/analyze
Content-Type: application/json

{
  "prompt": "Analyze this product: Oreo cookies with ingredients...",
  "type": "halal_analysis"
}

Response:
{
  "result": "Based on the ingredients analysis...",
  "metadata": {
    "inputTokens": 150,
    "outputTokens": 200,
    "estimatedCost": 0.0001,
    "timestamp": "2024-12-01T10:00:00Z"
  }
}
```

#### 2. Batch Analysis
```bash
POST /api/batch-analyze
Content-Type: application/json

{
  "products": [
    {
      "id": "product1",
      "prompt": "Analyze product 1..."
    },
    {
      "id": "product2", 
      "prompt": "Analyze product 2..."
    }
  ]
}

Response:
{
  "results": [...],
  "summary": {
    "totalProducts": 2,
    "successfulAnalyses": 2,
    "totalCost": 0.0002
  }
}
```

#### 3. Usage Statistics
```bash
GET /api/usage

Response:
{
  "today": {
    "totalCalls": 15,
    "totalTokens": 3000,
    "types": {
      "halal_analysis": 12,
      "batch_analysis": 3
    }
  },
  "limits": {
    "dailyCalls": 50,
    "remainingCalls": 35
  }
}
```

## 🛡️ Security Features

### Rate Limiting
```javascript
// Global rate limit: 1000 requests per day per IP
// User rate limit: 50 requests per day per user
// Automatic reset at midnight UTC
```

### Authentication
```javascript
// Firebase ID token verification
// User role-based access control
// Admin endpoints protection
```

### Data Protection
```javascript
// Helmet.js security headers
// CORS configuration
// Input validation and sanitization
// Error message sanitization
```

## 💰 Cost Management

### Pricing Model (Gemini 1.5 Flash)
```
Input tokens:  $0.075 per 1M tokens
Output tokens: $0.30 per 1M tokens
Average cost:  ~$0.0001 per analysis
```

### Cost Controls
- **Daily limits**: 50 calls per user
- **Token limits**: 1000 tokens per request
- **Usage tracking**: Real-time monitoring
- **Cost estimation**: Transparent pricing

### Example Costs
```
Daily usage (50 calls): ~$0.005 per user
Monthly (1500 calls):   ~$0.15 per user
1000 users/month:       ~$150 total
```

## 📊 Monitoring & Analytics

### Usage Metrics
- Total API calls per day/month
- Token consumption tracking
- Cost analysis and forecasting
- User behavior patterns

### Performance Metrics
- Response time monitoring
- Error rate tracking
- Uptime monitoring
- Resource utilization

### Admin Dashboard
```bash
GET /api/admin/analytics
# Requires admin role

Response:
{
  "date": "2024-12-01",
  "totalCalls": 1250,
  "totalTokens": 250000,
  "totalUsers": 45,
  "estimatedCost": 0.125
}
```

## 🚀 Deployment Options

### Option 1: Google Cloud Run (Recommended)
```bash
# Build and deploy
gcloud builds submit --tag gcr.io/PROJECT_ID/halalwise-backend
gcloud run deploy --image gcr.io/PROJECT_ID/halalwise-backend --platform managed

# Auto-scaling: 0-1000 instances
# Pay per request pricing
# Built-in load balancing
```

### Option 2: Heroku
```bash
# Create Heroku app
heroku create halalwise-backend

# Set environment variables
heroku config:set GEMINI_API_KEY=your_key_here

# Deploy
git push heroku main
```

### Option 3: AWS Lambda + API Gateway
```bash
# Use Serverless Framework
npm install -g serverless
serverless deploy

# Benefits:
# - Pay per invocation
# - Automatic scaling
# - Low latency
```

### Option 4: Self-hosted VPS
```bash
# Ubuntu/Debian setup
sudo apt update
sudo apt install nodejs npm nginx

# PM2 for process management
npm install -g pm2
pm2 start server.js --name halalwise-backend
pm2 startup
pm2 save
```

## 🔧 Configuration

### Environment Variables
```bash
# Server
PORT=3000
NODE_ENV=production

# Google Gemini AI
GEMINI_API_KEY=your_gemini_api_key

# Firebase Admin
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----..."
FIREBASE_CLIENT_EMAIL=<EMAIL>

# Rate Limiting
MAX_DAILY_CALLS_PER_USER=50
MAX_BATCH_SIZE=10

# Security
JWT_SECRET=your_jwt_secret
ADMIN_EMAILS=<EMAIL>,<EMAIL>
```

### Firebase Setup
```bash
1. Create Firebase project
2. Enable Firestore Database
3. Enable Authentication
4. Generate Admin SDK private key
5. Add service account credentials to .env
```

### Gemini API Setup
```bash
1. Visit https://makersuite.google.com/app/apikey
2. Create new API key
3. Add to .env file
4. Test with curl:

curl -X POST \
  "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"contents":[{"parts":[{"text":"Hello"}]}]}'
```

## 🧪 Testing

### Run Tests
```bash
# Unit tests
npm test

# Integration tests
npm run test:integration

# Load testing
npm run test:load
```

### Manual Testing
```bash
# Health check
curl http://localhost:3000/api/health

# Test analysis (requires auth token)
curl -X POST http://localhost:3000/api/analyze \
  -H "Authorization: Bearer YOUR_FIREBASE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"prompt":"Test analysis","type":"halal_analysis"}'
```

## 📈 Scaling Considerations

### Performance Optimization
- **Caching**: Redis for frequent queries
- **CDN**: Static asset delivery
- **Database**: Connection pooling
- **Monitoring**: APM tools (New Relic, DataDog)

### Cost Optimization
- **Batch processing**: Group multiple requests
- **Caching**: Store common analysis results
- **Rate limiting**: Prevent abuse
- **Model selection**: Use appropriate Gemini model

### High Availability
- **Load balancing**: Multiple server instances
- **Database replication**: Firebase multi-region
- **Monitoring**: Health checks and alerts
- **Backup**: Regular data backups

## 🤝 Contributing

### Development Workflow
```bash
1. Fork repository
2. Create feature branch
3. Make changes
4. Add tests
5. Submit pull request
```

### Code Standards
- ESLint configuration
- Prettier formatting
- Jest testing
- JSDoc documentation

## 📞 Support

### Issues & Questions
- GitHub Issues: Technical problems
- Discord: Real-time support
- Email: Business inquiries

### Monitoring & Alerts
- Uptime monitoring: UptimeRobot
- Error tracking: Sentry
- Performance: Google Cloud Monitoring

---

**🚀 Ready to deploy? Choose your preferred option and follow the deployment guide above!**

**Need help?** Check our troubleshooting guide or contact support.
