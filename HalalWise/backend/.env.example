# HalalWise Backend Environment Variables

# Server Configuration
PORT=3000
NODE_ENV=development

# Google Gemini AI
GEMINI_API_KEY=your_gemini_api_key_here

# Firebase Admin SDK
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_PRIVATE_KEY="-----B<PERSON><PERSON> PRIVATE KEY-----\nyour_private_key_here\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>

# API Limits
MAX_DAILY_CALLS_PER_USER=50
MAX_BATCH_SIZE=10

# Security
JWT_SECRET=your_jwt_secret_here
ADMIN_EMAILS=<EMAIL>,<EMAIL>

# Monitoring (Optional)
SENTRY_DSN=your_sentry_dsn_here
GOOGLE_ANALYTICS_ID=your_ga_id_here
