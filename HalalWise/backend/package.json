{"name": "halalwise-backend", "version": "1.0.0", "description": "Backend API server for HalalWise app with Gemini AI integration", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "deploy": "gcloud app deploy"}, "keywords": ["halal", "food", "ai", "gemini", "api", "backend"], "author": "HalalWise Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "@google/generative-ai": "^0.2.1", "firebase-admin": "^12.0.0", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}