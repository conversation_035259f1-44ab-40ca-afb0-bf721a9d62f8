const mongoose = require('mongoose');

const brandSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  slug: {
    type: String,
    unique: true,
    index: true
  },

  // Brand Information
  description: String,
  website: String,
  logo: String,
  country: String,
  founded: Date,

  // Halal Information
  halalInfo: {
    isHalalCertified: {
      type: Boolean,
      default: false
    },
    certificationBody: String,
    certificationNumber: String,
    certificationExpiry: Date,
    halalPolicy: String,
    halalStatement: String,
    lastVerified: Date
  },

  // Contact Information
  contact: {
    email: String,
    phone: String,
    address: {
      street: String,
      city: String,
      state: String,
      country: String,
      zipCode: String
    }
  },

  // Social Media
  socialMedia: {
    facebook: String,
    twitter: String,
    instagram: String,
    linkedin: String,
    youtube: String
  },

  // Statistics
  stats: {
    totalProducts: {
      type: Number,
      default: 0
    },
    halalProducts: {
      type: Number,
      default: 0
    },
    haramProducts: {
      type: Number,
      default: 0
    },
    questionableProducts: {
      type: Number,
      default: 0
    },
    averageHealthScore: {
      type: Number,
      default: 0
    },
    averageRating: {
      type: Number,
      default: 0
    }
  },

  // Categories this brand operates in
  categories: [String],

  // Trust Score (based on consistency of halal products)
  trustScore: {
    type: Number,
    min: 0,
    max: 10,
    default: 5
  },

  // Community feedback
  community: {
    reviews: [{
      userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      rating: {
        type: Number,
        min: 1,
        max: 5
      },
      review: String,
      date: {
        type: Date,
        default: Date.now
      }
    }],
    averageRating: {
      type: Number,
      default: 0
    },
    totalReviews: {
      type: Number,
      default: 0
    }
  },

  // Metadata
  isActive: {
    type: Boolean,
    default: true
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  verifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  verifiedDate: Date,

  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Create slug from name
brandSchema.pre('save', function(next) {
  if (this.isModified('name') || this.isNew) {
    this.slug = this.name.toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  }
  this.updatedAt = Date.now();
  next();
});

// Calculate trust score based on product halal consistency
brandSchema.methods.calculateTrustScore = function() {
  const total = this.stats.totalProducts;
  if (total === 0) {
    this.trustScore = 5;
    return this.trustScore;
  }

  const halalPercentage = this.stats.halalProducts / total;
  const haramPercentage = this.stats.haramProducts / total;

  let score = 5; // Base score

  // High halal percentage increases trust
  if (halalPercentage > 0.9) score += 3;
  else if (halalPercentage > 0.7) score += 2;
  else if (halalPercentage > 0.5) score += 1;

  // High haram percentage decreases trust
  if (haramPercentage > 0.3) score -= 3;
  else if (haramPercentage > 0.1) score -= 1;

  // Certification adds trust
  if (this.halalInfo.isHalalCertified) score += 2;

  // Community rating affects trust
  if (this.community.averageRating > 4) score += 1;
  else if (this.community.averageRating < 2) score -= 1;

  this.trustScore = Math.max(0, Math.min(10, score));
  return this.trustScore;
};

// Update brand statistics
brandSchema.methods.updateStats = async function() {
  const Product = mongoose.model('Product');

  const stats = await Product.aggregate([
    { $match: { brand: this.name } },
    {
      $group: {
        _id: null,
        totalProducts: { $sum: 1 },
        halalProducts: {
          $sum: { $cond: [{ $eq: ['$halalStatus.status', 'halal'] }, 1, 0] }
        },
        haramProducts: {
          $sum: { $cond: [{ $eq: ['$halalStatus.status', 'haram'] }, 1, 0] }
        },
        questionableProducts: {
          $sum: { $cond: [{ $eq: ['$halalStatus.status', 'questionable'] }, 1, 0] }
        },
        averageHealthScore: { $avg: '$healthInfo.healthScore' },
        averageRating: { $avg: '$community.averageRating' }
      }
    }
  ]);

  if (stats.length > 0) {
    this.stats = {
      ...this.stats,
      ...stats[0],
      averageHealthScore: Math.round(stats[0].averageHealthScore * 10) / 10,
      averageRating: Math.round(stats[0].averageRating * 10) / 10
    };
  }

  // Update categories
  const categories = await Product.distinct('category', { brand: this.name });
  this.categories = categories;

  // Recalculate trust score
  this.calculateTrustScore();

  return this.save();
};

// Add community review
brandSchema.methods.addReview = function(userId, rating, review) {
  this.community.reviews.push({
    userId,
    rating,
    review
  });

  // Recalculate average rating
  const totalRating = this.community.reviews.reduce((sum, r) => sum + r.rating, 0);
  this.community.averageRating = totalRating / this.community.reviews.length;
  this.community.totalReviews = this.community.reviews.length;

  return this.save();
};

// Static method to get top halal brands
brandSchema.statics.getTopHalalBrands = function(limit = 10) {
  return this.find({
    isActive: true,
    'stats.totalProducts': { $gte: 5 }
  })
  .sort({
    trustScore: -1,
    'stats.halalProducts': -1,
    'community.averageRating': -1
  })
  .limit(limit);
};

// Static method to search brands
brandSchema.statics.searchBrands = function(query, filters = {}) {
  const searchQuery = {
    isActive: true,
    $or: [
      { name: { $regex: query, $options: 'i' } },
      { description: { $regex: query, $options: 'i' } }
    ]
  };

  if (filters.country) {
    searchQuery.country = filters.country;
  }

  if (filters.isHalalCertified !== undefined) {
    searchQuery['halalInfo.isHalalCertified'] = filters.isHalalCertified;
  }

  if (filters.category) {
    searchQuery.categories = filters.category;
  }

  return this.find(searchQuery)
    .sort({ trustScore: -1, 'community.averageRating': -1 })
    .limit(filters.limit || 20);
};

module.exports = mongoose.model('Brand', brandSchema);
