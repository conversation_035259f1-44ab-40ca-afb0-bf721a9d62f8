const mongoose = require('mongoose');

const productSchema = new mongoose.Schema({
  // Basic Product Information
  barcode: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  name: {
    type: String,
    required: true,
    index: true
  },
  brand: {
    type: String,
    required: true,
    index: true
  },
  category: {
    type: String,
    required: true,
    index: true
  },
  subcategory: String,
  
  // Halal Information
  halalStatus: {
    status: {
      type: String,
      enum: ['halal', 'haram', 'questionable', 'unknown'],
      required: true,
      index: true
    },
    confidence: {
      type: Number,
      min: 0,
      max: 1,
      default: 0
    },
    reason: String,
    certificationBody: String,
    certificationNumber: String,
    certificationExpiry: Date,
    lastVerified: {
      type: Date,
      default: Date.now
    },
    verifiedBy: {
      type: String,
      enum: ['ai', 'manual', 'certification', 'community'],
      default: 'ai'
    }
  },

  // Ingredients
  ingredients: {
    list: [String],
    allergens: [String],
    questionableIngredients: [String],
    halalIngredients: [String],
    haramIngredients: [String]
  },

  // Nutritional Information
  nutrition: {
    servingSize: String,
    calories: Number,
    totalFat: Number,
    saturatedFat: Number,
    transFat: Number,
    cholesterol: Number,
    sodium: Number,
    totalCarbohydrates: Number,
    dietaryFiber: Number,
    sugars: Number,
    addedSugars: Number,
    protein: Number,
    vitaminD: Number,
    calcium: Number,
    iron: Number,
    potassium: Number
  },

  // Health Information
  healthInfo: {
    healthScore: {
      type: Number,
      min: 0,
      max: 10,
      default: 5
    },
    healthGrade: {
      type: String,
      enum: ['A', 'B', 'C', 'D', 'E'],
      default: 'C'
    },
    positives: [String],
    negatives: [String],
    warnings: [String],
    recommendations: [String]
  },

  // Product Details
  details: {
    description: String,
    weight: String,
    volume: String,
    packaging: String,
    origin: String,
    manufacturer: String,
    distributor: String,
    importedBy: String,
    countryOfOrigin: String,
    productionDate: Date,
    expiryDate: Date
  },

  // Images and Media
  images: {
    front: String,
    back: String,
    ingredients: String,
    nutrition: String,
    additional: [String]
  },

  // Pricing and Availability
  pricing: {
    averagePrice: Number,
    currency: {
      type: String,
      default: 'USD'
    },
    priceHistory: [{
      price: Number,
      date: Date,
      source: String
    }]
  },

  // External Data Sources
  externalData: {
    openFoodFactsId: String,
    upcDatabase: String,
    amazonAsin: String,
    walmartId: String,
    targetId: String
  },

  // Community Data
  community: {
    ratings: [{
      userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      rating: {
        type: Number,
        min: 1,
        max: 5
      },
      review: String,
      halalConfirmation: {
        type: String,
        enum: ['halal', 'haram', 'questionable']
      },
      date: {
        type: Date,
        default: Date.now
      }
    }],
    averageRating: {
      type: Number,
      default: 0
    },
    totalReviews: {
      type: Number,
      default: 0
    },
    reportedIssues: [{
      userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      issue: String,
      type: {
        type: String,
        enum: ['incorrect_halal_status', 'wrong_ingredients', 'outdated_info', 'other']
      },
      date: {
        type: Date,
        default: Date.now
      },
      resolved: {
        type: Boolean,
        default: false
      }
    }]
  },

  // Metadata
  metadata: {
    dataSource: {
      type: String,
      enum: ['manual', 'openfoodfacts', 'upc_database', 'ai_analysis', 'user_submission'],
      default: 'manual'
    },
    dataQuality: {
      type: String,
      enum: ['high', 'medium', 'low'],
      default: 'medium'
    },
    lastUpdated: {
      type: Date,
      default: Date.now
    },
    updateCount: {
      type: Number,
      default: 0
    },
    verified: {
      type: Boolean,
      default: false
    },
    verifiedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    verifiedDate: Date
  },

  // Search and Discovery
  searchTags: [String],
  popularity: {
    type: Number,
    default: 0
  },
  searchCount: {
    type: Number,
    default: 0
  },

  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Indexes for efficient searching
productSchema.index({ barcode: 1 });
productSchema.index({ name: 'text', brand: 'text', category: 'text' });
productSchema.index({ 'halalStatus.status': 1 });
productSchema.index({ brand: 1, category: 1 });
productSchema.index({ 'ingredients.allergens': 1 });
productSchema.index({ 'metadata.dataQuality': 1 });
productSchema.index({ popularity: -1 });
productSchema.index({ 'community.averageRating': -1 });

// Update the updatedAt field before saving
productSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  this.metadata.updateCount += 1;
  next();
});

// Calculate health score based on nutrition
productSchema.methods.calculateHealthScore = function() {
  let score = 5; // Base score
  
  if (this.nutrition.calories) {
    if (this.nutrition.calories < 100) score += 1;
    else if (this.nutrition.calories > 300) score -= 1;
  }
  
  if (this.nutrition.sugars) {
    if (this.nutrition.sugars < 5) score += 1;
    else if (this.nutrition.sugars > 15) score -= 2;
  }
  
  if (this.nutrition.sodium) {
    if (this.nutrition.sodium < 200) score += 1;
    else if (this.nutrition.sodium > 800) score -= 2;
  }
  
  if (this.nutrition.saturatedFat) {
    if (this.nutrition.saturatedFat < 2) score += 1;
    else if (this.nutrition.saturatedFat > 10) score -= 1;
  }
  
  if (this.nutrition.protein && this.nutrition.protein > 10) score += 1;
  if (this.nutrition.dietaryFiber && this.nutrition.dietaryFiber > 5) score += 1;
  
  this.healthInfo.healthScore = Math.max(0, Math.min(10, score));
  
  // Set health grade
  if (this.healthInfo.healthScore >= 8) this.healthInfo.healthGrade = 'A';
  else if (this.healthInfo.healthScore >= 6) this.healthInfo.healthGrade = 'B';
  else if (this.healthInfo.healthScore >= 4) this.healthInfo.healthGrade = 'C';
  else if (this.healthInfo.healthScore >= 2) this.healthInfo.healthGrade = 'D';
  else this.healthInfo.healthGrade = 'E';
  
  return this.healthInfo.healthScore;
};

// Add community rating
productSchema.methods.addRating = function(userId, rating, review, halalConfirmation) {
  this.community.ratings.push({
    userId,
    rating,
    review,
    halalConfirmation
  });
  
  // Recalculate average rating
  const totalRating = this.community.ratings.reduce((sum, r) => sum + r.rating, 0);
  this.community.averageRating = totalRating / this.community.ratings.length;
  this.community.totalReviews = this.community.ratings.length;
  
  return this.save();
};

// Static method to search products
productSchema.statics.searchProducts = function(query, filters = {}) {
  const searchQuery = {
    $or: [
      { name: { $regex: query, $options: 'i' } },
      { brand: { $regex: query, $options: 'i' } },
      { barcode: query },
      { searchTags: { $in: [new RegExp(query, 'i')] } }
    ]
  };
  
  if (filters.halalStatus) {
    searchQuery['halalStatus.status'] = filters.halalStatus;
  }
  
  if (filters.category) {
    searchQuery.category = filters.category;
  }
  
  if (filters.brand) {
    searchQuery.brand = filters.brand;
  }
  
  if (filters.allergens) {
    searchQuery['ingredients.allergens'] = { $nin: filters.allergens };
  }
  
  return this.find(searchQuery)
    .sort({ popularity: -1, 'community.averageRating': -1 })
    .limit(filters.limit || 20);
};

module.exports = mongoose.model('Product', productSchema);
