const axios = require('axios');
const jwt = require('jsonwebtoken');
const User = require('../models/User');

class AppleIAPService {
  constructor() {
    this.sandboxUrl = 'https://sandbox.itunes.apple.com/verifyReceipt';
    this.productionUrl = 'https://buy.itunes.apple.com/verifyReceipt';
    this.sharedSecret = process.env.APPLE_SHARED_SECRET;
    
    // Apple App Store Connect API credentials
    this.keyId = process.env.APPLE_KEY_ID;
    this.issuerId = process.env.APPLE_ISSUER_ID;
    this.privateKey = process.env.APPLE_PRIVATE_KEY;
    
    // Product IDs from App Store Connect
    this.productIds = {
      premium_monthly: 'com.halalwise.premium.monthly',
      premium_yearly: 'com.halalwise.premium.yearly',
      enterprise_monthly: 'com.halalwise.enterprise.monthly',
      enterprise_yearly: 'com.halalwise.enterprise.yearly'
    };
  }

  // Verify receipt with Apple
  async verifyReceipt(receiptData, isProduction = false) {
    try {
      const url = isProduction ? this.productionUrl : this.sandboxUrl;
      
      const requestBody = {
        'receipt-data': receiptData,
        'password': this.sharedSecret,
        'exclude-old-transactions': true
      };

      const response = await axios.post(url, requestBody, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });

      const result = response.data;

      // If sandbox receipt was sent to production, try sandbox
      if (result.status === 21007 && isProduction) {
        return this.verifyReceipt(receiptData, false);
      }

      return result;
    } catch (error) {
      console.error('Apple receipt verification error:', error);
      throw new Error('Failed to verify receipt with Apple');
    }
  }

  // Process subscription purchase
  async processSubscription(userId, receiptData, isProduction = false) {
    try {
      const verificationResult = await this.verifyReceipt(receiptData, isProduction);
      
      if (verificationResult.status !== 0) {
        throw new Error(`Receipt verification failed: ${this.getStatusMessage(verificationResult.status)}`);
      }

      const receipt = verificationResult.receipt;
      const latestReceiptInfo = verificationResult.latest_receipt_info;
      
      if (!latestReceiptInfo || latestReceiptInfo.length === 0) {
        throw new Error('No subscription information found in receipt');
      }

      // Get the most recent transaction
      const latestTransaction = latestReceiptInfo[latestReceiptInfo.length - 1];
      
      // Determine subscription tier from product ID
      const productId = latestTransaction.product_id;
      const subscriptionTier = this.getSubscriptionTier(productId);
      
      if (!subscriptionTier) {
        throw new Error(`Unknown product ID: ${productId}`);
      }

      // Update user subscription
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Parse dates
      const purchaseDate = new Date(parseInt(latestTransaction.purchase_date_ms));
      const expiresDate = new Date(parseInt(latestTransaction.expires_date_ms));
      const isActive = expiresDate > new Date();

      // Update user subscription
      user.subscription.tier = subscriptionTier;
      user.subscription.status = isActive ? 'active' : 'expired';
      user.subscription.currentPeriodStart = purchaseDate;
      user.subscription.currentPeriodEnd = expiresDate;
      user.subscription.appleTransactionId = latestTransaction.transaction_id;
      user.subscription.appleOriginalTransactionId = latestTransaction.original_transaction_id;
      user.subscription.appleProductId = productId;
      user.usage.dailyLimit = user.getDailyLimit();
      user.usage.monthlyLimit = user.getMonthlyLimit();

      await user.save();

      return {
        success: true,
        subscription: user.subscription,
        transactionId: latestTransaction.transaction_id,
        expiresDate: expiresDate
      };

    } catch (error) {
      console.error('Process subscription error:', error);
      throw error;
    }
  }

  // Get subscription tier from product ID
  getSubscriptionTier(productId) {
    if (productId.includes('premium')) {
      return 'premium';
    } else if (productId.includes('enterprise')) {
      return 'enterprise';
    }
    return null;
  }

  // Get status message for Apple receipt verification status codes
  getStatusMessage(status) {
    const statusMessages = {
      0: 'Valid receipt',
      21000: 'The App Store could not read the JSON object you provided',
      21002: 'The data in the receipt-data property was malformed or missing',
      21003: 'The receipt could not be authenticated',
      21004: 'The shared secret you provided does not match the shared secret on file',
      21005: 'The receipt server is not currently available',
      21006: 'This receipt is valid but the subscription has expired',
      21007: 'This receipt is from the test environment, but it was sent to the production environment',
      21008: 'This receipt is from the production environment, but it was sent to the test environment',
      21010: 'This receipt could not be authorized'
    };
    
    return statusMessages[status] || `Unknown status: ${status}`;
  }

  // Check subscription status
  async checkSubscriptionStatus(userId) {
    try {
      const user = await User.findById(userId);
      if (!user || !user.subscription.appleTransactionId) {
        return { isActive: false, message: 'No Apple subscription found' };
      }

      // For active subscriptions, we should periodically verify with Apple
      // This is a simplified check - in production, you'd want to verify with Apple's servers
      const isActive = user.subscription.currentPeriodEnd > new Date();
      
      return {
        isActive,
        tier: user.subscription.tier,
        expiresDate: user.subscription.currentPeriodEnd,
        transactionId: user.subscription.appleTransactionId
      };

    } catch (error) {
      console.error('Check subscription status error:', error);
      throw error;
    }
  }

  // Handle subscription renewal/cancellation via Apple's Server-to-Server notifications
  async handleServerNotification(notificationData) {
    try {
      const notificationType = notificationData.notification_type;
      const latestReceiptInfo = notificationData.latest_receipt_info;
      
      if (!latestReceiptInfo) {
        throw new Error('No receipt info in notification');
      }

      // Find user by original transaction ID
      const originalTransactionId = latestReceiptInfo.original_transaction_id;
      const user = await User.findOne({
        'subscription.appleOriginalTransactionId': originalTransactionId
      });

      if (!user) {
        console.warn(`User not found for transaction ID: ${originalTransactionId}`);
        return;
      }

      switch (notificationType) {
        case 'INITIAL_BUY':
        case 'DID_RENEW':
          // Subscription renewed
          const expiresDate = new Date(parseInt(latestReceiptInfo.expires_date_ms));
          user.subscription.status = 'active';
          user.subscription.currentPeriodEnd = expiresDate;
          await user.save();
          console.log(`Subscription renewed for user ${user._id}`);
          break;

        case 'DID_FAIL_TO_RENEW':
          // Subscription failed to renew
          user.subscription.status = 'past_due';
          await user.save();
          console.log(`Subscription failed to renew for user ${user._id}`);
          break;

        case 'CANCEL':
          // Subscription cancelled
          user.subscription.status = 'cancelled';
          user.subscription.tier = 'free';
          user.usage.dailyLimit = 10;
          user.usage.monthlyLimit = 300;
          await user.save();
          console.log(`Subscription cancelled for user ${user._id}`);
          break;

        case 'DID_CHANGE_RENEWAL_STATUS':
          // Auto-renewal status changed
          const autoRenewStatus = latestReceiptInfo.auto_renew_status;
          user.subscription.cancelAtPeriodEnd = autoRenewStatus === '0';
          await user.save();
          console.log(`Auto-renewal status changed for user ${user._id}: ${autoRenewStatus}`);
          break;

        default:
          console.log(`Unhandled notification type: ${notificationType}`);
      }

    } catch (error) {
      console.error('Handle server notification error:', error);
      throw error;
    }
  }

  // Get available products for the app
  getAvailableProducts() {
    return {
      premium_monthly: {
        productId: this.productIds.premium_monthly,
        tier: 'premium',
        duration: 'monthly',
        price: '$9.99',
        features: [
          'Up to 100 daily API calls',
          'Advanced halal analysis',
          'Health insights',
          'Batch processing',
          'Priority support'
        ]
      },
      premium_yearly: {
        productId: this.productIds.premium_yearly,
        tier: 'premium',
        duration: 'yearly',
        price: '$99.99',
        savings: '17%',
        features: [
          'Up to 100 daily API calls',
          'Advanced halal analysis',
          'Health insights',
          'Batch processing',
          'Priority support',
          '2 months free'
        ]
      },
      enterprise_monthly: {
        productId: this.productIds.enterprise_monthly,
        tier: 'enterprise',
        duration: 'monthly',
        price: '$29.99',
        features: [
          'Up to 1000 daily API calls',
          'All premium features',
          'Image analysis',
          'API access',
          'Custom integrations',
          'Dedicated support'
        ]
      },
      enterprise_yearly: {
        productId: this.productIds.enterprise_yearly,
        tier: 'enterprise',
        duration: 'yearly',
        price: '$299.99',
        savings: '17%',
        features: [
          'Up to 1000 daily API calls',
          'All premium features',
          'Image analysis',
          'API access',
          'Custom integrations',
          'Dedicated support',
          '2 months free'
        ]
      }
    };
  }

  // Generate App Store Connect API JWT token (for advanced features)
  generateAppStoreConnectToken() {
    if (!this.keyId || !this.issuerId || !this.privateKey) {
      throw new Error('Apple App Store Connect credentials not configured');
    }

    const now = Math.floor(Date.now() / 1000);
    
    const payload = {
      iss: this.issuerId,
      iat: now,
      exp: now + 1200, // 20 minutes
      aud: 'appstoreconnect-v1',
      bid: process.env.APPLE_BUNDLE_ID || 'com.halalwise.app'
    };

    const header = {
      alg: 'ES256',
      kid: this.keyId,
      typ: 'JWT'
    };

    return jwt.sign(payload, this.privateKey, { 
      algorithm: 'ES256',
      header: header
    });
  }
}

module.exports = AppleIAPService;
