const Product = require('../models/Product');
const Brand = require('../models/Brand');

class DataSeeder {
  
  // Seed initial brands
  static async seedBrands() {
    const brands = [
      {
        name: 'Nestlé',
        description: 'Global food and beverage company',
        website: 'https://www.nestle.com',
        country: 'Switzerland',
        founded: new Date('1866-01-01'),
        halalInfo: {
          isHalalCertified: true,
          certificationBody: 'JAKIM',
          halalPolicy: 'Nestlé is committed to providing halal products in Muslim markets'
        },
        categories: ['Beverages', 'Confectionery', 'Dairy', 'Cereals']
      },
      {
        name: 'Unilever',
        description: 'British-Dutch multinational consumer goods company',
        website: 'https://www.unilever.com',
        country: 'Netherlands',
        founded: new Date('1929-01-01'),
        halalInfo: {
          isHalalCertified: true,
          certificationBody: 'MUI',
          halalPolicy: 'Unilever ensures halal compliance in Muslim-majority countries'
        },
        categories: ['Personal Care', 'Food', 'Ice Cream']
      },
      {
        name: 'Coca-Cola',
        description: 'American multinational beverage corporation',
        website: 'https://www.coca-cola.com',
        country: 'United States',
        founded: new Date('1886-01-01'),
        halalInfo: {
          isHalalCertified: true,
          certificationBody: 'IFANCA',
          halalPolicy: 'Coca-Cola products are halal certified in many countries'
        },
        categories: ['Beverages', 'Soft Drinks']
      },
      {
        name: 'Kellogg\'s',
        description: 'American multinational food manufacturing company',
        website: 'https://www.kelloggs.com',
        country: 'United States',
        founded: new Date('1906-01-01'),
        halalInfo: {
          isHalalCertified: false,
          halalPolicy: 'Some products may contain non-halal ingredients'
        },
        categories: ['Cereals', 'Snacks', 'Breakfast Foods']
      },
      {
        name: 'Al Marai',
        description: 'Saudi Arabian dairy company',
        website: 'https://www.almarai.com',
        country: 'Saudi Arabia',
        founded: new Date('1977-01-01'),
        halalInfo: {
          isHalalCertified: true,
          certificationBody: 'SASO',
          halalPolicy: 'All products are halal certified'
        },
        categories: ['Dairy', 'Beverages', 'Bakery']
      },
      {
        name: 'Haldiram\'s',
        description: 'Indian multinational sweets and snacks manufacturer',
        website: 'https://www.haldirams.com',
        country: 'India',
        founded: new Date('1937-01-01'),
        halalInfo: {
          isHalalCertified: true,
          certificationBody: 'JAMIATUL ULAMA',
          halalPolicy: 'Vegetarian products, halal certified'
        },
        categories: ['Snacks', 'Sweets', 'Ready-to-Eat']
      }
    ];

    for (const brandData of brands) {
      try {
        const existingBrand = await Brand.findOne({ name: brandData.name });
        if (!existingBrand) {
          const brand = new Brand(brandData);
          await brand.save();
          console.log(`✅ Seeded brand: ${brandData.name}`);
        }
      } catch (error) {
        console.error(`❌ Error seeding brand ${brandData.name}:`, error.message);
      }
    }
  }

  // Seed initial products
  static async seedProducts() {
    const products = [
      // Nestlé Products
      {
        barcode: '7613031349418',
        name: 'KitKat 4 Finger',
        brand: 'Nestlé',
        category: 'Confectionery',
        subcategory: 'Chocolate Bars',
        halalStatus: {
          status: 'halal',
          confidence: 0.95,
          reason: 'Halal certified by JAKIM',
          certificationBody: 'JAKIM',
          verifiedBy: 'certification'
        },
        ingredients: {
          list: ['Sugar', 'Wheat Flour', 'Cocoa Butter', 'Cocoa Mass', 'Skimmed Milk Powder', 'Milk Fat', 'Lactose', 'Emulsifier (Lecithin)', 'Raising Agent (Sodium Bicarbonate)', 'Natural Vanilla Flavoring'],
          allergens: ['Gluten', 'Milk', 'May contain nuts'],
          halalIngredients: ['Sugar', 'Wheat Flour', 'Cocoa Butter', 'Cocoa Mass']
        },
        nutrition: {
          servingSize: '41.5g (1 bar)',
          calories: 218,
          totalFat: 11.2,
          saturatedFat: 6.6,
          sodium: 16,
          totalCarbohydrates: 26.6,
          sugars: 22.9,
          protein: 3.0
        },
        details: {
          weight: '41.5g',
          packaging: 'Wrapper',
          countryOfOrigin: 'Malaysia',
          manufacturer: 'Nestlé Malaysia'
        }
      },
      {
        barcode: '7613036716429',
        name: 'Nescafé Original 3-in-1',
        brand: 'Nestlé',
        category: 'Beverages',
        subcategory: 'Instant Coffee',
        halalStatus: {
          status: 'halal',
          confidence: 0.98,
          reason: 'Halal certified, no animal-derived ingredients',
          certificationBody: 'JAKIM',
          verifiedBy: 'certification'
        },
        ingredients: {
          list: ['Sugar', 'Glucose Syrup', 'Instant Coffee', 'Hydrogenated Palm Kernel Oil', 'Stabilizers', 'Emulsifiers', 'Anti-caking Agent'],
          allergens: ['May contain milk'],
          halalIngredients: ['Sugar', 'Instant Coffee', 'Palm Oil']
        },
        nutrition: {
          servingSize: '20g (1 sachet)',
          calories: 74,
          totalFat: 2.1,
          saturatedFat: 1.9,
          sodium: 58,
          totalCarbohydrates: 13.1,
          sugars: 11.2,
          protein: 0.8
        }
      },
      // Coca-Cola Products
      {
        barcode: '5449000000996',
        name: 'Coca-Cola Classic',
        brand: 'Coca-Cola',
        category: 'Beverages',
        subcategory: 'Soft Drinks',
        halalStatus: {
          status: 'halal',
          confidence: 0.99,
          reason: 'No animal-derived ingredients, halal certified',
          certificationBody: 'IFANCA',
          verifiedBy: 'certification'
        },
        ingredients: {
          list: ['Carbonated Water', 'Sugar', 'Colour (Caramel E150d)', 'Phosphoric Acid', 'Natural Flavourings', 'Caffeine'],
          allergens: [],
          halalIngredients: ['Carbonated Water', 'Sugar', 'Natural Flavourings']
        },
        nutrition: {
          servingSize: '330ml',
          calories: 139,
          totalFat: 0,
          saturatedFat: 0,
          sodium: 15,
          totalCarbohydrates: 35,
          sugars: 35,
          protein: 0
        }
      },
      // Al Marai Products
      {
        barcode: '6281007001234',
        name: 'Al Marai Fresh Milk Full Fat',
        brand: 'Al Marai',
        category: 'Dairy',
        subcategory: 'Milk',
        halalStatus: {
          status: 'halal',
          confidence: 1.0,
          reason: 'Halal certified dairy product',
          certificationBody: 'SASO',
          verifiedBy: 'certification'
        },
        ingredients: {
          list: ['Fresh Cow Milk', 'Vitamin D3'],
          allergens: ['Milk'],
          halalIngredients: ['Fresh Cow Milk', 'Vitamin D3']
        },
        nutrition: {
          servingSize: '200ml',
          calories: 126,
          totalFat: 6.6,
          saturatedFat: 4.2,
          sodium: 88,
          totalCarbohydrates: 9.4,
          sugars: 9.4,
          protein: 6.4,
          calcium: 228
        }
      },
      // Kellogg's Products (Questionable)
      {
        barcode: '3800020256107',
        name: 'Kellogg\'s Corn Flakes',
        brand: 'Kellogg\'s',
        category: 'Cereals',
        subcategory: 'Breakfast Cereals',
        halalStatus: {
          status: 'questionable',
          confidence: 0.6,
          reason: 'Contains vitamin D3 which may be from animal source',
          verifiedBy: 'ai'
        },
        ingredients: {
          list: ['Corn', 'Sugar', 'Salt', 'Barley Malt Extract', 'Vitamins (Vitamin C, Niacin, Vitamin B6, Vitamin B2, Vitamin B1, Folic Acid, Vitamin D, Vitamin B12)', 'Iron'],
          allergens: ['Gluten', 'May contain milk'],
          questionableIngredients: ['Vitamin D3', 'Vitamin B12']
        },
        nutrition: {
          servingSize: '30g',
          calories: 113,
          totalFat: 0.3,
          saturatedFat: 0.1,
          sodium: 271,
          totalCarbohydrates: 25.2,
          sugars: 2.4,
          protein: 2.1,
          iron: 5.1
        }
      },
      // Haldiram's Products
      {
        barcode: '8901719101234',
        name: 'Haldiram\'s Aloo Bhujia',
        brand: 'Haldiram\'s',
        category: 'Snacks',
        subcategory: 'Namkeen',
        halalStatus: {
          status: 'halal',
          confidence: 0.95,
          reason: 'Vegetarian product, halal certified',
          certificationBody: 'JAMIATUL ULAMA',
          verifiedBy: 'certification'
        },
        ingredients: {
          list: ['Potato Flakes', 'Vegetable Oil', 'Gram Flour', 'Salt', 'Red Chili Powder', 'Turmeric Powder', 'Asafoetida', 'Spices'],
          allergens: ['May contain nuts'],
          halalIngredients: ['Potato Flakes', 'Vegetable Oil', 'Gram Flour', 'Spices']
        },
        nutrition: {
          servingSize: '100g',
          calories: 503,
          totalFat: 30.2,
          saturatedFat: 12.1,
          sodium: 1200,
          totalCarbohydrates: 48.5,
          protein: 12.8
        }
      }
    ];

    for (const productData of products) {
      try {
        const existingProduct = await Product.findOne({ barcode: productData.barcode });
        if (!existingProduct) {
          const product = new Product(productData);
          product.calculateHealthScore();
          await product.save();
          console.log(`✅ Seeded product: ${productData.name}`);
        }
      } catch (error) {
        console.error(`❌ Error seeding product ${productData.name}:`, error.message);
      }
    }
  }

  // Update brand statistics after seeding products
  static async updateBrandStats() {
    const brands = await Brand.find({});
    for (const brand of brands) {
      await brand.updateStats();
      console.log(`✅ Updated stats for brand: ${brand.name}`);
    }
  }

  // Main seeding function
  static async seedAll() {
    console.log('🌱 Starting database seeding...');
    
    try {
      await this.seedBrands();
      console.log('✅ Brands seeded successfully');
      
      await this.seedProducts();
      console.log('✅ Products seeded successfully');
      
      await this.updateBrandStats();
      console.log('✅ Brand statistics updated');
      
      console.log('🎉 Database seeding completed successfully!');
    } catch (error) {
      console.error('❌ Error during seeding:', error);
    }
  }

  // Seed from OpenFoodFacts API
  static async seedFromOpenFoodFacts(barcodes) {
    const axios = require('axios');
    
    for (const barcode of barcodes) {
      try {
        const response = await axios.get(`https://world.openfoodfacts.org/api/v0/product/${barcode}.json`);
        
        if (response.data.status === 1) {
          const product = response.data.product;
          
          const productData = {
            barcode: barcode,
            name: product.product_name || 'Unknown Product',
            brand: product.brands || 'Unknown Brand',
            category: product.categories_tags?.[0]?.replace('en:', '') || 'Unknown',
            halalStatus: {
              status: 'unknown',
              confidence: 0,
              reason: 'Requires analysis',
              verifiedBy: 'ai'
            },
            ingredients: {
              list: product.ingredients_text ? product.ingredients_text.split(',').map(i => i.trim()) : [],
              allergens: product.allergens_tags ? product.allergens_tags.map(a => a.replace('en:', '')) : []
            },
            nutrition: {
              servingSize: product.serving_size,
              calories: product.nutriments?.['energy-kcal_100g'],
              totalFat: product.nutriments?.fat_100g,
              saturatedFat: product.nutriments?.['saturated-fat_100g'],
              sodium: product.nutriments?.sodium_100g,
              totalCarbohydrates: product.nutriments?.carbohydrates_100g,
              sugars: product.nutriments?.sugars_100g,
              protein: product.nutriments?.proteins_100g
            },
            images: {
              front: product.image_front_url,
              ingredients: product.image_ingredients_url,
              nutrition: product.image_nutrition_url
            },
            externalData: {
              openFoodFactsId: barcode
            },
            metadata: {
              dataSource: 'openfoodfacts',
              dataQuality: 'medium'
            }
          };

          const existingProduct = await Product.findOne({ barcode });
          if (!existingProduct) {
            const newProduct = new Product(productData);
            newProduct.calculateHealthScore();
            await newProduct.save();
            console.log(`✅ Imported from OpenFoodFacts: ${productData.name}`);
          }
        }
      } catch (error) {
        console.error(`❌ Error importing barcode ${barcode}:`, error.message);
      }
    }
  }
}

module.exports = DataSeeder;
