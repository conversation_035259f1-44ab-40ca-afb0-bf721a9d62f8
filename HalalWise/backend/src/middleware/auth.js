const jwt = require('jsonwebtoken');
const User = require('../models/User');
const Usage = require('../models/Usage');

// Generate JWT token
const generateToken = (userId) => {
  return jwt.sign({ userId }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN || '7d'
  });
};

// Verify JWT token
const verifyToken = (token) => {
  return jwt.verify(token, process.env.JWT_SECRET);
};

// Authentication middleware
const authenticate = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: 'Access denied. No token provided.',
        code: 'NO_TOKEN'
      });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    try {
      const decoded = verifyToken(token);
      const user = await User.findById(decoded.userId).select('-password');

      if (!user) {
        return res.status(401).json({
          error: 'Invalid token. User not found.',
          code: 'USER_NOT_FOUND'
        });
      }

      // Check if user's subscription is active
      if (!user.hasActiveSubscription() && user.subscription.tier !== 'free') {
        return res.status(403).json({
          error: 'Subscription expired. Please renew your subscription.',
          code: 'SUBSCRIPTION_EXPIRED'
        });
      }

      req.user = user;
      next();
    } catch (jwtError) {
      return res.status(401).json({
        error: 'Invalid token.',
        code: 'INVALID_TOKEN'
      });
    }
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(500).json({
      error: 'Authentication failed',
      code: 'AUTH_ERROR'
    });
  }
};



// Usage limit middleware
const checkUsageLimit = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    const dailyLimit = req.user.getDailyLimit();
    const hasExceededLimit = await Usage.checkDailyLimit(req.user._id, dailyLimit);

    if (hasExceededLimit) {
      return res.status(429).json({
        error: 'Daily API limit exceeded',
        code: 'LIMIT_EXCEEDED',
        limit: dailyLimit,
        upgradeUrl: '/api/subscription/upgrade'
      });
    }

    // Add usage info to request for tracking
    req.usageInfo = {
      dailyLimit,
      currentUsage: await Usage.getDailyUsage(req.user._id)
    };

    next();
  } catch (error) {
    console.error('Usage limit check error:', error);
    res.status(500).json({
      error: 'Usage limit check failed',
      code: 'USAGE_CHECK_ERROR'
    });
  }
};

// Optional authentication (for public endpoints that can benefit from user context)
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      req.user = null;
      return next();
    }

    const token = authHeader.substring(7);

    try {
      const decoded = verifyToken(token);
      const user = await User.findById(decoded.userId).select('-password');
      req.user = user;
    } catch (jwtError) {
      req.user = null;
    }

    next();
  } catch (error) {
    req.user = null;
    next();
  }
};

// Subscription tier middleware
const requireSubscription = (minTier = 'premium') => {
  const tierLevels = {
    free: 0,
    premium: 1,
    enterprise: 2
  };

  return (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        });
      }

      const userTierLevel = tierLevels[req.user.subscription.tier] || 0;
      const requiredTierLevel = tierLevels[minTier] || 1;

      if (userTierLevel < requiredTierLevel) {
        return res.status(403).json({
          error: `${minTier} subscription required`,
          code: 'SUBSCRIPTION_REQUIRED',
          currentTier: req.user.subscription.tier,
          requiredTier: minTier,
          upgradeUrl: '/api/subscription/upgrade'
        });
      }

      next();
    } catch (error) {
      console.error('Subscription check error:', error);
      res.status(500).json({
        error: 'Subscription check failed',
        code: 'SUBSCRIPTION_CHECK_ERROR'
      });
    }
  };
};

// Admin authorization middleware
const requireAdmin = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    // Check if user is admin (you can customize this logic)
    const adminEmails = process.env.ADMIN_EMAILS?.split(',') || ['<EMAIL>'];

    if (!adminEmails.includes(req.user.email)) {
      return res.status(403).json({
        error: 'Admin access required',
        code: 'ADMIN_REQUIRED'
      });
    }

    next();
  } catch (error) {
    console.error('Admin authorization error:', error);
    res.status(500).json({
      error: 'Authorization failed',
      code: 'ADMIN_AUTH_ERROR'
    });
  }
};

// Rate limiting by subscription tier
const subscriptionRateLimit = (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    // Different rate limits based on subscription tier
    const rateLimits = {
      free: { windowMs: 60 * 1000, max: 5 }, // 5 requests per minute
      premium: { windowMs: 60 * 1000, max: 20 }, // 20 requests per minute
      enterprise: { windowMs: 60 * 1000, max: 100 } // 100 requests per minute
    };

    const userLimit = rateLimits[req.user.subscription.tier] || rateLimits.free;

    // Store rate limit info for potential rate limiting middleware
    req.rateLimit = userLimit;

    next();
  } catch (error) {
    console.error('Rate limit check error:', error);
    res.status(500).json({
      error: 'Rate limit check failed',
      code: 'RATE_LIMIT_ERROR'
    });
  }
};

module.exports = {
  generateToken,
  verifyToken,
  authenticate,
  requireAdmin,
  checkUsageLimit,
  optionalAuth,
  requireSubscription,
  subscriptionRateLimit
};
