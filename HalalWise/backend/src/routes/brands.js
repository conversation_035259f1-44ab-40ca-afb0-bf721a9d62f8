const express = require('express');
const { body, validationResult, query } = require('express-validator');
const Brand = require('../models/Brand');
const Product = require('../models/Product');
const { authenticate, optionalAuth } = require('../middleware/auth');

const router = express.Router();

// Get all brands with pagination
router.get('/', [
  query('page').optional().isInt({ min: 1 }).toInt(),
  query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
  query('sort').optional().isIn(['name', 'trustScore', 'totalProducts', 'averageRating']),
  query('order').optional().isIn(['asc', 'desc'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const sort = req.query.sort || 'trustScore';
    const order = req.query.order === 'asc' ? 1 : -1;
    const skip = (page - 1) * limit;

    const sortObj = {};
    sortObj[sort] = order;

    const brands = await Brand.find({ isActive: true })
      .sort(sortObj)
      .skip(skip)
      .limit(limit);

    const total = await Brand.countDocuments({ isActive: true });

    res.json({
      brands,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Get brands error:', error);
    res.status(500).json({
      error: 'Failed to get brands',
      code: 'BRANDS_ERROR'
    });
  }
});

// Search brands
router.get('/search', [
  query('q').notEmpty().withMessage('Search query is required'),
  query('limit').optional().isInt({ min: 1, max: 50 }).toInt(),
  query('country').optional().isString(),
  query('isHalalCertified').optional().isBoolean(),
  query('category').optional().isString()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { q, limit = 20, country, isHalalCertified, category } = req.query;
    
    const filters = {
      limit: parseInt(limit),
      ...(country && { country }),
      ...(isHalalCertified !== undefined && { isHalalCertified: isHalalCertified === 'true' }),
      ...(category && { category })
    };

    const brands = await Brand.searchBrands(q, filters);

    res.json({
      query: q,
      filters,
      results: brands.length,
      brands
    });

  } catch (error) {
    console.error('Brand search error:', error);
    res.status(500).json({
      error: 'Search failed',
      code: 'BRAND_SEARCH_ERROR'
    });
  }
});

// Get top halal brands
router.get('/top-halal', [
  query('limit').optional().isInt({ min: 1, max: 50 }).toInt()
], async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;
    const brands = await Brand.getTopHalalBrands(limit);

    res.json({
      brands,
      count: brands.length
    });

  } catch (error) {
    console.error('Top halal brands error:', error);
    res.status(500).json({
      error: 'Failed to get top halal brands',
      code: 'TOP_HALAL_BRANDS_ERROR'
    });
  }
});

// Get brand by ID or slug
router.get('/:identifier', async (req, res) => {
  try {
    const { identifier } = req.params;
    
    // Try to find by ID first, then by slug
    let brand = await Brand.findById(identifier);
    if (!brand) {
      brand = await Brand.findOne({ slug: identifier, isActive: true });
    }
    
    if (!brand) {
      return res.status(404).json({
        error: 'Brand not found'
      });
    }

    // Get brand products
    const products = await Product.find({ brand: brand.name })
      .sort({ popularity: -1 })
      .limit(20);

    // Get brand statistics
    const stats = await Product.aggregate([
      { $match: { brand: brand.name } },
      {
        $group: {
          _id: null,
          totalProducts: { $sum: 1 },
          halalProducts: {
            $sum: { $cond: [{ $eq: ['$halalStatus.status', 'halal'] }, 1, 0] }
          },
          haramProducts: {
            $sum: { $cond: [{ $eq: ['$halalStatus.status', 'haram'] }, 1, 0] }
          },
          questionableProducts: {
            $sum: { $cond: [{ $eq: ['$halalStatus.status', 'questionable'] }, 1, 0] }
          },
          averageHealthScore: { $avg: '$healthInfo.healthScore' },
          averageRating: { $avg: '$community.averageRating' }
        }
      }
    ]);

    res.json({
      brand,
      products,
      statistics: stats[0] || {
        totalProducts: 0,
        halalProducts: 0,
        haramProducts: 0,
        questionableProducts: 0,
        averageHealthScore: 0,
        averageRating: 0
      }
    });

  } catch (error) {
    console.error('Brand details error:', error);
    res.status(500).json({
      error: 'Failed to get brand details',
      code: 'BRAND_DETAILS_ERROR'
    });
  }
});

// Get brand products
router.get('/:identifier/products', [
  query('page').optional().isInt({ min: 1 }).toInt(),
  query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
  query('halalStatus').optional().isIn(['halal', 'haram', 'questionable', 'unknown']),
  query('category').optional().isString(),
  query('sort').optional().isIn(['name', 'popularity', 'healthScore', 'rating'])
], async (req, res) => {
  try {
    const { identifier } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const { halalStatus, category, sort = 'popularity' } = req.query;
    const skip = (page - 1) * limit;

    // Find brand
    let brand = await Brand.findById(identifier);
    if (!brand) {
      brand = await Brand.findOne({ slug: identifier, isActive: true });
    }
    
    if (!brand) {
      return res.status(404).json({
        error: 'Brand not found'
      });
    }

    // Build query
    const query = { brand: brand.name };
    if (halalStatus) query['halalStatus.status'] = halalStatus;
    if (category) query.category = category;

    // Build sort
    const sortObj = {};
    switch (sort) {
      case 'name':
        sortObj.name = 1;
        break;
      case 'healthScore':
        sortObj['healthInfo.healthScore'] = -1;
        break;
      case 'rating':
        sortObj['community.averageRating'] = -1;
        break;
      default:
        sortObj.popularity = -1;
    }

    const products = await Product.find(query)
      .sort(sortObj)
      .skip(skip)
      .limit(limit);

    const total = await Product.countDocuments(query);

    res.json({
      brand: {
        id: brand._id,
        name: brand.name,
        slug: brand.slug
      },
      products,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Brand products error:', error);
    res.status(500).json({
      error: 'Failed to get brand products',
      code: 'BRAND_PRODUCTS_ERROR'
    });
  }
});

// Add brand review
router.post('/:identifier/review', [
  authenticate,
  body('rating').isInt({ min: 1, max: 5 }),
  body('review').optional().isString().isLength({ max: 1000 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { identifier } = req.params;
    const { rating, review } = req.body;

    // Find brand
    let brand = await Brand.findById(identifier);
    if (!brand) {
      brand = await Brand.findOne({ slug: identifier, isActive: true });
    }
    
    if (!brand) {
      return res.status(404).json({
        error: 'Brand not found'
      });
    }

    // Check if user already reviewed this brand
    const existingReview = brand.community.reviews.find(
      r => r.userId.toString() === req.user._id.toString()
    );

    if (existingReview) {
      return res.status(400).json({
        error: 'You have already reviewed this brand'
      });
    }

    await brand.addReview(req.user._id, rating, review);

    res.json({
      message: 'Review added successfully',
      averageRating: brand.community.averageRating,
      totalReviews: brand.community.totalReviews
    });

  } catch (error) {
    console.error('Add brand review error:', error);
    res.status(500).json({
      error: 'Failed to add review',
      code: 'BRAND_REVIEW_ERROR'
    });
  }
});

// Get brand categories
router.get('/categories', async (req, res) => {
  try {
    const categories = await Brand.aggregate([
      { $unwind: '$categories' },
      {
        $group: {
          _id: '$categories',
          count: { $sum: 1 },
          halalCertifiedCount: {
            $sum: { $cond: ['$halalInfo.isHalalCertified', 1, 0] }
          }
        }
      },
      {
        $project: {
          category: '$_id',
          count: 1,
          halalCertifiedCount: 1,
          halalPercentage: { 
            $multiply: [{ $divide: ['$halalCertifiedCount', '$count'] }, 100] 
          }
        }
      },
      { $sort: { count: -1 } }
    ]);

    res.json({
      categories
    });

  } catch (error) {
    console.error('Brand categories error:', error);
    res.status(500).json({
      error: 'Failed to get brand categories',
      code: 'BRAND_CATEGORIES_ERROR'
    });
  }
});

// Update brand statistics (admin only)
router.post('/:identifier/update-stats', [
  authenticate,
  // Add admin check here if needed
], async (req, res) => {
  try {
    const { identifier } = req.params;

    let brand = await Brand.findById(identifier);
    if (!brand) {
      brand = await Brand.findOne({ slug: identifier, isActive: true });
    }
    
    if (!brand) {
      return res.status(404).json({
        error: 'Brand not found'
      });
    }

    await brand.updateStats();

    res.json({
      message: 'Brand statistics updated successfully',
      stats: brand.stats,
      trustScore: brand.trustScore
    });

  } catch (error) {
    console.error('Update brand stats error:', error);
    res.status(500).json({
      error: 'Failed to update brand statistics',
      code: 'BRAND_STATS_UPDATE_ERROR'
    });
  }
});

module.exports = router;
