const express = require('express');
const { body, validationResult } = require('express-validator');
const AppleIAPService = require('../services/appleIAPService');
const { authenticate } = require('../middleware/auth');

const router = express.Router();
const appleIAP = new AppleIAPService();

// Get available Apple IAP products
router.get('/products', async (req, res) => {
  try {
    const products = appleIAP.getAvailableProducts();
    
    res.json({
      products,
      message: 'Configure these product IDs in your iOS app and App Store Connect'
    });
  } catch (error) {
    console.error('Get Apple IAP products error:', error);
    res.status(500).json({
      error: 'Failed to get products',
      code: 'APPLE_IAP_PRODUCTS_ERROR'
    });
  }
});

// Verify and process Apple IAP receipt
router.post('/verify-receipt', [
  authenticate,
  body('receiptData').notEmpty().withMessage('Receipt data is required'),
  body('isProduction').optional().isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { receiptData, isProduction = false } = req.body;
    
    const result = await appleIAP.processSubscription(
      req.user._id, 
      receiptData, 
      isProduction
    );

    res.json({
      message: 'Receipt verified and subscription updated successfully',
      subscription: result.subscription,
      transactionId: result.transactionId,
      expiresDate: result.expiresDate
    });

  } catch (error) {
    console.error('Verify Apple IAP receipt error:', error);
    
    // Return specific error messages for common issues
    let errorMessage = 'Failed to verify receipt';
    let errorCode = 'APPLE_IAP_VERIFICATION_ERROR';
    
    if (error.message.includes('Receipt verification failed')) {
      errorMessage = error.message;
      errorCode = 'APPLE_RECEIPT_INVALID';
    } else if (error.message.includes('User not found')) {
      errorMessage = 'User account not found';
      errorCode = 'USER_NOT_FOUND';
    } else if (error.message.includes('Unknown product ID')) {
      errorMessage = 'Invalid product purchased';
      errorCode = 'INVALID_PRODUCT';
    }

    res.status(400).json({
      error: errorMessage,
      code: errorCode
    });
  }
});

// Check current Apple subscription status
router.get('/subscription-status', authenticate, async (req, res) => {
  try {
    const status = await appleIAP.checkSubscriptionStatus(req.user._id);
    
    res.json({
      subscription: status,
      user: {
        tier: req.user.subscription.tier,
        status: req.user.subscription.status,
        dailyLimit: req.user.getDailyLimit(),
        monthlyLimit: req.user.getMonthlyLimit()
      }
    });

  } catch (error) {
    console.error('Check Apple subscription status error:', error);
    res.status(500).json({
      error: 'Failed to check subscription status',
      code: 'APPLE_SUBSCRIPTION_STATUS_ERROR'
    });
  }
});

// Apple Server-to-Server notification webhook
router.post('/webhook', express.raw({ type: 'application/json' }), async (req, res) => {
  try {
    // Parse the notification
    const notification = JSON.parse(req.body.toString());
    
    // Log the notification for debugging
    console.log('Apple Server-to-Server notification received:', {
      type: notification.notification_type,
      environment: notification.environment,
      bundleId: notification.bundle_id
    });

    // Verify the notification is for our app
    const expectedBundleId = process.env.APPLE_BUNDLE_ID || 'com.halalwise.app';
    if (notification.bundle_id !== expectedBundleId) {
      console.warn(`Notification for wrong bundle ID: ${notification.bundle_id}`);
      return res.status(400).json({ error: 'Invalid bundle ID' });
    }

    // Process the notification
    await appleIAP.handleServerNotification(notification);
    
    res.status(200).json({ status: 'ok' });

  } catch (error) {
    console.error('Apple webhook error:', error);
    res.status(500).json({ error: 'Webhook processing failed' });
  }
});

// Restore purchases (for users who reinstall the app)
router.post('/restore-purchases', [
  authenticate,
  body('receiptData').notEmpty().withMessage('Receipt data is required'),
  body('isProduction').optional().isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { receiptData, isProduction = false } = req.body;
    
    // Verify the receipt to get all transactions
    const verificationResult = await appleIAP.verifyReceipt(receiptData, isProduction);
    
    if (verificationResult.status !== 0) {
      throw new Error(`Receipt verification failed: ${appleIAP.getStatusMessage(verificationResult.status)}`);
    }

    const latestReceiptInfo = verificationResult.latest_receipt_info;
    
    if (!latestReceiptInfo || latestReceiptInfo.length === 0) {
      return res.json({
        message: 'No previous purchases found',
        hasActiveSubscription: false
      });
    }

    // Find the most recent active subscription
    let activeSubscription = null;
    const now = new Date();
    
    for (const transaction of latestReceiptInfo) {
      const expiresDate = new Date(parseInt(transaction.expires_date_ms));
      if (expiresDate > now) {
        activeSubscription = transaction;
        break;
      }
    }

    if (activeSubscription) {
      // Restore the active subscription
      const result = await appleIAP.processSubscription(
        req.user._id, 
        receiptData, 
        isProduction
      );

      res.json({
        message: 'Subscription restored successfully',
        hasActiveSubscription: true,
        subscription: result.subscription,
        transactionId: result.transactionId,
        expiresDate: result.expiresDate
      });
    } else {
      res.json({
        message: 'No active subscription found to restore',
        hasActiveSubscription: false,
        expiredTransactions: latestReceiptInfo.length
      });
    }

  } catch (error) {
    console.error('Restore purchases error:', error);
    res.status(400).json({
      error: 'Failed to restore purchases',
      details: error.message,
      code: 'APPLE_RESTORE_ERROR'
    });
  }
});

// Get subscription history
router.get('/subscription-history', authenticate, async (req, res) => {
  try {
    const user = req.user;
    
    if (!user.subscription.appleOriginalTransactionId) {
      return res.json({
        message: 'No Apple subscription history found',
        history: []
      });
    }

    // In a real implementation, you might want to store transaction history
    // For now, return the current subscription info
    const history = [{
      transactionId: user.subscription.appleTransactionId,
      originalTransactionId: user.subscription.appleOriginalTransactionId,
      productId: user.subscription.appleProductId,
      tier: user.subscription.tier,
      status: user.subscription.status,
      purchaseDate: user.subscription.currentPeriodStart,
      expiresDate: user.subscription.currentPeriodEnd
    }];

    res.json({
      history,
      currentSubscription: {
        tier: user.subscription.tier,
        status: user.subscription.status,
        isActive: user.hasActiveSubscription()
      }
    });

  } catch (error) {
    console.error('Get subscription history error:', error);
    res.status(500).json({
      error: 'Failed to get subscription history',
      code: 'APPLE_HISTORY_ERROR'
    });
  }
});

// Cancel subscription (sets auto-renewal to false)
router.post('/cancel-subscription', authenticate, async (req, res) => {
  try {
    const user = req.user;
    
    if (!user.subscription.appleTransactionId) {
      return res.status(400).json({
        error: 'No Apple subscription found to cancel',
        code: 'NO_APPLE_SUBSCRIPTION'
      });
    }

    // Note: Actual cancellation must be done by the user in iOS Settings
    // We can only mark it as "will cancel at period end"
    user.subscription.cancelAtPeriodEnd = true;
    await user.save();

    res.json({
      message: 'Subscription marked for cancellation at period end',
      subscription: {
        tier: user.subscription.tier,
        status: user.subscription.status,
        cancelAtPeriodEnd: user.subscription.cancelAtPeriodEnd,
        currentPeriodEnd: user.subscription.currentPeriodEnd
      },
      note: 'To fully cancel, the user must disable auto-renewal in iOS Settings > Apple ID > Subscriptions'
    });

  } catch (error) {
    console.error('Cancel Apple subscription error:', error);
    res.status(500).json({
      error: 'Failed to cancel subscription',
      code: 'APPLE_CANCEL_ERROR'
    });
  }
});

module.exports = router;
