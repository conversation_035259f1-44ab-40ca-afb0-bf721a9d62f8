const express = require('express');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const { body, validationResult } = require('express-validator');
const SubscriptionService = require('../services/subscriptionService');
const { authenticate } = require('../middleware/auth');

const router = express.Router();

// Get available subscription plans
router.get('/plans', async (req, res) => {
  try {
    const plans = SubscriptionService.getPlans();
    res.json({
      plans: Object.keys(plans).map(key => ({
        id: key,
        ...plans[key]
      }))
    });
  } catch (error) {
    console.error('Error fetching plans:', error);
    res.status(500).json({
      error: 'Failed to fetch subscription plans',
      code: 'PLANS_FETCH_ERROR'
    });
  }
});

// Get current user's subscription info
router.get('/current', authenticate, async (req, res) => {
  try {
    const subscriptionInfo = await SubscriptionService.getSubscriptionInfo(req.user._id);
    res.json({
      subscription: subscriptionInfo
    });
  } catch (error) {
    console.error('Error fetching subscription info:', error);
    res.status(500).json({
      error: 'Failed to fetch subscription information',
      code: 'SUBSCRIPTION_FETCH_ERROR'
    });
  }
});

// Create new subscription
router.post('/create', [
  authenticate,
  body('planType').isIn(['premium', 'enterprise'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { planType } = req.body;
    
    const result = await SubscriptionService.createSubscription(req.user._id, planType);
    
    res.json({
      message: 'Subscription created successfully',
      subscriptionId: result.subscriptionId,
      clientSecret: result.clientSecret
    });

  } catch (error) {
    console.error('Error creating subscription:', error);
    res.status(500).json({
      error: error.message || 'Failed to create subscription',
      code: 'SUBSCRIPTION_CREATE_ERROR'
    });
  }
});

// Update subscription plan
router.put('/update', [
  authenticate,
  body('planType').isIn(['premium', 'enterprise'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { planType } = req.body;
    
    const subscription = await SubscriptionService.updateSubscription(req.user._id, planType);
    
    res.json({
      message: 'Subscription updated successfully',
      subscription
    });

  } catch (error) {
    console.error('Error updating subscription:', error);
    res.status(500).json({
      error: error.message || 'Failed to update subscription',
      code: 'SUBSCRIPTION_UPDATE_ERROR'
    });
  }
});

// Cancel subscription
router.post('/cancel', [
  authenticate,
  body('immediate').optional().isBoolean()
], async (req, res) => {
  try {
    const { immediate = false } = req.body;
    
    const subscription = await SubscriptionService.cancelSubscription(req.user._id, immediate);
    
    res.json({
      message: immediate ? 'Subscription cancelled immediately' : 'Subscription will be cancelled at the end of the current period',
      subscription
    });

  } catch (error) {
    console.error('Error cancelling subscription:', error);
    res.status(500).json({
      error: error.message || 'Failed to cancel subscription',
      code: 'SUBSCRIPTION_CANCEL_ERROR'
    });
  }
});

// Reactivate subscription
router.post('/reactivate', authenticate, async (req, res) => {
  try {
    const subscription = await SubscriptionService.reactivateSubscription(req.user._id);
    
    res.json({
      message: 'Subscription reactivated successfully',
      subscription
    });

  } catch (error) {
    console.error('Error reactivating subscription:', error);
    res.status(500).json({
      error: error.message || 'Failed to reactivate subscription',
      code: 'SUBSCRIPTION_REACTIVATE_ERROR'
    });
  }
});

// Create Stripe customer portal session
router.post('/portal', authenticate, async (req, res) => {
  try {
    if (!req.user.subscription.stripeCustomerId) {
      return res.status(400).json({
        error: 'No Stripe customer found',
        code: 'NO_CUSTOMER'
      });
    }

    const session = await stripe.billingPortal.sessions.create({
      customer: req.user.subscription.stripeCustomerId,
      return_url: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/settings/subscription`
    });

    res.json({
      url: session.url
    });

  } catch (error) {
    console.error('Error creating portal session:', error);
    res.status(500).json({
      error: 'Failed to create billing portal session',
      code: 'PORTAL_ERROR'
    });
  }
});

// Stripe webhook endpoint
router.post('/webhook', express.raw({ type: 'application/json' }), async (req, res) => {
  const sig = req.headers['stripe-signature'];
  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, process.env.STRIPE_WEBHOOK_SECRET);
  } catch (err) {
    console.error('Webhook signature verification failed:', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  try {
    await SubscriptionService.handleWebhook(event);
    res.json({ received: true });
  } catch (error) {
    console.error('Webhook handling failed:', error);
    res.status(500).json({ error: 'Webhook handling failed' });
  }
});

// Get subscription usage and limits
router.get('/usage', authenticate, async (req, res) => {
  try {
    const Usage = require('../models/Usage');
    
    const dailyUsage = await Usage.getDailyUsage(req.user._id);
    const monthlyUsage = await Usage.getMonthlyUsage(req.user._id);
    
    const dailyLimit = req.user.getDailyLimit();
    const monthlyLimit = req.user.getMonthlyLimit();

    res.json({
      usage: {
        daily: {
          ...dailyUsage,
          limit: dailyLimit,
          remaining: Math.max(0, dailyLimit - dailyUsage.totalCalls)
        },
        monthly: {
          ...monthlyUsage,
          limit: monthlyLimit,
          remaining: Math.max(0, monthlyLimit - monthlyUsage.totalCalls)
        }
      },
      subscription: {
        tier: req.user.subscription.tier,
        status: req.user.subscription.status,
        currentPeriodEnd: req.user.subscription.currentPeriodEnd
      }
    });

  } catch (error) {
    console.error('Error fetching usage:', error);
    res.status(500).json({
      error: 'Failed to fetch usage information',
      code: 'USAGE_FETCH_ERROR'
    });
  }
});

// Upgrade recommendation endpoint
router.get('/upgrade-recommendation', authenticate, async (req, res) => {
  try {
    const Usage = require('../models/Usage');
    
    const dailyUsage = await Usage.getDailyUsage(req.user._id);
    const monthlyUsage = await Usage.getMonthlyUsage(req.user._id);
    
    const currentTier = req.user.subscription.tier;
    const dailyLimit = req.user.getDailyLimit();
    const monthlyLimit = req.user.getMonthlyLimit();

    let recommendation = null;

    // Check if user is approaching limits
    if (dailyUsage.totalCalls >= dailyLimit * 0.8) {
      if (currentTier === 'free') {
        recommendation = {
          suggestedTier: 'premium',
          reason: 'You are approaching your daily limit. Upgrade to Premium for 10x more API calls.',
          urgency: 'high'
        };
      } else if (currentTier === 'premium') {
        recommendation = {
          suggestedTier: 'enterprise',
          reason: 'You are approaching your daily limit. Upgrade to Enterprise for unlimited usage.',
          urgency: 'high'
        };
      }
    } else if (monthlyUsage.totalCalls >= monthlyLimit * 0.8) {
      if (currentTier === 'free') {
        recommendation = {
          suggestedTier: 'premium',
          reason: 'You are approaching your monthly limit. Upgrade to Premium for more capacity.',
          urgency: 'medium'
        };
      }
    }

    res.json({
      recommendation,
      currentUsage: {
        daily: dailyUsage,
        monthly: monthlyUsage
      },
      limits: {
        daily: dailyLimit,
        monthly: monthlyLimit
      }
    });

  } catch (error) {
    console.error('Error generating upgrade recommendation:', error);
    res.status(500).json({
      error: 'Failed to generate upgrade recommendation',
      code: 'RECOMMENDATION_ERROR'
    });
  }
});

module.exports = router;
