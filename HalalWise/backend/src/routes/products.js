const express = require('express');
const { body, validationResult, query } = require('express-validator');
const Product = require('../models/Product');
const Brand = require('../models/Brand');
const { authenticate, optionalAuth } = require('../middleware/auth');
const { GoogleGenerativeAI } = require('@google/generative-ai');

const router = express.Router();
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

// Get product by barcode (database first, then AI analysis if not found)
router.get('/barcode/:barcode', optionalAuth, async (req, res) => {
  try {
    const { barcode } = req.params;
    
    // First, check if product exists in database
    let product = await Product.findOne({ barcode });
    
    if (product) {
      // Update search count
      product.searchCount += 1;
      product.popularity += 1;
      await product.save();
      
      return res.json({
        source: 'database',
        product: product,
        cached: true
      });
    }
    
    // If not found, try to fetch from OpenFoodFacts
    try {
      const axios = require('axios');
      const response = await axios.get(`https://world.openfoodfacts.org/api/v0/product/${barcode}.json`);
      
      if (response.data.status === 1) {
        const openFoodProduct = response.data.product;
        
        // Create new product from OpenFoodFacts data
        const productData = {
          barcode: barcode,
          name: openFoodProduct.product_name || 'Unknown Product',
          brand: openFoodProduct.brands || 'Unknown Brand',
          category: openFoodProduct.categories_tags?.[0]?.replace('en:', '') || 'Food',
          halalStatus: {
            status: 'unknown',
            confidence: 0,
            reason: 'Requires AI analysis',
            verifiedBy: 'ai'
          },
          ingredients: {
            list: openFoodProduct.ingredients_text ? 
              openFoodProduct.ingredients_text.split(',').map(i => i.trim()) : [],
            allergens: openFoodProduct.allergens_tags ? 
              openFoodProduct.allergens_tags.map(a => a.replace('en:', '')) : []
          },
          nutrition: {
            servingSize: openFoodProduct.serving_size,
            calories: openFoodProduct.nutriments?.['energy-kcal_100g'],
            totalFat: openFoodProduct.nutriments?.fat_100g,
            saturatedFat: openFoodProduct.nutriments?.['saturated-fat_100g'],
            sodium: openFoodProduct.nutriments?.sodium_100g,
            totalCarbohydrates: openFoodProduct.nutriments?.carbohydrates_100g,
            sugars: openFoodProduct.nutriments?.sugars_100g,
            protein: openFoodProduct.nutriments?.proteins_100g
          },
          images: {
            front: openFoodProduct.image_front_url,
            ingredients: openFoodProduct.image_ingredients_url,
            nutrition: openFoodProduct.image_nutrition_url
          },
          externalData: {
            openFoodFactsId: barcode
          },
          metadata: {
            dataSource: 'openfoodfacts',
            dataQuality: 'medium'
          },
          searchCount: 1,
          popularity: 1
        };

        // Perform AI analysis for halal status
        if (productData.ingredients.list.length > 0) {
          const analysisResult = await analyzeProductWithAI(productData);
          if (analysisResult) {
            productData.halalStatus = analysisResult.halalStatus;
            productData.healthInfo = analysisResult.healthInfo;
          }
        }

        // Save to database
        product = new Product(productData);
        product.calculateHealthScore();
        await product.save();

        return res.json({
          source: 'openfoodfacts_with_ai',
          product: product,
          cached: false
        });
      }
    } catch (openFoodError) {
      console.error('OpenFoodFacts API error:', openFoodError.message);
    }
    
    // If not found anywhere, return not found
    return res.status(404).json({
      error: 'Product not found',
      barcode: barcode,
      suggestion: 'Try manual input or contact support to add this product'
    });

  } catch (error) {
    console.error('Product lookup error:', error);
    res.status(500).json({
      error: 'Failed to lookup product',
      code: 'PRODUCT_LOOKUP_ERROR'
    });
  }
});

// Search products
router.get('/search', [
  query('q').notEmpty().withMessage('Search query is required'),
  query('limit').optional().isInt({ min: 1, max: 50 }).toInt(),
  query('halalStatus').optional().isIn(['halal', 'haram', 'questionable', 'unknown']),
  query('category').optional().isString(),
  query('brand').optional().isString()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { q, limit = 20, halalStatus, category, brand, allergens } = req.query;
    
    const filters = {
      limit: parseInt(limit),
      ...(halalStatus && { halalStatus }),
      ...(category && { category }),
      ...(brand && { brand }),
      ...(allergens && { allergens: allergens.split(',') })
    };

    const products = await Product.searchProducts(q, filters);

    res.json({
      query: q,
      filters,
      results: products.length,
      products
    });

  } catch (error) {
    console.error('Product search error:', error);
    res.status(500).json({
      error: 'Search failed',
      code: 'SEARCH_ERROR'
    });
  }
});

// Get product details by ID
router.get('/:id', async (req, res) => {
  try {
    const product = await Product.findById(req.params.id);
    
    if (!product) {
      return res.status(404).json({
        error: 'Product not found'
      });
    }

    // Update view count
    product.searchCount += 1;
    await product.save();

    res.json({ product });

  } catch (error) {
    console.error('Product details error:', error);
    res.status(500).json({
      error: 'Failed to get product details',
      code: 'PRODUCT_DETAILS_ERROR'
    });
  }
});

// Add product rating/review
router.post('/:id/rating', [
  authenticate,
  body('rating').isInt({ min: 1, max: 5 }),
  body('review').optional().isString(),
  body('halalConfirmation').optional().isIn(['halal', 'haram', 'questionable'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const product = await Product.findById(req.params.id);
    if (!product) {
      return res.status(404).json({
        error: 'Product not found'
      });
    }

    const { rating, review, halalConfirmation } = req.body;
    
    // Check if user already rated this product
    const existingRating = product.community.ratings.find(
      r => r.userId.toString() === req.user._id.toString()
    );

    if (existingRating) {
      return res.status(400).json({
        error: 'You have already rated this product'
      });
    }

    await product.addRating(req.user._id, rating, review, halalConfirmation);

    res.json({
      message: 'Rating added successfully',
      averageRating: product.community.averageRating,
      totalReviews: product.community.totalReviews
    });

  } catch (error) {
    console.error('Add rating error:', error);
    res.status(500).json({
      error: 'Failed to add rating',
      code: 'RATING_ERROR'
    });
  }
});

// Get popular products
router.get('/popular', async (req, res) => {
  try {
    const { limit = 20, category, halalStatus } = req.query;
    
    const filter = {};
    if (category) filter.category = category;
    if (halalStatus) filter['halalStatus.status'] = halalStatus;

    const products = await Product.find(filter)
      .sort({ popularity: -1, 'community.averageRating': -1 })
      .limit(parseInt(limit));

    res.json({
      products,
      count: products.length
    });

  } catch (error) {
    console.error('Popular products error:', error);
    res.status(500).json({
      error: 'Failed to get popular products',
      code: 'POPULAR_PRODUCTS_ERROR'
    });
  }
});

// Get categories
router.get('/categories', async (req, res) => {
  try {
    const categories = await Product.distinct('category');
    const categoriesWithCount = await Product.aggregate([
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 },
          halalCount: {
            $sum: { $cond: [{ $eq: ['$halalStatus.status', 'halal'] }, 1, 0] }
          }
        }
      },
      {
        $project: {
          category: '$_id',
          count: 1,
          halalCount: 1,
          halalPercentage: { $multiply: [{ $divide: ['$halalCount', '$count'] }, 100] }
        }
      },
      { $sort: { count: -1 } }
    ]);

    res.json({
      categories: categoriesWithCount
    });

  } catch (error) {
    console.error('Categories error:', error);
    res.status(500).json({
      error: 'Failed to get categories',
      code: 'CATEGORIES_ERROR'
    });
  }
});

// AI Analysis helper function
async function analyzeProductWithAI(productData) {
  try {
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
    
    const prompt = `Analyze this food product for halal compliance and health score:

Product: ${productData.name}
Brand: ${productData.brand}
Ingredients: ${productData.ingredients.list.join(', ')}

Please provide a JSON response with:
{
  "halalStatus": {
    "status": "halal|haram|questionable",
    "confidence": 0.0-1.0,
    "reason": "detailed explanation",
    "questionableIngredients": ["ingredient1", "ingredient2"],
    "haramIngredients": ["ingredient1", "ingredient2"]
  },
  "healthInfo": {
    "healthScore": 0-10,
    "positives": ["positive1", "positive2"],
    "negatives": ["negative1", "negative2"],
    "recommendations": ["recommendation1", "recommendation2"]
  }
}`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    // Try to parse JSON response
    const jsonMatch = text.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }
    
    return null;
  } catch (error) {
    console.error('AI analysis error:', error);
    return null;
  }
}

module.exports = router;
