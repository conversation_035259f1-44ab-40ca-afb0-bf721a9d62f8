const express = require('express');
const { body, validationResult, query } = require('express-validator');
const User = require('../models/User');
const Product = require('../models/Product');
const Brand = require('../models/Brand');
const Usage = require('../models/Usage');
const { authenticate, requireAdmin } = require('../middleware/auth');
const DataSeeder = require('../services/dataSeeder');

const router = express.Router();

// Admin authentication middleware
const adminAuth = [authenticate, requireAdmin];

// Get admin dashboard stats
router.get('/stats', adminAuth, async (req, res) => {
  try {
    const [
      totalUsers,
      totalProducts,
      totalBrands,
      activeSubscriptions,
      todayUsage
    ] = await Promise.all([
      User.countDocuments(),
      Product.countDocuments(),
      Brand.countDocuments(),
      User.countDocuments({ 'subscription.status': 'active', 'subscription.tier': { $ne: 'free' } }),
      Usage.aggregate([
        {
          $match: {
            date: {
              $gte: new Date(new Date().setHours(0, 0, 0, 0)),
              $lt: new Date(new Date().setHours(23, 59, 59, 999))
            }
          }
        },
        {
          $group: {
            _id: null,
            totalCalls: { $sum: '$totalCalls' },
            totalCost: { $sum: '$totalCost' }
          }
        }
      ])
    ]);

    const todayStats = todayUsage[0] || { totalCalls: 0, totalCost: 0 };

    res.json({
      totalUsers,
      totalProducts,
      totalBrands,
      activeSubscriptions,
      todayAnalyses: todayStats.totalCalls,
      todayCost: todayStats.totalCost,
      costSavings: 90 // Percentage
    });

  } catch (error) {
    console.error('Admin stats error:', error);
    res.status(500).json({
      error: 'Failed to get admin stats',
      code: 'ADMIN_STATS_ERROR'
    });
  }
});

// Get all users with pagination
router.get('/users', [
  adminAuth,
  query('page').optional().isInt({ min: 1 }).toInt(),
  query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
  query('search').optional().isString()
], async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const search = req.query.search;
    const skip = (page - 1) * limit;

    let query = {};
    if (search) {
      query = {
        $or: [
          { name: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } }
        ]
      };
    }

    const [users, total] = await Promise.all([
      User.find(query)
        .select('-password')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      User.countDocuments(query)
    ]);

    res.json({
      users,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Admin users error:', error);
    res.status(500).json({
      error: 'Failed to get users',
      code: 'ADMIN_USERS_ERROR'
    });
  }
});

// Get user details
router.get('/users/:id', adminAuth, async (req, res) => {
  try {
    const user = await User.findById(req.params.id).select('-password');
    
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Get user's usage statistics
    const usage = await Usage.find({ userId: user._id })
      .sort({ date: -1 })
      .limit(30);

    res.json({
      user,
      usage
    });

  } catch (error) {
    console.error('Admin user details error:', error);
    res.status(500).json({
      error: 'Failed to get user details',
      code: 'ADMIN_USER_DETAILS_ERROR'
    });
  }
});

// Update user subscription
router.put('/users/:id/subscription', [
  adminAuth,
  body('tier').isIn(['free', 'premium', 'enterprise']),
  body('status').optional().isIn(['active', 'cancelled', 'expired', 'past_due'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { tier, status } = req.body;
    
    const user = await User.findById(req.params.id);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    user.subscription.tier = tier;
    if (status) user.subscription.status = status;
    
    // Update limits based on tier
    user.usage.dailyLimit = user.getDailyLimit();
    user.usage.monthlyLimit = user.getMonthlyLimit();

    await user.save();

    res.json({
      message: 'User subscription updated successfully',
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        subscription: user.subscription
      }
    });

  } catch (error) {
    console.error('Admin update subscription error:', error);
    res.status(500).json({
      error: 'Failed to update subscription',
      code: 'ADMIN_UPDATE_SUBSCRIPTION_ERROR'
    });
  }
});

// Delete user
router.delete('/users/:id', adminAuth, async (req, res) => {
  try {
    const user = await User.findById(req.params.id);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Delete user's usage data
    await Usage.deleteMany({ userId: user._id });
    
    // Delete user
    await User.findByIdAndDelete(req.params.id);

    res.json({
      message: 'User deleted successfully'
    });

  } catch (error) {
    console.error('Admin delete user error:', error);
    res.status(500).json({
      error: 'Failed to delete user',
      code: 'ADMIN_DELETE_USER_ERROR'
    });
  }
});

// Get all products with admin details
router.get('/products', [
  adminAuth,
  query('page').optional().isInt({ min: 1 }).toInt(),
  query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
  query('search').optional().isString(),
  query('halalStatus').optional().isIn(['halal', 'haram', 'questionable', 'unknown']),
  query('verified').optional().isBoolean()
], async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const search = req.query.search;
    const halalStatus = req.query.halalStatus;
    const verified = req.query.verified;
    const skip = (page - 1) * limit;

    let query = {};
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { brand: { $regex: search, $options: 'i' } },
        { barcode: search }
      ];
    }
    
    if (halalStatus) {
      query['halalStatus.status'] = halalStatus;
    }
    
    if (verified !== undefined) {
      query['metadata.verified'] = verified;
    }

    const [products, total] = await Promise.all([
      Product.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      Product.countDocuments(query)
    ]);

    res.json({
      products,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Admin products error:', error);
    res.status(500).json({
      error: 'Failed to get products',
      code: 'ADMIN_PRODUCTS_ERROR'
    });
  }
});

// Update product verification status
router.put('/products/:id/verify', adminAuth, async (req, res) => {
  try {
    const product = await Product.findById(req.params.id);
    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }

    product.metadata.verified = true;
    product.metadata.verifiedBy = req.user._id;
    product.metadata.verifiedDate = new Date();

    await product.save();

    res.json({
      message: 'Product verified successfully',
      product
    });

  } catch (error) {
    console.error('Admin verify product error:', error);
    res.status(500).json({
      error: 'Failed to verify product',
      code: 'ADMIN_VERIFY_PRODUCT_ERROR'
    });
  }
});

// Get system analytics
router.get('/analytics', adminAuth, async (req, res) => {
  try {
    const { days = 30 } = req.query;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));

    const analytics = await Usage.aggregate([
      {
        $match: {
          date: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$date' },
            month: { $month: '$date' },
            day: { $dayOfMonth: '$date' }
          },
          totalCalls: { $sum: '$totalCalls' },
          totalCost: { $sum: '$totalCost' },
          uniqueUsers: { $addToSet: '$userId' }
        }
      },
      {
        $project: {
          date: {
            $dateFromParts: {
              year: '$_id.year',
              month: '$_id.month',
              day: '$_id.day'
            }
          },
          totalCalls: 1,
          totalCost: 1,
          uniqueUsers: { $size: '$uniqueUsers' }
        }
      },
      {
        $sort: { date: 1 }
      }
    ]);

    // Get subscription distribution
    const subscriptionStats = await User.aggregate([
      {
        $group: {
          _id: '$subscription.tier',
          count: { $sum: 1 }
        }
      }
    ]);

    res.json({
      dailyAnalytics: analytics,
      subscriptionDistribution: subscriptionStats,
      period: `${days} days`
    });

  } catch (error) {
    console.error('Admin analytics error:', error);
    res.status(500).json({
      error: 'Failed to get analytics',
      code: 'ADMIN_ANALYTICS_ERROR'
    });
  }
});

// Seed database endpoint
router.post('/seed-database', adminAuth, async (req, res) => {
  try {
    await DataSeeder.seedAll();
    
    res.json({
      message: 'Database seeded successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Database seeding error:', error);
    res.status(500).json({
      error: 'Database seeding failed',
      details: error.message,
      code: 'ADMIN_SEED_ERROR'
    });
  }
});

// System maintenance endpoints
router.post('/maintenance/optimize-db', adminAuth, async (req, res) => {
  try {
    // Run database optimization tasks
    await Promise.all([
      Product.collection.reIndex(),
      User.collection.reIndex(),
      Brand.collection.reIndex(),
      Usage.collection.reIndex()
    ]);

    res.json({
      message: 'Database optimization completed',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Database optimization error:', error);
    res.status(500).json({
      error: 'Database optimization failed',
      code: 'ADMIN_OPTIMIZE_ERROR'
    });
  }
});

// Export data endpoints
router.get('/export/users', adminAuth, async (req, res) => {
  try {
    const users = await User.find({}).select('-password');
    
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', 'attachment; filename=users-export.json');
    res.json(users);

  } catch (error) {
    console.error('Export users error:', error);
    res.status(500).json({
      error: 'Failed to export users',
      code: 'ADMIN_EXPORT_USERS_ERROR'
    });
  }
});

router.get('/export/products', adminAuth, async (req, res) => {
  try {
    const products = await Product.find({});
    
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', 'attachment; filename=products-export.json');
    res.json(products);

  } catch (error) {
    console.error('Export products error:', error);
    res.status(500).json({
      error: 'Failed to export products',
      code: 'ADMIN_EXPORT_PRODUCTS_ERROR'
    });
  }
});

module.exports = router;
