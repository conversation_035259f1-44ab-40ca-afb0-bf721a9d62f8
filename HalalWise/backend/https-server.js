const https = require('https');
const fs = require('fs');
const path = require('path');

// Import the main app
const app = require('./server-app'); // We'll need to extract the app from server.js

// HTTPS options
const options = {
  key: fs.readFileSync(path.join(__dirname, 'server.key')),
  cert: fs.readFileSync(path.join(__dirname, 'server.crt'))
};

// Create HTTPS server
const httpsServer = https.createServer(options, app);

const PORT = process.env.HTTPS_PORT || 3443;

httpsServer.listen(PORT, () => {
  console.log(`🔒 HTTPS Server running on https://localhost:${PORT}`);
  console.log(`🛠️ Admin Panel: https://localhost:${PORT}/admin.html`);
  console.log('⚠️  You may need to accept the self-signed certificate warning');
});
