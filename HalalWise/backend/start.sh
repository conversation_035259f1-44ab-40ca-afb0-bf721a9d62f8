#!/bin/bash

# HalalWise Backend Quick Start Script
echo "🚀 Starting HalalWise Backend Server..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    echo "   Visit: https://nodejs.org/"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ required. Current version: $(node -v)"
    echo "   Please update Node.js: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies"
        exit 1
    fi
    echo "✅ Dependencies installed successfully"
else
    echo "✅ Dependencies already installed"
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found, copying from .env.example"
    cp .env.example .env
    echo "✅ .env file created"
fi

# Verify Gemini API key
if grep -q "your_gemini_api_key_here" .env; then
    echo "⚠️  Please update your Gemini API key in .env file"
    echo "   Current key: AIzaSyD8uelHhB2IEZWduY92L2CTt7sXzsD7oV8"
else
    echo "✅ Gemini API key configured"
fi

# Start the server
echo "🚀 Starting server on http://localhost:3000"
echo "📊 API Documentation: http://localhost:3000/api/health"
echo "🛑 Press Ctrl+C to stop the server"
echo ""

# Start with nodemon if available, otherwise use node
if command -v nodemon &> /dev/null; then
    nodemon server.js
else
    node server.js
fi
