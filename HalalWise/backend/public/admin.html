<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HalalWise Admin Panel</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 5px;
        }

        .header p {
            opacity: 0.9;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .nav-tab {
            flex: 1;
            padding: 15px 20px;
            background: white;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .nav-tab.active {
            color: #2E8B57;
            border-bottom-color: #2E8B57;
            background: #f8fff8;
        }

        .nav-tab:hover {
            background: #f0f8f0;
        }

        .tab-content {
            display: none;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }

        .tab-content.active {
            display: block;
        }

        .section-title {
            font-size: 1.5rem;
            color: #2E8B57;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e0e0e0;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #f8fff8 0%, #e8f5e8 100%);
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #2E8B57;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #2E8B57;
        }

        .stat-label {
            color: #666;
            margin-top: 5px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #2E8B57;
            box-shadow: 0 0 0 3px rgba(46, 139, 87, 0.1);
        }

        .btn {
            background: #2E8B57;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            background: #228B22;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2E8B57;
        }

        .table tr:hover {
            background: #f8fff8;
        }

        .badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .badge-success {
            background: #d4edda;
            color: #155724;
        }

        .badge-warning {
            background: #fff3cd;
            color: #856404;
        }

        .badge-danger {
            background: #f8d7da;
            color: #721c24;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 10px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .close {
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: #aaa;
        }

        .close:hover {
            color: #000;
        }

        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #2E8B57;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .search-box {
            width: 100%;
            max-width: 400px;
            margin-bottom: 20px;
        }

        .actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛠️ HalalWise Admin Panel</h1>
        <p>Manage users, products, subscriptions, and system settings</p>
    </div>

    <div class="container">
        <!-- Login Section -->
        <div id="loginSection" class="tab-content active">
            <h2 class="section-title">Admin Login</h2>
            <div style="max-width: 400px; margin: 0 auto;">
                <form id="loginForm">
                    <div class="form-group">
                        <label>Email</label>
                        <input type="email" id="loginEmail" required placeholder="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label>Password</label>
                        <input type="password" id="loginPassword" required placeholder="Enter password">
                    </div>
                    <button type="submit" class="btn" style="width: 100%;">🔐 Login to Admin Panel</button>
                </form>
                <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; font-size: 0.9rem;">
                    <strong>Demo Credentials:</strong><br>
                    Email: <EMAIL><br>
                    Password: admin123<br>
                    <em>Note: You'll need to register this user first via the API</em>
                </div>
            </div>
        </div>

        <!-- Main Admin Panel -->
        <div id="adminPanel" style="display: none;">
            <div class="nav-tabs">
                <button class="nav-tab active" onclick="showTab('dashboard', this)">📊 Dashboard</button>
                <button class="nav-tab" onclick="showTab('users', this)">👥 Users</button>
                <button class="nav-tab" onclick="showTab('products', this)">🥗 Products</button>
                <button class="nav-tab" onclick="showTab('brands', this)">🏢 Brands</button>
                <button class="nav-tab" onclick="showTab('subscriptions', this)">💳 Subscriptions</button>
                <button class="nav-tab" onclick="showTab('analytics', this)">📈 Analytics</button>
                <button class="nav-tab" onclick="showTab('settings', this)">⚙️ Settings</button>
                <button class="nav-tab" onclick="logout()" style="margin-left: auto; background: #dc3545;">🚪 Logout</button>
            </div>

        <!-- Dashboard Tab -->
        <div id="dashboard" class="tab-content active">
            <h2 class="section-title">System Overview</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalUsers">-</div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalProducts">-</div>
                    <div class="stat-label">Products</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalBrands">-</div>
                    <div class="stat-label">Brands</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="activeSubscriptions">-</div>
                    <div class="stat-label">Active Subscriptions</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="todayAnalyses">-</div>
                    <div class="stat-label">Today's Analyses</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="costSavings">90%</div>
                    <div class="stat-label">Cost Savings</div>
                </div>
            </div>

            <div class="actions">
                <button class="btn" onclick="testButton()">🧪 Test Button</button>
                <button class="btn" onclick="seedDatabase()">🌱 Seed Database</button>
                <button class="btn" onclick="refreshStats()">🔄 Refresh Stats</button>
                <button class="btn btn-secondary" onclick="exportData()">📥 Export Data</button>
                <button class="btn btn-secondary" onclick="viewLogs()">📋 View Logs</button>
            </div>

            <div id="systemStatus"></div>
        </div>

        <!-- Users Tab -->
        <div id="users" class="tab-content">
            <h2 class="section-title">User Management</h2>

            <div class="actions">
                <input type="text" class="search-box" id="userSearch" placeholder="Search users..." onkeyup="searchUsers()">
                <button class="btn" onclick="showCreateUserModal()">➕ Add User</button>
                <button class="btn btn-secondary" onclick="exportUsers()">📥 Export Users</button>
            </div>

            <div id="usersTable">
                <div class="loading">
                    <div class="spinner"></div>
                    Loading users...
                </div>
            </div>
        </div>

        <!-- Products Tab -->
        <div id="products" class="tab-content">
            <h2 class="section-title">Product Management</h2>

            <div class="actions">
                <input type="text" class="search-box" id="productSearch" placeholder="Search products..." onkeyup="searchProducts()">
                <button class="btn" onclick="showCreateProductModal()">➕ Add Product</button>
                <button class="btn btn-secondary" onclick="showImportProductsModal()">📤 Import Products</button>
                <button class="btn btn-secondary" onclick="exportProducts()">📥 Export Products</button>
            </div>

            <div id="productsTable">
                <div class="loading">
                    <div class="spinner"></div>
                    Loading products...
                </div>
            </div>
        </div>

        <!-- Brands Tab -->
        <div id="brands" class="tab-content">
            <h2 class="section-title">Brand Management</h2>

            <div class="actions">
                <input type="text" class="search-box" id="brandSearch" placeholder="Search brands..." onkeyup="searchBrands()">
                <button class="btn" onclick="showCreateBrandModal()">➕ Add Brand</button>
                <button class="btn btn-secondary" onclick="updateBrandStats()">🔄 Update Stats</button>
            </div>

            <div id="brandsTable">
                <div class="loading">
                    <div class="spinner"></div>
                    Loading brands...
                </div>
            </div>
        </div>

        <!-- Subscriptions Tab -->
        <div id="subscriptions" class="tab-content">
            <h2 class="section-title">Subscription Management</h2>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="freeUsers">-</div>
                    <div class="stat-label">Free Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="premiumUsers">-</div>
                    <div class="stat-label">Premium Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="enterpriseUsers">-</div>
                    <div class="stat-label">Enterprise Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="monthlyRevenue">-</div>
                    <div class="stat-label">Monthly Revenue</div>
                </div>
            </div>

            <div class="actions">
                <input type="text" class="search-box" id="subscriptionSearch" placeholder="Search subscriptions..." onkeyup="searchSubscriptions()">
                <button class="btn btn-secondary" onclick="exportSubscriptions()">📥 Export Data</button>
                <button class="btn btn-secondary" onclick="generateSubscriptionReport()">📊 Generate Report</button>
            </div>

            <div id="subscriptionsTable">
                <div class="loading">
                    <div class="spinner"></div>
                    Loading subscriptions...
                </div>
            </div>
        </div>

        <!-- Analytics Tab -->
        <div id="analytics" class="tab-content">
            <h2 class="section-title">Analytics & Usage</h2>

            <div class="grid-2">
                <div>
                    <h3>API Usage Trends</h3>
                    <canvas id="usageChart" width="400" height="200"></canvas>
                </div>
                <div>
                    <h3>Cost Analysis</h3>
                    <canvas id="costChart" width="400" height="200"></canvas>
                </div>
            </div>

            <div class="actions">
                <button class="btn" onclick="generateReport()">📊 Generate Report</button>
                <button class="btn btn-secondary" onclick="exportAnalytics()">📥 Export Analytics</button>
            </div>

            <div id="analyticsData"></div>
        </div>

        <!-- Settings Tab -->
        <div id="settings" class="tab-content">
            <h2 class="section-title">System Settings</h2>

            <div class="grid-2">
                <div>
                    <h3>API Configuration</h3>
                    <div class="form-group">
                        <label>Gemini API Key</label>
                        <input type="password" id="geminiApiKey" placeholder="Enter API key">
                    </div>
                    <div class="form-group">
                        <label>Rate Limit (requests/day)</label>
                        <input type="number" id="rateLimit" value="1000">
                    </div>
                    <button class="btn" onclick="saveApiSettings()">💾 Save API Settings</button>
                </div>

                <div>
                    <h3>Subscription Settings</h3>
                    <div class="form-group">
                        <label>Free Tier Daily Limit</label>
                        <input type="number" id="freeTierLimit" value="10">
                    </div>
                    <div class="form-group">
                        <label>Premium Tier Daily Limit</label>
                        <input type="number" id="premiumTierLimit" value="100">
                    </div>
                    <div class="form-group">
                        <label>Enterprise Tier Daily Limit</label>
                        <input type="number" id="enterpriseTierLimit" value="1000">
                    </div>
                    <button class="btn" onclick="saveSubscriptionSettings()">💾 Save Subscription Settings</button>
                </div>
            </div>

            <div style="margin-top: 30px;">
                <h3>System Maintenance</h3>
                <div class="actions">
                    <button class="btn btn-secondary" onclick="clearCache()">🗑️ Clear Cache</button>
                    <button class="btn btn-secondary" onclick="optimizeDatabase()">⚡ Optimize Database</button>
                    <button class="btn btn-danger" onclick="resetSystem()">🔄 Reset System</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <div id="createUserModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('createUserModal')">&times;</span>
            <h2>Create New User</h2>
            <form id="createUserForm">
                <div class="form-group">
                    <label>Name</label>
                    <input type="text" id="userName" required>
                </div>
                <div class="form-group">
                    <label>Email</label>
                    <input type="email" id="userEmail" required>
                </div>
                <div class="form-group">
                    <label>Password</label>
                    <input type="password" id="userPassword" required>
                </div>
                <div class="form-group">
                    <label>Subscription Tier</label>
                    <select id="userTier">
                        <option value="free">Free</option>
                        <option value="premium">Premium</option>
                        <option value="enterprise">Enterprise</option>
                    </select>
                </div>
                <button type="submit" class="btn">Create User</button>
                <button type="button" class="btn btn-secondary" onclick="closeModal('createUserModal')">Cancel</button>
            </form>
        </div>
    </div>

    <div id="createProductModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('createProductModal')">&times;</span>
            <h2>Add New Product</h2>
            <form id="createProductForm">
                <div class="form-group">
                    <label>Barcode</label>
                    <input type="text" id="productBarcode" required>
                </div>
                <div class="form-group">
                    <label>Product Name</label>
                    <input type="text" id="productName" required>
                </div>
                <div class="form-group">
                    <label>Brand</label>
                    <input type="text" id="productBrand" required>
                </div>
                <div class="form-group">
                    <label>Category</label>
                    <input type="text" id="productCategory" required>
                </div>
                <div class="form-group">
                    <label>Halal Status</label>
                    <select id="productHalalStatus">
                        <option value="halal">Halal</option>
                        <option value="haram">Haram</option>
                        <option value="questionable">Questionable</option>
                        <option value="unknown">Unknown</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Ingredients (comma-separated)</label>
                    <textarea id="productIngredients" rows="3"></textarea>
                </div>
                <button type="submit" class="btn">Add Product</button>
                <button type="button" class="btn btn-secondary" onclick="closeModal('createProductModal')">Cancel</button>
            </form>
        </div>
    </div>

    <div id="importProductsModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('importProductsModal')">&times;</span>
            <h2>Import Products</h2>
            <div style="margin-bottom: 20px;">
                <h3>Upload CSV File</h3>
                <p>Upload a CSV file with the following columns: barcode, name, brand, category, halalStatus, ingredients</p>
                <div class="form-group">
                    <label>Select CSV File</label>
                    <input type="file" id="productCsvFile" accept=".csv" required>
                </div>
                <button class="btn" onclick="uploadProductsCsv()">📤 Upload CSV</button>
            </div>

            <div style="margin-bottom: 20px;">
                <h3>Or Paste JSON Data</h3>
                <div class="form-group">
                    <label>JSON Data</label>
                    <textarea id="productJsonData" rows="10" placeholder='[{"barcode":"123","name":"Product","brand":"Brand","category":"Food","halalStatus":"halal","ingredients":"ingredient1,ingredient2"}]'></textarea>
                </div>
                <button class="btn" onclick="importProductsJson()">📥 Import JSON</button>
            </div>

            <div style="margin-top: 20px;">
                <h3>Sample CSV Format</h3>
                <pre style="background: #f5f5f5; padding: 10px; border-radius: 5px; font-size: 0.9rem;">barcode,name,brand,category,halalStatus,ingredients
123456789,Sample Product,Sample Brand,Food,halal,"ingredient1,ingredient2"
987654321,Another Product,Another Brand,Beverage,haram,"ingredient3,ingredient4"</pre>
            </div>

            <button type="button" class="btn btn-secondary" onclick="closeModal('importProductsModal')">Cancel</button>
        </div>
    </div>

    <div id="createBrandModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('createBrandModal')">&times;</span>
            <h2>Add New Brand</h2>
            <form id="createBrandForm">
                <div class="form-group">
                    <label>Brand Name</label>
                    <input type="text" id="brandName" required>
                </div>
                <div class="form-group">
                    <label>Country</label>
                    <input type="text" id="brandCountry" placeholder="e.g., USA, Malaysia, UAE">
                </div>
                <div class="form-group">
                    <label>Description</label>
                    <textarea id="brandDescription" rows="3" placeholder="Brief description of the brand"></textarea>
                </div>
                <div class="form-group">
                    <label>Halal Certified</label>
                    <select id="brandHalalCertified">
                        <option value="false">No</option>
                        <option value="true">Yes</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Certification Body</label>
                    <input type="text" id="brandCertificationBody" placeholder="e.g., JAKIM, MUI, IFANCA">
                </div>
                <div class="form-group">
                    <label>Trust Score (0-100)</label>
                    <input type="number" id="brandTrustScore" min="0" max="100" value="50">
                </div>
                <button type="submit" class="btn">Add Brand</button>
                <button type="button" class="btn btn-secondary" onclick="closeModal('createBrandModal')">Cancel</button>
            </form>
        </div>
    </div>

    <div id="manageSubscriptionModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('manageSubscriptionModal')">&times;</span>
            <h2>Manage Subscription</h2>
            <div id="subscriptionDetails">
                <div class="loading">
                    <div class="spinner"></div>
                    Loading subscription details...
                </div>
            </div>
            <div id="subscriptionActions" style="display: none;">
                <h3>Subscription Actions</h3>
                <div class="form-group">
                    <label>Change Tier</label>
                    <select id="newSubscriptionTier">
                        <option value="free">Free</option>
                        <option value="premium">Premium</option>
                        <option value="enterprise">Enterprise</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Status</label>
                    <select id="newSubscriptionStatus">
                        <option value="active">Active</option>
                        <option value="cancelled">Cancelled</option>
                        <option value="expired">Expired</option>
                        <option value="past_due">Past Due</option>
                    </select>
                </div>
                <div class="actions">
                    <button class="btn" onclick="updateSubscription()">💾 Update Subscription</button>
                    <button class="btn btn-secondary" onclick="sendSubscriptionEmail()">📧 Send Email</button>
                    <button class="btn btn-danger" onclick="cancelSubscription()">❌ Cancel Subscription</button>
                </div>
            </div>
            <button type="button" class="btn btn-secondary" onclick="closeModal('manageSubscriptionModal')">Close</button>
        </div>
    </div>

        </div> <!-- End adminPanel -->
    </div>

    <script src="admin.js"></script>
</body>
</html>
