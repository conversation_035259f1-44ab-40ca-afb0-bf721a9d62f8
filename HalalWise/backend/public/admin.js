// Admin Panel JavaScript
let authToken = localStorage.getItem('adminToken');
let currentData = {
    users: [],
    products: [],
    brands: [],
    subscriptions: []
};

// Initialize admin panel
document.addEventListener('DOMContentLoaded', function() {
    // Check if already logged in
    if (authToken) {
        showAdminPanel();
    } else {
        showLoginSection();
    }

    // Add login form handler
    document.getElementById('loginForm').addEventListener('submit', handleLogin);
});

// Authentication functions
function showLoginSection() {
    document.getElementById('loginSection').style.display = 'block';
    document.getElementById('adminPanel').style.display = 'none';
}

function showAdminPanel() {
    document.getElementById('loginSection').style.display = 'none';
    document.getElementById('adminPanel').style.display = 'block';

    // Initialize the dashboard tab as active
    showTab('dashboard');
    loadDashboard();
}

async function handleLogin(e) {
    e.preventDefault();

    const email = document.getElementById('loginEmail').value;
    const password = document.getElementById('loginPassword').value;

    try {
        const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email, password })
        });

        const data = await response.json();

        if (response.ok) {
            authToken = data.token;
            localStorage.setItem('adminToken', authToken);
            showAlert('Login successful!', 'success');
            showAdminPanel();
        } else {
            showAlert('Login failed: ' + data.error, 'error');
        }
    } catch (error) {
        showAlert('Login error: ' + error.message, 'error');
    }
}

function logout() {
    authToken = null;
    localStorage.removeItem('adminToken');
    showAlert('Logged out successfully', 'success');
    showLoginSection();
}

// Tab management
function showTab(tabName, clickedElement = null) {
    console.log('showTab called with:', tabName, clickedElement);

    // Hide all tabs
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });

    // Remove active class from all nav tabs
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.remove('active');
    });

    // Show selected tab
    const targetTab = document.getElementById(tabName);
    if (targetTab) {
        targetTab.classList.add('active');
        console.log('Activated tab:', tabName);
    } else {
        console.error('Tab not found:', tabName);
    }

    // Add active class to clicked nav tab
    if (clickedElement) {
        clickedElement.classList.add('active');
        console.log('Activated nav button');
    } else {
        // Find the nav tab by text content
        document.querySelectorAll('.nav-tab').forEach(tab => {
            if (tab.textContent.includes(getTabDisplayName(tabName))) {
                tab.classList.add('active');
            }
        });
    }

    // Load tab-specific data
    switch(tabName) {
        case 'dashboard':
            loadDashboard();
            break;
        case 'users':
            loadUsers();
            break;
        case 'products':
            loadProducts();
            break;
        case 'brands':
            loadBrands();
            break;
        case 'subscriptions':
            loadSubscriptions();
            break;
        case 'analytics':
            loadAnalytics();
            break;
        case 'settings':
            loadSettings();
            break;
    }
}

// Helper function to get tab display names
function getTabDisplayName(tabName) {
    const names = {
        'dashboard': 'Dashboard',
        'users': 'Users',
        'products': 'Products',
        'brands': 'Brands',
        'subscriptions': 'Subscriptions',
        'analytics': 'Analytics',
        'settings': 'Settings'
    };
    return names[tabName] || tabName;
}

// API helper functions
async function apiCall(endpoint, method = 'GET', data = null) {
    const options = {
        method,
        headers: {
            'Content-Type': 'application/json',
        }
    };

    if (authToken) {
        options.headers['Authorization'] = `Bearer ${authToken}`;
    }

    if (data) {
        options.body = JSON.stringify(data);
    }

    try {
        const response = await fetch(endpoint, options);
        const result = await response.json();

        if (!response.ok) {
            throw new Error(result.error || 'API call failed');
        }

        return result;
    } catch (error) {
        console.error('API Error:', error);
        showAlert('Error: ' + error.message, 'error');
        throw error;
    }
}

// Dashboard functions
async function loadDashboard() {
    try {
        // Load system stats from admin API
        const [health, stats] = await Promise.all([
            apiCall('/api/health'),
            apiCall('/api/admin/stats')
        ]);

        // Update dashboard stats
        document.getElementById('totalUsers').textContent = stats.totalUsers || '0';
        document.getElementById('totalProducts').textContent = stats.totalProducts || '6';
        document.getElementById('totalBrands').textContent = stats.totalBrands || '6';
        document.getElementById('activeSubscriptions').textContent = stats.activeSubscriptions || '0';
        document.getElementById('todayAnalyses').textContent = stats.todayAnalyses || '0';

        // Show system status
        const statusDiv = document.getElementById('systemStatus');
        statusDiv.innerHTML = `
            <div class="alert alert-success">
                <strong>System Status:</strong> ${health.status} |
                <strong>Database:</strong> ${health.database} |
                <strong>Uptime:</strong> ${Math.floor(health.uptime / 60)} minutes |
                <strong>Today's Cost:</strong> $${(stats.todayCost || 0).toFixed(4)}
            </div>
        `;

    } catch (error) {
        console.error('Failed to load dashboard:', error);
        // Fallback to basic info if admin stats fail
        try {
            const info = await apiCall('/api/info');
            document.getElementById('totalProducts').textContent = info.database?.seededProducts || '6';
            document.getElementById('totalBrands').textContent = info.database?.seededBrands || '6';
        } catch (fallbackError) {
            console.error('Fallback also failed:', fallbackError);
        }
    }
}

// User management functions
async function loadUsers() {
    const tableDiv = document.getElementById('usersTable');
    tableDiv.innerHTML = '<div class="loading"><div class="spinner"></div>Loading users...</div>';

    try {
        // For now, show sample data since we don't have a user list endpoint yet
        const sampleUsers = [
            { id: 1, name: 'John Doe', email: '<EMAIL>', tier: 'premium', status: 'active', createdAt: '2024-01-15' },
            { id: 2, name: 'Jane Smith', email: '<EMAIL>', tier: 'free', status: 'active', createdAt: '2024-01-20' },
            { id: 3, name: 'Ahmed Ali', email: '<EMAIL>', tier: 'enterprise', status: 'active', createdAt: '2024-01-25' }
        ];

        currentData.users = sampleUsers;
        renderUsersTable(sampleUsers);

    } catch (error) {
        tableDiv.innerHTML = '<div class="alert alert-error">Failed to load users</div>';
    }
}

function renderUsersTable(users) {
    const tableDiv = document.getElementById('usersTable');

    if (users.length === 0) {
        tableDiv.innerHTML = '<div class="alert alert-warning">No users found</div>';
        return;
    }

    const tableHTML = `
        <table class="table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Tier</th>
                    <th>Status</th>
                    <th>Created</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                ${users.map(user => `
                    <tr>
                        <td>${user.id}</td>
                        <td>${user.name}</td>
                        <td>${user.email}</td>
                        <td><span class="badge badge-${getBadgeClass(user.tier)}">${user.tier}</span></td>
                        <td><span class="badge badge-${user.status === 'active' ? 'success' : 'danger'}">${user.status}</span></td>
                        <td>${user.createdAt}</td>
                        <td>
                            <button class="btn btn-secondary" onclick="editUser(${user.id})">Edit</button>
                            <button class="btn btn-danger" onclick="deleteUser(${user.id})">Delete</button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;

    tableDiv.innerHTML = tableHTML;
}

// Product management functions
async function loadProducts() {
    const tableDiv = document.getElementById('productsTable');
    tableDiv.innerHTML = '<div class="loading"><div class="spinner"></div>Loading products...</div>';

    try {
        const response = await apiCall('/api/products/search?q=&limit=50');
        currentData.products = response.products || [];
        renderProductsTable(currentData.products);

    } catch (error) {
        // If search fails, try to get some sample data
        try {
            const kitkat = await apiCall('/api/products/barcode/7613031349418');
            currentData.products = [kitkat.product];
            renderProductsTable(currentData.products);
        } catch (fallbackError) {
            tableDiv.innerHTML = '<div class="alert alert-error">Failed to load products</div>';
        }
    }
}

function renderProductsTable(products) {
    const tableDiv = document.getElementById('productsTable');

    if (products.length === 0) {
        tableDiv.innerHTML = '<div class="alert alert-warning">No products found</div>';
        return;
    }

    const tableHTML = `
        <table class="table">
            <thead>
                <tr>
                    <th>Barcode</th>
                    <th>Name</th>
                    <th>Brand</th>
                    <th>Category</th>
                    <th>Halal Status</th>
                    <th>Health Score</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                ${products.map(product => `
                    <tr>
                        <td>${product.barcode}</td>
                        <td>${product.name}</td>
                        <td>${product.brand}</td>
                        <td>${product.category}</td>
                        <td><span class="badge badge-${getHalalBadgeClass(product.halalStatus?.status)}">${product.halalStatus?.status || 'unknown'}</span></td>
                        <td>${product.healthInfo?.healthScore || 'N/A'}</td>
                        <td>
                            <button class="btn btn-secondary" onclick="editProduct('${product._id}')">Edit</button>
                            <button class="btn btn-danger" onclick="deleteProduct('${product._id}')">Delete</button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;

    tableDiv.innerHTML = tableHTML;
}

// Brand management functions
async function loadBrands() {
    const tableDiv = document.getElementById('brandsTable');
    tableDiv.innerHTML = '<div class="loading"><div class="spinner"></div>Loading brands...</div>';

    try {
        const response = await apiCall('/api/brands?limit=50');
        currentData.brands = response.brands || [];
        renderBrandsTable(currentData.brands);

    } catch (error) {
        tableDiv.innerHTML = '<div class="alert alert-error">Failed to load brands</div>';
    }
}

function renderBrandsTable(brands) {
    const tableDiv = document.getElementById('brandsTable');

    if (brands.length === 0) {
        tableDiv.innerHTML = '<div class="alert alert-warning">No brands found</div>';
        return;
    }

    const tableHTML = `
        <table class="table">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Country</th>
                    <th>Trust Score</th>
                    <th>Halal Certified</th>
                    <th>Total Products</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                ${brands.map(brand => `
                    <tr>
                        <td>${brand.name}</td>
                        <td>${brand.country || 'N/A'}</td>
                        <td>${brand.trustScore || 'N/A'}</td>
                        <td><span class="badge badge-${brand.halalInfo?.isHalalCertified ? 'success' : 'warning'}">${brand.halalInfo?.isHalalCertified ? 'Yes' : 'No'}</span></td>
                        <td>${brand.stats?.totalProducts || 0}</td>
                        <td>
                            <button class="btn btn-secondary" onclick="editBrand('${brand._id}')">Edit</button>
                            <button class="btn btn-danger" onclick="deleteBrand('${brand._id}')">Delete</button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;

    tableDiv.innerHTML = tableHTML;
}

// Subscription management functions
async function loadSubscriptions() {
    const tableDiv = document.getElementById('subscriptionsTable');
    tableDiv.innerHTML = '<div class="loading"><div class="spinner"></div>Loading subscriptions...</div>';

    // Sample subscription data
    const sampleSubscriptions = [
        { id: 1, user: '<EMAIL>', tier: 'premium', status: 'active', amount: '$9.99', nextBilling: '2024-02-15' },
        { id: 2, user: '<EMAIL>', tier: 'enterprise', status: 'active', amount: '$29.99', nextBilling: '2024-02-20' }
    ];

    currentData.subscriptions = sampleSubscriptions;
    renderSubscriptionsTable(sampleSubscriptions);

    // Update subscription stats
    document.getElementById('freeUsers').textContent = '1';
    document.getElementById('premiumUsers').textContent = '1';
    document.getElementById('enterpriseUsers').textContent = '1';
    document.getElementById('monthlyRevenue').textContent = '$39.98';
}

function renderSubscriptionsTable(subscriptions) {
    const tableDiv = document.getElementById('subscriptionsTable');

    const tableHTML = `
        <table class="table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>User</th>
                    <th>Tier</th>
                    <th>Status</th>
                    <th>Amount</th>
                    <th>Next Billing</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                ${subscriptions.map(sub => `
                    <tr>
                        <td>${sub.id}</td>
                        <td>${sub.user}</td>
                        <td><span class="badge badge-${getBadgeClass(sub.tier)}">${sub.tier}</span></td>
                        <td><span class="badge badge-${sub.status === 'active' ? 'success' : 'danger'}">${sub.status}</span></td>
                        <td>${sub.amount}</td>
                        <td>${sub.nextBilling}</td>
                        <td>
                            <button class="btn btn-secondary" onclick="manageSubscription(${sub.id})">Manage</button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;

    tableDiv.innerHTML = tableHTML;
}

// Analytics functions
async function loadAnalytics() {
    const analyticsDiv = document.getElementById('analyticsData');
    analyticsDiv.innerHTML = `
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">1,234</div>
                <div class="stat-label">Total API Calls</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">$12.34</div>
                <div class="stat-label">Total Cost</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">89%</div>
                <div class="stat-label">Cache Hit Rate</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">45ms</div>
                <div class="stat-label">Avg Response Time</div>
            </div>
        </div>
    `;
}

// Settings functions
function loadSettings() {
    // Load current settings
    document.getElementById('freeTierLimit').value = '10';
    document.getElementById('premiumTierLimit').value = '100';
    document.getElementById('enterpriseTierLimit').value = '1000';
    document.getElementById('rateLimit').value = '1000';
}

// Test function
function testButton() {
    console.log('Test button clicked!');
    alert('JavaScript is working! Buttons are clickable.');
    showAlert('Test successful - JavaScript is working!', 'success');
}

// Action functions
async function seedDatabase() {
    console.log('seedDatabase called');
    try {
        showAlert('Seeding database...', 'info');
        const result = await apiCall('/api/admin/seed-database', 'POST');
        console.log('Seed result:', result);
        showAlert('Database seeded successfully!', 'success');
        loadDashboard();
    } catch (error) {
        console.error('Seed error:', error);
        showAlert('Failed to seed database: ' + error.message, 'error');
    }
}

function refreshStats() {
    loadDashboard();
    showAlert('Statistics refreshed!', 'success');
}

// Modal functions
function showCreateUserModal() {
    document.getElementById('createUserModal').style.display = 'block';
}

function showCreateProductModal() {
    document.getElementById('createProductModal').style.display = 'block';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// Form handlers
document.getElementById('createUserForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const userData = {
        name: document.getElementById('userName').value,
        email: document.getElementById('userEmail').value,
        password: document.getElementById('userPassword').value,
        tier: document.getElementById('userTier').value
    };

    try {
        await apiCall('/api/auth/register', 'POST', userData);
        showAlert('User created successfully!', 'success');
        closeModal('createUserModal');
        loadUsers();
    } catch (error) {
        showAlert('Failed to create user: ' + error.message, 'error');
    }
});

// Utility functions
function getBadgeClass(tier) {
    switch(tier) {
        case 'free': return 'secondary';
        case 'premium': return 'warning';
        case 'enterprise': return 'success';
        default: return 'secondary';
    }
}

function getHalalBadgeClass(status) {
    switch(status) {
        case 'halal': return 'success';
        case 'haram': return 'danger';
        case 'questionable': return 'warning';
        default: return 'secondary';
    }
}

function showAlert(message, type) {
    console.log('showAlert called:', message, type);

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'error' : 'success'}`;
    alertDiv.textContent = message;
    alertDiv.style.marginBottom = '15px';

    // Insert at the top of the current tab
    const activeTab = document.querySelector('.tab-content.active');
    if (activeTab) {
        activeTab.insertBefore(alertDiv, activeTab.firstChild);
        console.log('Alert inserted into active tab');
    } else {
        // Fallback: insert into dashboard
        const dashboard = document.getElementById('dashboard');
        if (dashboard) {
            dashboard.insertBefore(alertDiv, dashboard.firstChild);
            console.log('Alert inserted into dashboard');
        }
    }

    // Remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
            console.log('Alert removed');
        }
    }, 5000);
}

// Search functions
function searchUsers() {
    const query = document.getElementById('userSearch').value.toLowerCase();
    const filtered = currentData.users.filter(user =>
        user.name.toLowerCase().includes(query) ||
        user.email.toLowerCase().includes(query)
    );
    renderUsersTable(filtered);
}

function searchProducts() {
    const query = document.getElementById('productSearch').value.toLowerCase();
    const filtered = currentData.products.filter(product =>
        product.name.toLowerCase().includes(query) ||
        product.brand.toLowerCase().includes(query) ||
        product.barcode.includes(query)
    );
    renderProductsTable(filtered);
}

function searchBrands() {
    const query = document.getElementById('brandSearch').value.toLowerCase();
    const filtered = currentData.brands.filter(brand =>
        brand.name.toLowerCase().includes(query) ||
        (brand.country && brand.country.toLowerCase().includes(query))
    );
    renderBrandsTable(filtered);
}

// Placeholder functions for actions
function editUser(id) { showAlert('Edit user functionality coming soon!', 'info'); }
function deleteUser(id) { showAlert('Delete user functionality coming soon!', 'info'); }
function editProduct(id) { showAlert('Edit product functionality coming soon!', 'info'); }
function deleteProduct(id) { showAlert('Delete product functionality coming soon!', 'info'); }
function editBrand(id) { showAlert('Edit brand functionality coming soon!', 'info'); }
function deleteBrand(id) { showAlert('Delete brand functionality coming soon!', 'info'); }
function manageSubscription(id) { showAlert('Manage subscription functionality coming soon!', 'info'); }
function exportData() { showAlert('Export functionality coming soon!', 'info'); }
function exportUsers() { showAlert('Export users functionality coming soon!', 'info'); }
function exportProducts() { showAlert('Export products functionality coming soon!', 'info'); }
function exportSubscriptions() { showAlert('Export subscriptions functionality coming soon!', 'info'); }
function exportAnalytics() { showAlert('Export analytics functionality coming soon!', 'info'); }
function importProducts() { showAlert('Import products functionality coming soon!', 'info'); }
function updateBrandStats() { showAlert('Update brand stats functionality coming soon!', 'info'); }
function generateReport() { showAlert('Generate report functionality coming soon!', 'info'); }
function viewLogs() { showAlert('View logs functionality coming soon!', 'info'); }
function saveApiSettings() { showAlert('API settings saved!', 'success'); }
function saveSubscriptionSettings() { showAlert('Subscription settings saved!', 'success'); }
function clearCache() { showAlert('Cache cleared!', 'success'); }
function optimizeDatabase() { showAlert('Database optimized!', 'success'); }
function resetSystem() {
    if (confirm('Are you sure you want to reset the system? This cannot be undone.')) {
        showAlert('System reset functionality coming soon!', 'info');
    }
}
