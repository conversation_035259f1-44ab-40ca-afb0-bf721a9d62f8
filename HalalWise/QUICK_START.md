# HalalWise Quick Start Guide 🚀

> **Get your HalalWise app running in 15 minutes!**

## 🎯 What You'll Achieve
- ✅ Running HalalWise app on your device
- ✅ Functional barcode scanning
- ✅ Beautiful UI with Islamic theming
- ✅ Database with halal/haram ingredients
- ⚠️ AI integration (requires API key setup)

---

## ⚡ 5-Minute Setup (Basic Functionality)

### Step 1: Prerequisites Check
```bash
# Verify Flutter installation
flutter doctor

# Should show:
# ✓ Flutter (Channel stable, 3.10+)
# ✓ Android toolchain
# ✓ VS Code / Android Studio
```

### Step 2: Clone and Install
```bash
# Clone the repository
git clone <your-repo-url>
cd HalalWise

# Install dependencies
flutter pub get

# This should complete without errors
```

### Step 3: Run the App
```bash
# For Android (connect device or start emulator)
flutter run

# For iOS (macOS only)
flutter run -d ios

# For web testing
flutter run -d chrome
```

### Step 4: Test Basic Features
1. **Home Screen**: Should show "Assalamu Alaikum!" welcome
2. **Barcode Scanner**: Tap "Scan Barcode" - camera should open
3. **Manual Input**: Try entering a barcode manually
4. **Navigation**: All buttons should navigate to appropriate screens

---

## 🔧 15-Minute Setup (Full Functionality)

### Step 5: Configure AI Integration

#### 5.1 Get xAI Grok API Key
```bash
1. Visit https://x.ai
2. Sign up for an account
3. Navigate to API section
4. Generate new API key
5. Copy the key (starts with 'xai-...')
```

#### 5.2 Update AI Service
```dart
// Edit: lib/core/network/ai_service.dart
// Line 89: Replace placeholder with your key

const apiKey = 'xai-your-actual-api-key-here';
```

#### 5.3 Test AI Integration
```bash
# Run the app
flutter run

# Try scanning a real product barcode
# Should now show AI-powered halal analysis
```

### Step 6: Optional Firebase Setup

#### 6.1 Create Firebase Project
```bash
1. Visit https://console.firebase.google.com
2. Create new project: "HalalWise"
3. Enable Firestore Database
4. Enable Authentication
```

#### 6.2 Download Config Files
```bash
# For Android
1. Add Android app in Firebase console
2. Download google-services.json
3. Place in: android/app/google-services.json

# For iOS
1. Add iOS app in Firebase console
2. Download GoogleService-Info.plist
3. Place in: ios/Runner/GoogleService-Info.plist
```

---

## 🧪 Testing Your Setup

### Test Checklist
```bash
✅ App launches without crashes
✅ Home screen displays correctly
✅ Barcode scanner opens camera
✅ Can scan real product barcodes
✅ Results page shows halal status
✅ Navigation works between screens
✅ Database queries work offline
✅ AI analysis works (if API key configured)
```

### Sample Test Products
Try scanning these barcodes to test functionality:
- **Coca-Cola**: `5449000000996` (Should be halal)
- **Oreo Cookies**: `7622210951557` (May contain questionable ingredients)
- **Nutella**: `3017620422003` (Contains questionable ingredients)

---

## 🚨 Troubleshooting

### Common Issues

#### "Flutter not found"
```bash
# Add Flutter to PATH
export PATH="$PATH:/path/to/flutter/bin"

# Or reinstall Flutter
# https://docs.flutter.dev/get-started/install
```

#### "Camera permission denied"
```bash
# Android: Check AndroidManifest.xml has camera permission
# iOS: Check Info.plist has camera usage description
# Both files are already configured in the project
```

#### "API key not working"
```bash
# Verify API key format
# xAI keys start with 'xai-'
# Check for extra spaces or characters
# Ensure account has API access enabled
```

#### "Build errors"
```bash
# Clean and rebuild
flutter clean
flutter pub get
flutter run

# Check Flutter doctor
flutter doctor
```

#### "Database errors"
```bash
# Clear app data and restart
# Database will reinitialize automatically
# Check device storage space
```

---

## 🎯 Next Steps After Setup

### Immediate Actions (Today)
1. **Test with real products** - Scan 10+ different barcodes
2. **Check UI on different devices** - Test phone/tablet layouts
3. **Verify offline functionality** - Turn off internet and test
4. **Review halal accuracy** - Compare results with known halal products

### This Week
1. **Implement missing features** - See DEVELOPMENT_TRACKER.md
2. **Add more test products** - Expand your testing database
3. **Customize for your region** - Add local halal certifications
4. **Get user feedback** - Share with friends/family for testing

### This Month
1. **Complete OCR implementation** - For label scanning
2. **Add voice input** - For accessibility
3. **Implement user accounts** - For personalization
4. **Prepare for app store** - Screenshots, descriptions, etc.

---

## 📚 Learning Resources

### Flutter Development
- [Flutter Cookbook](https://docs.flutter.dev/cookbook)
- [Dart Language Tour](https://dart.dev/guides/language/language-tour)
- [Flutter Widget Catalog](https://docs.flutter.dev/development/ui/widgets)

### Islamic App Development
- [Islamic Design Principles](https://www.islamicdesign.org/)
- [Halal Certification Standards](https://www.halalfocus.net/)
- [Arabic Typography in Apps](https://arabictypography.com/)

### AI Integration
- [xAI Grok Documentation](https://docs.x.ai/)
- [Prompt Engineering Guide](https://www.promptingguide.ai/)
- [AI Ethics in Islamic Context](https://islamicai.org/)

---

## 🤝 Getting Help

### Quick Help
- **GitHub Issues**: Report bugs and request features
- **Discord Community**: Real-time chat with developers
- **Stack Overflow**: Technical Flutter questions

### Professional Support
- **Code Review**: Get expert feedback on your implementation
- **Custom Features**: Hire developers for specific requirements
- **Islamic Consultation**: Connect with Islamic scholars for accuracy

---

## 🎉 Success! What's Next?

Congratulations! You now have a working halal food verification app. Here's what you can do:

### Share Your Success
- **Screenshot**: Take a photo of your working app
- **Social Media**: Share your achievement with #HalalWise
- **Community**: Help others in the Discord/GitHub community

### Contribute Back
- **Bug Reports**: Found an issue? Report it!
- **Feature Ideas**: Have suggestions? Share them!
- **Code Contributions**: Improve the app for everyone

### Scale Your Impact
- **Local Community**: Share with your mosque/community
- **App Stores**: Publish for wider reach
- **Open Source**: Keep it free for the ummah

---

**🚀 Ready to make halal food verification accessible to everyone? Let's build something amazing together!**

**Need help?** Create an issue on GitHub or join our Discord community.
**Found this helpful?** Star the repository and share with others!
