#!/bin/bash

# HalalWise Web Setup Fix Script
echo "🔧 Fixing HalalWise Web Setup..."

# Create web directory structure
mkdir -p web/icons

# Create a simple SVG icon that can be used as placeholder
cat > web/icons/Icon-192.png << 'EOF'
# Placeholder for 192x192 icon
EOF

cat > web/icons/Icon-512.png << 'EOF'
# Placeholder for 512x512 icon
EOF

cat > web/icons/Icon-maskable-192.png << 'EOF'
# Placeholder for maskable 192x192 icon
EOF

cat > web/icons/Icon-maskable-512.png << 'EOF'
# Placeholder for maskable 512x512 icon
EOF

# Create a simple favicon
cat > web/favicon.png << 'EOF'
# Placeholder favicon
EOF

echo "✅ Web setup files created"

# Try to run flutter create to add web support properly
echo "🔧 Adding web support to Flutter project..."
flutter create . --platforms=web --overwrite

echo "✅ Web support added"

# Clean and get dependencies
echo "📦 Cleaning and getting dependencies..."
flutter clean
flutter pub get

echo "✅ Setup complete! Try running: flutter run -d chrome"
